#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建一个干净的模板，手动插入正确的联系人占位符
"""

import zipfile
import xml.etree.ElementTree as ET
import re
import os
import shutil
import tempfile

def create_clean_template():
    """创建一个干净的模板，手动插入联系人占位符"""
    print("🔧 创建干净的联系人模板")
    print("=" * 60)
    
    template_path = "../word_zc/template_15players_fixed.docx"
    clean_template_path = "../word_zc/template_15players_clean.docx"
    
    try:
        # 复制原模板
        shutil.copy2(template_path, clean_template_path)
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 解压模板
            with zipfile.ZipFile(clean_template_path, 'r') as zip_file:
                zip_file.extractall(temp_dir)
            
            # 读取document.xml
            doc_xml_path = os.path.join(temp_dir, 'word', 'document.xml')
            with open(doc_xml_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print("📄 查找联系人相关的位置...")
            
            # 查找联系人相关的段落
            # 根据之前的分析，联系人信息在这个位置：
            # 球队联系人: {{contactPerson}} 电话：{{contactPhone}}
            
            # 找到包含"球队联系人"的段落
            contact_paragraph_pattern = r'(<w:p[^>]*>.*?球队.*?联系人.*?</w:p>)'
            
            match = re.search(contact_paragraph_pattern, content, re.DOTALL)
            
            if match:
                print("   ✅ 找到联系人段落")
                old_paragraph = match.group(1)
                print(f"   原段落长度: {len(old_paragraph)} 字符")
                
                # 创建一个新的、干净的联系人段落
                new_paragraph = '''<w:p w14:paraId="50E95516" w14:textId="219A8B79" w:rsidR="008011D3" w:rsidRPr="008011D3" w:rsidRDefault="00000000" w:rsidP="008011D3">
    <w:r>
        <w:rPr>
            <w:rFonts w:hint="eastAsia"/>
        </w:rPr>
        <w:t>球队联系人: {{contactPerson}} 电话：{{contactPhone}}</w:t>
    </w:r>
</w:p>'''
                
                # 替换段落
                content = content.replace(old_paragraph, new_paragraph)
                print("   ✅ 替换为干净的联系人段落")
                
            else:
                print("   ❌ 未找到联系人段落，手动添加...")
                
                # 在表格后面添加联系人段落
                table_end_pattern = r'(</w:tbl>)'
                
                new_contact_section = '''</w:tbl>
<w:p w14:paraId="1DD0BBA4" w14:textId="77777777" w:rsidR="00913B81" w:rsidRDefault="00913B81">
    <w:pPr>
        <w:rPr>
            <w:b/>
            <w:bCs/>
            <w:sz w:val="48"/>
            <w:szCs w:val="48"/>
        </w:rPr>
    </w:pPr>
</w:p>
<w:p w14:paraId="50E95516" w14:textId="219A8B79" w:rsidR="008011D3" w:rsidRPr="008011D3" w:rsidRDefault="00000000" w:rsidP="008011D3">
    <w:r>
        <w:rPr>
            <w:rFonts w:hint="eastAsia"/>
        </w:rPr>
        <w:t>球队联系人: {{contactPerson}} 电话：{{contactPhone}}</w:t>
    </w:r>
</w:p>'''
                
                content = re.sub(table_end_pattern, new_contact_section, content)
                print("   ✅ 手动添加联系人段落")
            
            # 保存修复后的内容
            with open(doc_xml_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 重新打包
            with zipfile.ZipFile(clean_template_path, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_path = os.path.relpath(file_path, temp_dir)
                        zip_file.write(file_path, arc_path)
            
            print(f"✅ 创建干净模板: {clean_template_path}")
            
            # 验证模板
            return verify_clean_template(clean_template_path)
            
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return False

def verify_clean_template(template_path):
    """验证干净模板"""
    print(f"\n🔍 验证干净模板")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 检查占位符
                has_contact_person = '{{contactPerson}}' in content
                has_contact_phone = '{{contactPhone}}' in content
                
                print(f"📄 占位符检查:")
                print(f"   {{{{contactPerson}}}}: {'✅ 找到' if has_contact_person else '❌ 未找到'}")
                print(f"   {{{{contactPhone}}}}: {'✅ 找到' if has_contact_phone else '❌ 未找到'}")
                
                # 检查是否还有分割的占位符
                has_split_contact = 'contactPerson</w:t></w:r><w:proofErr' in content
                has_split_phone = 'contactPhone</w:t></w:r><w:proofErr' in content
                
                print(f"   分割的占位符: {'❌ 仍存在' if has_split_contact or has_split_phone else '✅ 已清理'}")
                
                if has_contact_person and has_contact_phone:
                    print("🎉 干净模板创建成功！")
                    
                    # 显示联系人相关内容
                    lines = content.split('\n')
                    for i, line in enumerate(lines):
                        if '{{contactPerson}}' in line or '{{contactPhone}}' in line:
                            print(f"   第{i+1}行: {line.strip()}")
                    
                    return True
                else:
                    print("❌ 干净模板创建失败")
                    return False
                
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def test_clean_template():
    """测试干净模板"""
    print(f"\n🧪 测试干净模板")
    print("=" * 60)
    
    clean_template_path = "../word_zc/template_15players_clean.docx"
    
    if not os.path.exists(clean_template_path):
        print("❌ 干净模板不存在")
        return False
    
    try:
        # 创建测试数据
        test_data = {
            "teamInfo": {
                "title": "干净模板测试报名表",
                "organizationName": "干净模板测试队",
                "teamLeader": "张三",
                "coach": "李四",
                "teamDoctor": "王五",
                "contactPerson": "赵六",
                "contactPhone": "13800138000"
            },
            "players": [
                {
                    "number": "10",
                    "name": "测试球员",
                    "photoPath": "java_word_photos/player1.png"
                }
            ],
            "config": {
                "templatePath": clean_template_path,
                "outputDir": "../word_zc/ai-football-generator/output",
                "photosDir": "java_word_photos"
            }
        }
        
        # 写入测试文件
        test_file = "test_clean_template.json"
        with open(test_file, 'w', encoding='utf-8') as f:
            import json
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建测试文件: {test_file}")
        print(f"📄 使用干净模板: {clean_template_path}")
        print(f"📄 测试数据:")
        print(f"   contactPerson: '{test_data['teamInfo']['contactPerson']}'")
        print(f"   contactPhone: '{test_data['teamInfo']['contactPhone']}'")
        
        # 运行Java程序
        import subprocess
        result = subprocess.run(
            ["java", "-cp", "target/word-generator.jar", "CommandLineMain", test_file],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=30,
            errors='ignore',
            cwd="../word_zc/ai-football-generator"
        )
        
        print(f"\n📊 Java返回码: {result.returncode}")
        
        if result.stderr:
            print(f"📝 Java日志:")
            for line in result.stderr.split('\n'):
                if 'INFO:Team info parsed:' in line:
                    print(f"   🔍 关键: {line}")
                    # 检查联系人信息是否正确解析
                    if '联系人=赵六' in line and '联系电话=13800138000' in line:
                        print("   ✅ 联系人信息解析正确！")
                    else:
                        print("   ⚠️ 联系人信息解析可能有问题")
        
        if result.returncode == 0:
            print("✅ Java程序运行成功")
            
            # 检查生成的文件
            output_dir = "../word_zc/ai-football-generator/output"
            if os.path.exists(output_dir):
                files = [f for f in os.listdir(output_dir) if f.endswith('.docx')]
                if files:
                    latest_file = max([os.path.join(output_dir, f) for f in files], 
                                    key=os.path.getmtime)
                    print(f"📄 生成文件: {latest_file}")
                    
                    # 检查生成文件的内容
                    return check_final_result(latest_file)
        else:
            print(f"❌ Java程序运行失败")
            if result.stderr:
                print("错误信息:")
                print(result.stderr[:500])
        
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists("test_clean_template.json"):
            os.remove("test_clean_template.json")

def check_final_result(file_path):
    """检查最终结果"""
    print(f"\n🔍 检查最终结果")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    # 检查联系人信息
                    has_contact_person = "赵六" in full_text
                    has_contact_phone = "13800138000" in full_text
                    has_placeholder_person = "{{contactPerson}}" in content
                    has_placeholder_phone = "{{contactPhone}}" in content
                    
                    print(f"📄 最终检查结果:")
                    print(f"   联系人'赵六': {'✅ 找到' if has_contact_person else '❌ 未找到'}")
                    print(f"   电话'13800138000': {'✅ 找到' if has_contact_phone else '❌ 未找到'}")
                    print(f"   占位符{{{{contactPerson}}}}: {'⚠️ 仍存在' if has_placeholder_person else '✅ 已替换'}")
                    print(f"   占位符{{{{contactPhone}}}}: {'⚠️ 仍存在' if has_placeholder_phone else '✅ 已替换'}")
                    
                    if has_contact_person and has_contact_phone and not has_placeholder_person and not has_placeholder_phone:
                        print("🎉 联系人信息问题完全解决！")
                        print("✅ 占位符被正确替换为实际数据")
                        
                        # 显示联系人相关的上下文
                        words = full_text.split()
                        for i, word in enumerate(words):
                            if "联系人" in word:
                                start = max(0, i-2)
                                end = min(len(words), i+8)
                                context = ' '.join(words[start:end])
                                print(f"   联系人上下文: {context}")
                                break
                        
                        return True
                    elif has_contact_person and has_contact_phone:
                        print("✅ 联系人信息显示正确，但占位符未完全替换")
                        return True
                    else:
                        print("❌ 联系人信息仍未正确显示")
                        
                        # 显示实际的联系人相关内容
                        if "联系人" in full_text:
                            words = full_text.split()
                            for i, word in enumerate(words):
                                if "联系人" in word:
                                    start = max(0, i-2)
                                    end = min(len(words), i+8)
                                    context = ' '.join(words[start:end])
                                    print(f"   实际内容: {context}")
                                    break
                        
                        return False
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return False
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 创建干净的联系人模板")
    print("=" * 70)
    
    # 1. 创建干净模板
    template_created = create_clean_template()
    
    # 2. 测试干净模板
    if template_created:
        test_result = test_clean_template()
        
        print(f"\n📊 最终结果")
        print("=" * 70)
        
        if test_result:
            print("🎉 联系人信息问题完全解决！")
            print("✅ 干净模板创建成功")
            print("✅ 联系人信息现在能够正确显示在Word文档中")
            print("💡 建议使用干净模板替换原模板")
            print(f"\n🔄 替换命令:")
            print(f"   cp ../word_zc/template_15players_clean.docx ../word_zc/template_15players_fixed.docx")
            print(f"\n🎯 用户现在可以:")
            print(f"   1. 在AI聊天中输入: '我是张三，电话13800138000'")
            print(f"   2. AI会自动提取并保存联系人信息")
            print(f"   3. 生成Word报名表时，联系人信息会自动填入")
            print(f"   4. 最终的Word文档包含完整的联系人信息")
        else:
            print("⚠️ 干净模板创建部分成功")
            print("💡 需要进一步调试")
    else:
        print("❌ 干净模板创建失败")
        print("💡 需要手动检查和修复")

if __name__ == "__main__":
    main()
