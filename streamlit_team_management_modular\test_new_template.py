#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用户最新修改的模板占位符
"""

import sys
import os
import json
import subprocess
import zipfile
import xml.etree.ElementTree as ET

def test_template_structure():
    """测试模板结构"""
    print("🔍 测试1: 检查最新修改的模板结构")
    print("=" * 60)
    
    template_path = "../word_zc/template_15players_fixed.docx"
    
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        return False
    
    try:
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                print("📄 检查占位符格式:")
                
                # 检查标准驼峰格式
                has_contact_person_camel = '{{contactPerson}}' in content
                has_contact_phone_camel = '{{contactPhone}}' in content
                
                # 检查空格格式
                has_contact_person_spaced = '{{contact Person}}' in content
                has_contact_phone_spaced = '{{contact Phone}}' in content
                
                print(f"   {{{{contactPerson}}}}: {'✅ 找到' if has_contact_person_camel else '❌ 未找到'}")
                print(f"   {{{{contactPhone}}}}: {'✅ 找到' if has_contact_phone_camel else '❌ 未找到'}")
                print(f"   {{{{contact Person}}}}: {'✅ 找到' if has_contact_person_spaced else '❌ 未找到'}")
                print(f"   {{{{contact Phone}}}}: {'✅ 找到' if has_contact_phone_spaced else '❌ 未找到'}")
                
                # 检查是否还有分割的占位符
                has_split_placeholders = any([
                    'contactPerson</w:t></w:r>' in content,
                    'contactPhone</w:t></w:r>' in content,
                    'contact Person</w:t></w:r>' in content,
                    'contact Phone</w:t></w:r>' in content
                ])
                
                print(f"   分割的占位符: {'⚠️ 仍存在' if has_split_placeholders else '✅ 已清理'}")
                
                # 显示联系人相关的上下文
                print(f"\n📄 联系人相关上下文:")
                lines = content.split('\n')
                contact_lines = []
                for i, line in enumerate(lines):
                    if any(keyword in line for keyword in ['contact', 'Contact', '联系人', '电话']):
                        contact_lines.append(f"   第{i+1}行: {line.strip()}")
                
                for line in contact_lines[:3]:  # 显示前3行相关内容
                    print(line)
                
                # 检查所有占位符
                import re
                placeholders = re.findall(r'\{\{[^}]+\}\}', content)
                unique_placeholders = list(set(placeholders))
                
                print(f"\n📄 联系人相关占位符:")
                contact_placeholders = [p for p in unique_placeholders if 'contact' in p.lower()]
                for placeholder in sorted(contact_placeholders):
                    print(f"   • {placeholder}")
                
                # 返回是否找到任何联系人占位符
                return (has_contact_person_camel or has_contact_phone_camel or 
                       has_contact_person_spaced or has_contact_phone_spaced)
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def test_java_generation():
    """测试Java生成"""
    print("\n🔍 测试2: Java使用最新模板")
    print("=" * 60)
    
    try:
        # 创建测试数据
        test_data = {
            "teamInfo": {
                "title": "最新模板测试报名表",
                "organizationName": "最新模板测试队",
                "teamLeader": "张三",
                "coach": "李四",
                "teamDoctor": "王五",
                "contactPerson": "赵六",
                "contactPhone": "13800138000"
            },
            "players": [
                {
                    "number": "10",
                    "name": "测试球员",
                    "photoPath": "java_word_photos/player1.png"
                }
            ],
            "config": {
                "templatePath": "template_15players_fixed.docx",
                "outputDir": "output",
                "photosDir": "java_word_photos"
            }
        }
        
        # Java工作目录
        java_dir = "../word_zc/ai-football-generator"
        
        # 写入测试文件
        test_file = os.path.join(java_dir, "test_new.json")
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建测试文件")
        print(f"📄 测试数据:")
        print(f"   contactPerson: '{test_data['teamInfo']['contactPerson']}'")
        print(f"   contactPhone: '{test_data['teamInfo']['contactPhone']}'")
        
        # 运行Java程序
        print(f"\n🚀 运行Java程序...")
        result = subprocess.run(
            ["java", "-cp", "target/word-generator.jar", "CommandLineMain", "test_new.json"],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=30,
            errors='ignore',
            cwd=java_dir
        )
        
        print(f"📊 返回码: {result.returncode}")
        
        # 分析输出
        contact_parsed_correctly = False
        java_success = result.returncode == 0
        
        if result.stdout:
            print(f"\n📝 Java标准输出:")
            for line in result.stdout.split('\n'):
                if line.strip():
                    print(f"   {line}")
        
        if result.stderr:
            print(f"\n📝 Java错误输出:")
            for line in result.stderr.split('\n'):
                if line.strip():
                    print(f"   {line}")
                    
                    # 检查团队信息解析
                    if 'INFO:Team info parsed:' in line:
                        print(f"   🔍 关键日志: {line}")
                        if '联系人=赵六' in line and '联系电话=13800138000' in line:
                            print("   ✅ 联系人信息解析完全正确！")
                            contact_parsed_correctly = True
                        elif '联系人=' in line and '联系电话=' in line:
                            print("   ⚠️ 联系人字段存在但值可能为空")
        
        # 检查生成的文件
        generated_file = None
        if java_success:
            print("✅ Java程序运行成功")
            
            output_dir = os.path.join(java_dir, "output")
            if os.path.exists(output_dir):
                files = [f for f in os.listdir(output_dir) if f.endswith('.docx')]
                if files:
                    latest_file = max([os.path.join(output_dir, f) for f in files], 
                                    key=os.path.getmtime)
                    print(f"📄 生成文件: {os.path.basename(latest_file)}")
                    generated_file = latest_file
        else:
            print(f"❌ Java程序运行失败")
        
        return {
            'success': java_success,
            'contact_parsed': contact_parsed_correctly,
            'generated_file': generated_file
        }
        
    except Exception as e:
        print(f"❌ Java测试失败: {e}")
        return {'success': False, 'contact_parsed': False, 'generated_file': None}
    finally:
        # 清理测试文件
        test_file = os.path.join("../word_zc/ai-football-generator", "test_new.json")
        if os.path.exists(test_file):
            os.remove(test_file)

def test_python_generation():
    """测试Python生成"""
    print("\n🔍 测试3: Python使用最新模板")
    print("=" * 60)
    
    try:
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 测试数据
        team_data = {
            'name': 'Python最新模板测试队',
            'leader': '张三',
            'coach': '李四',
            'doctor': '王五',
            'contact_person': '赵六',
            'contact_phone': '13800138000'
        }
        
        players_data = [
            {
                'name': '测试球员',
                'jersey_number': '10',
                'photo': 'word_zc/ai-football-generator/java_word_photos/player1.png'
            }
        ]
        
        print(f"📄 测试数据:")
        print(f"   contact_person: '{team_data['contact_person']}'")
        print(f"   contact_phone: '{team_data['contact_phone']}'")
        
        # 获取配置
        paths = app_settings.word_generator.get_absolute_paths("new_test", app_settings.paths)
        
        # 创建服务
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        # 检查数据准备
        json_data = word_service._prepare_json_data(team_data, players_data)
        team_info = json_data['teamInfo']
        
        print(f"\n📄 Python准备的数据:")
        print(f"   contactPerson: '{team_info.get('contactPerson', 'MISSING')}'")
        print(f"   contactPhone: '{team_info.get('contactPhone', 'MISSING')}'")
        
        # 生成Word
        print(f"\n🚀 运行Python生成...")
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print(f"✅ 生成成功: {os.path.basename(result['file_path'])}")
            return {
                'success': True,
                'generated_file': result['file_path']
            }
        else:
            print(f"❌ 生成失败: {result['message']}")
            return {'success': False, 'generated_file': None}
            
    except Exception as e:
        print(f"❌ Python测试失败: {e}")
        return {'success': False, 'generated_file': None}

def check_word_content(file_path, test_name):
    """检查Word内容"""
    print(f"\n🔍 检查{test_name}生成的Word内容")
    print("=" * 60)
    
    if not file_path or not os.path.exists(file_path):
        print("❌ 文件不存在")
        return False
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    # 检查联系人信息
                    has_contact_person = "赵六" in full_text
                    has_contact_phone = "13800138000" in full_text
                    
                    # 检查各种占位符格式是否还存在
                    has_placeholder_camel_person = "{{contactPerson}}" in content
                    has_placeholder_camel_phone = "{{contactPhone}}" in content
                    has_placeholder_spaced_person = "{{contact Person}}" in content
                    has_placeholder_spaced_phone = "{{contact Phone}}" in content
                    
                    print(f"📄 内容检查:")
                    print(f"   联系人'赵六': {'✅ 找到' if has_contact_person else '❌ 未找到'}")
                    print(f"   电话'13800138000': {'✅ 找到' if has_contact_phone else '❌ 未找到'}")
                    
                    print(f"\n📄 占位符检查:")
                    print(f"   {{{{contactPerson}}}}: {'⚠️ 仍存在' if has_placeholder_camel_person else '✅ 已替换'}")
                    print(f"   {{{{contactPhone}}}}: {'⚠️ 仍存在' if has_placeholder_camel_phone else '✅ 已替换'}")
                    print(f"   {{{{contact Person}}}}: {'⚠️ 仍存在' if has_placeholder_spaced_person else '✅ 已替换'}")
                    print(f"   {{{{contact Phone}}}}: {'⚠️ 仍存在' if has_placeholder_spaced_phone else '✅ 已替换'}")
                    
                    # 显示联系人上下文
                    print(f"\n📄 联系人相关内容:")
                    words = full_text.split()
                    contact_found = False
                    for i, word in enumerate(words):
                        if "联系人" in word:
                            start = max(0, i-3)
                            end = min(len(words), i+15)
                            context = ' '.join(words[start:end])
                            print(f"   联系人上下文: {context}")
                            contact_found = True
                            break
                    
                    if not contact_found:
                        print("   ❌ 未找到联系人相关内容")
                    
                    return {
                        'has_contact_person': has_contact_person,
                        'has_contact_phone': has_contact_phone,
                        'all_placeholders_replaced': not any([
                            has_placeholder_camel_person, has_placeholder_camel_phone,
                            has_placeholder_spaced_person, has_placeholder_spaced_phone
                        ]),
                        'contact_found': contact_found
                    }
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return False
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 测试用户最新修改的模板")
    print("=" * 70)
    print("用户刚刚修改了占位符，现在进行验证测试")
    print("=" * 70)
    
    # 测试1: 模板结构
    template_ok = test_template_structure()
    
    # 测试2: Java生成
    java_result = test_java_generation()
    
    # 测试3: Python生成
    python_result = test_python_generation()
    
    # 检查生成的内容
    java_content = None
    python_content = None
    
    if java_result.get('generated_file'):
        java_content = check_word_content(java_result['generated_file'], "Java")
    
    if python_result.get('generated_file'):
        python_content = check_word_content(python_result['generated_file'], "Python")
    
    # 综合结果
    print(f"\n📊 测试结果汇总")
    print("=" * 70)
    
    print(f"🔍 模板结构: {'✅ 正常' if template_ok else '❌ 有问题'}")
    print(f"🔍 Java程序: {'✅ 正常' if java_result.get('success') else '❌ 有问题'}")
    print(f"🔍 Java联系人解析: {'✅ 正常' if java_result.get('contact_parsed') else '❌ 有问题'}")
    print(f"🔍 Python集成: {'✅ 正常' if python_result.get('success') else '❌ 有问题'}")
    
    if java_content:
        java_contact_ok = java_content.get('has_contact_person') and java_content.get('has_contact_phone')
        print(f"🔍 Java生成内容: {'✅ 联系人信息正确' if java_contact_ok else '❌ 联系人信息缺失'}")
    
    if python_content:
        python_contact_ok = python_content.get('has_contact_person') and python_content.get('has_contact_phone')
        print(f"🔍 Python生成内容: {'✅ 联系人信息正确' if python_contact_ok else '❌ 联系人信息缺失'}")
    
    # 最终结论
    print(f"\n🎯 最终结论:")
    
    all_perfect = (
        template_ok and 
        java_result.get('success') and 
        java_result.get('contact_parsed') and 
        python_result.get('success') and
        (java_content and java_content.get('has_contact_person') and java_content.get('has_contact_phone')) and
        (python_content and python_content.get('has_contact_person') and python_content.get('has_contact_phone'))
    )
    
    if all_perfect:
        print("🎉 模板修复完全成功！")
        print("✅ 模板占位符格式正确")
        print("✅ Java和Python都能正确处理联系人信息")
        print("✅ 生成的Word文档包含正确的联系人信息")
        print("✅ 联系人信息自动化流程完整可用")
        print("✅ 没有发现任何报错")
        
        print(f"\n🎯 用户现在可以正常使用:")
        print(f"   1. 在AI聊天中输入联系人信息")
        print(f"   2. 系统自动提取并保存")
        print(f"   3. 生成Word时自动填入联系人信息")
        print(f"   4. 联系人信息正确显示在Word文档中")
        print(f"\n🎉 联系人信息问题已完全解决！")
        
    elif (java_result.get('success') and python_result.get('success')):
        print("✅ 程序运行成功，但需要检查内容:")
        
        if not java_result.get('contact_parsed'):
            print("   ⚠️ Java联系人信息解析可能有问题")
        
        if java_content and not (java_content.get('has_contact_person') and java_content.get('has_contact_phone')):
            print("   ⚠️ Java生成的Word文档联系人信息可能缺失")
        
        if python_content and not (python_content.get('has_contact_person') and python_content.get('has_contact_phone')):
            print("   ⚠️ Python生成的Word文档联系人信息可能缺失")
            
        print(f"\n💡 建议检查模板占位符格式是否完全正确")
        
    else:
        print("❌ 仍有问题需要解决:")
        
        if not template_ok:
            print("   ❌ 模板结构有问题")
        if not java_result.get('success'):
            print("   ❌ Java程序运行失败")
        if not python_result.get('success'):
            print("   ❌ Python程序运行失败")
            
        print(f"\n💡 建议重新检查模板占位符格式")

if __name__ == "__main__":
    main()
