#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试team_service返回的数据
"""

import json

def debug_team_service_data():
    """调试team_service返回的数据"""
    print("🔍 调试team_service返回的数据")
    print("=" * 60)
    
    try:
        import streamlit as st
        
        # 设置session state
        if 'user_id' not in st.session_state:
            st.session_state.user_id = 'user_e358df6703a9'
        
        from services.team_service import TeamService
        
        # 创建team_service
        team_service = TeamService()
        
        # 加载团队数据
        team_data = team_service.load_team_data_for_user('user_e358df6703a9', '新建项目201')
        
        if team_data:
            print("✅ 团队数据加载成功")
            
            print(f"📄 团队数据结构:")
            print(f"   数据类型: {type(team_data)}")
            print(f"   数据键: {list(team_data.keys()) if isinstance(team_data, dict) else 'N/A'}")
            
            # 检查联系人信息
            contact_person = team_data.get('contact_person', 'MISSING')
            contact_phone = team_data.get('contact_phone', 'MISSING')
            leader = team_data.get('leader', 'MISSING')
            coach = team_data.get('coach', 'MISSING')
            doctor = team_data.get('doctor', 'MISSING')
            
            print(f"\n📄 联系人相关字段:")
            print(f"   contact_person: '{contact_person}'")
            print(f"   contact_phone: '{contact_phone}'")
            print(f"   leader: '{leader}'")
            print(f"   coach: '{coach}'")
            print(f"   doctor: '{doctor}'")
            
            # 显示完整数据
            print(f"\n📄 完整团队数据:")
            print(json.dumps(team_data, ensure_ascii=False, indent=2))
            
            # 检查是否有AI数据
            has_ai_data = any(key in team_data for key in ['ai_extracted_info', 'extracted_info'])
            print(f"\n📊 AI数据检查:")
            print(f"   包含AI数据: {'✅' if has_ai_data else '❌'}")
            
            if not has_ai_data:
                print(f"\n💡 问题分析:")
                print(f"   team_service.load_team_data_for_user返回的是原始团队数据")
                print(f"   没有包含AI提取的联系人信息")
                print(f"   这就是为什么Word文档中联系人信息为空的原因")
                
                print(f"\n🔧 解决方案:")
                print(f"   需要修改_auto_generate_word_document方法")
                print(f"   使用AI数据来构建team_data，而不是原始团队数据")
            
            return team_data
            
        else:
            print("❌ 团队数据加载失败")
            return None
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def compare_with_ai_data():
    """与AI数据进行对比"""
    print(f"\n🔍 与AI数据进行对比")
    print("=" * 60)
    
    try:
        # 加载AI数据
        ai_file = "data/user_e358df6703a9/enhanced_ai_data/新建项目201_ai_data.json"
        with open(ai_file, 'r', encoding='utf-8') as f:
            ai_data = json.load(f)
        
        extracted_info = ai_data.get('extracted_info', {})
        basic_info = extracted_info.get('basic_info', {})
        
        print(f"📄 AI数据中的联系人信息:")
        print(f"   contact_person: '{basic_info.get('contact_person', 'MISSING')}'")
        print(f"   contact_phone: '{basic_info.get('contact_phone', 'MISSING')}'")
        print(f"   leader_name: '{basic_info.get('leader_name', 'MISSING')}'")
        
        # 加载原始团队数据
        team_file = "data/user_e358df6703a9/teams/新建项目201.json"
        with open(team_file, 'r', encoding='utf-8') as f:
            team_data = json.load(f)
        
        print(f"\n📄 原始团队数据中的联系人信息:")
        print(f"   contact_person: '{team_data.get('contact_person', 'MISSING')}'")
        print(f"   contact_phone: '{team_data.get('contact_phone', 'MISSING')}'")
        print(f"   leader: '{team_data.get('leader', 'MISSING')}'")
        
        print(f"\n📊 对比结果:")
        ai_has_contact = bool(basic_info.get('contact_person')) and bool(basic_info.get('contact_phone'))
        team_has_contact = bool(team_data.get('contact_person')) and bool(team_data.get('contact_phone'))
        
        print(f"   AI数据有联系人信息: {'✅' if ai_has_contact else '❌'}")
        print(f"   原始团队数据有联系人信息: {'✅' if team_has_contact else '❌'}")
        
        if ai_has_contact and not team_has_contact:
            print(f"\n🎯 结论:")
            print(f"   AI数据包含联系人信息，但原始团队数据不包含")
            print(f"   这就是问题的根源！")
            
            print(f"\n🔧 解决方案:")
            print(f"   需要在_auto_generate_word_document中:")
            print(f"   1. 加载AI数据")
            print(f"   2. 将AI提取的联系人信息合并到team_data中")
            print(f"   3. 然后再调用word_service.generate_report")
        
        return ai_has_contact, team_has_contact
        
    except Exception as e:
        print(f"❌ 对比失败: {e}")
        return False, False

def main():
    """主函数"""
    print("🎯 调试team_service返回的数据")
    print("=" * 70)
    
    # 调试team_service数据
    team_data = debug_team_service_data()
    
    # 与AI数据对比
    ai_has_contact, team_has_contact = compare_with_ai_data()
    
    print(f"\n📊 最终分析")
    print("=" * 70)
    
    if team_data:
        print("✅ team_service能够加载团队数据")
    else:
        print("❌ team_service无法加载团队数据")
    
    if ai_has_contact and not team_has_contact:
        print("🎯 问题确认:")
        print("   ✅ AI数据包含联系人信息")
        print("   ❌ 原始团队数据不包含联系人信息")
        print("   ❌ _auto_generate_word_document使用的是原始团队数据")
        
        print(f"\n💡 这就是联系人信息缺失的根本原因！")
        
        print(f"\n🔧 需要修复:")
        print(f"   修改_auto_generate_word_document方法")
        print(f"   在生成Word前，将AI数据中的联系人信息合并到team_data中")

if __name__ == "__main__":
    main()
