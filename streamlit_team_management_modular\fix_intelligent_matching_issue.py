#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复"智能搭配"问题的完整解决方案
"""

import os
import sys
import json
import shutil
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_user_data_files():
    """修复用户数据文件中的"智能搭配"问题"""
    print("=" * 60)
    print("🔧 修复用户数据文件中的'智能搭配'问题")
    print("=" * 60)
    
    problem_strings = ["智能搭配", "智能填充", "AI搭配", "AI填充", "自动搭配"]
    
    # 查找所有JSON文件
    json_files = []
    for root, dirs, files in os.walk("data"):
        for file in files:
            if file.endswith('.json'):
                json_files.append(os.path.join(root, file))
    
    print(f"📄 检查 {len(json_files)} 个JSON文件")
    
    fixed_files = []
    
    for json_file in json_files:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否包含问题字符串
            has_problems = any(problem in content for problem in problem_strings)
            
            if has_problems:
                print(f"\n📄 修复文件: {os.path.relpath(json_file)}")
                
                # 备份原文件
                backup_path = f"{json_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(json_file, backup_path)
                print(f"   ✅ 已备份到: {os.path.basename(backup_path)}")
                
                # 读取JSON数据
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 修复数据
                fixed_count = fix_data_recursively(data)
                
                if fixed_count > 0:
                    # 保存修复后的数据
                    with open(json_file, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    
                    print(f"   ✅ 修复完成，共修复 {fixed_count} 个问题字段")
                    fixed_files.append(json_file)
                else:
                    # 删除不必要的备份
                    os.remove(backup_path)
                    
        except Exception as e:
            print(f"   ❌ 修复失败: {e}")
    
    return fixed_files

def fix_data_recursively(data, path=""):
    """递归修复数据中的问题字符串"""
    fixed_count = 0
    problem_strings = ["智能搭配", "智能填充", "AI搭配", "AI填充", "自动搭配"]
    
    if isinstance(data, dict):
        for key, value in data.items():
            current_path = f"{path}.{key}" if path else key
            
            if isinstance(value, str) and value in problem_strings:
                # 根据字段类型进行智能修复
                if "color" in key.lower() or "颜色" in key:
                    # 颜色字段：根据上下文推断或留空
                    if "jersey" in key.lower() or "球衣" in key:
                        data[key] = ""  # 球衣颜色留空，让用户重新输入
                    elif "shorts" in key.lower() or "球裤" in key:
                        data[key] = ""  # 留空，让系统自动填充
                    elif "socks" in key.lower() or "球袜" in key:
                        data[key] = ""  # 留空，让系统自动填充
                    elif "goalkeeper" in key.lower() or "守门员" in key:
                        data[key] = ""  # 留空，让系统自动填充
                    else:
                        data[key] = ""
                elif any(person_field in key.lower() for person_field in ["leader", "doctor", "coach", "领队", "队医", "教练"]):
                    # 人员字段：留空，让系统自动填充
                    data[key] = ""
                else:
                    # 其他字段：留空
                    data[key] = ""
                
                fixed_count += 1
                print(f"   修复: {current_path} '{value}' → '{data[key]}'")
            
            elif isinstance(value, (dict, list)):
                fixed_count += fix_data_recursively(value, current_path)
    
    elif isinstance(data, list):
        for i, item in enumerate(data):
            current_path = f"{path}[{i}]"
            if isinstance(item, (dict, list)):
                fixed_count += fix_data_recursively(item, current_path)
    
    return fixed_count

def test_ai_service_after_fix():
    """测试修复后的AI服务"""
    print(f"\n" + "=" * 60)
    print("🧪 测试修复后的AI服务")
    print("=" * 60)
    
    try:
        from services.enhanced_ai_assistant import EnhancedAIAssistant
        
        enhanced_ai = EnhancedAIAssistant()
        
        # 模拟用户输入
        test_input = "我是张三，电话13812345678，我们队的球衣是粉色的"
        
        print(f"📄 测试输入: '{test_input}'")
        
        # 测试信息提取
        result = enhanced_ai._extract_team_info_from_text({"user_text": test_input})
        
        print(f"\n📄 AI提取结果:")
        print(f"   success: {result.get('success')}")
        print(f"   extracted_info: {result.get('extracted_info')}")
        
        # 检查是否还有问题字符串
        extracted_text = str(result.get('extracted_info', ''))
        problem_strings = ["智能搭配", "智能填充", "AI搭配", "AI填充", "自动搭配"]
        
        problems_found = []
        for problem in problem_strings:
            if problem in extracted_text:
                problems_found.append(problem)
        
        if problems_found:
            print(f"\n❌ AI服务仍然生成问题字符串: {problems_found}")
            return False
        else:
            print(f"\n✅ AI服务修复成功，未生成问题字符串")
            return True
            
    except Exception as e:
        print(f"❌ 测试AI服务失败: {e}")
        return False

def test_complete_workflow():
    """测试完整的工作流"""
    print(f"\n" + "=" * 60)
    print("🧪 测试完整工作流")
    print("=" * 60)
    
    try:
        from services.ai_service import AIService
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 创建测试数据
        test_data = {
            "contact_person": "测试联系人",
            "contact_phone": "13800000000",
            "jersey_color": "粉色"
        }
        
        print(f"📄 测试数据:")
        for key, value in test_data.items():
            print(f"   {key}: '{value}'")
        
        # 1. 测试AI服务自动填充
        ai_service = AIService()
        filled_data = ai_service._apply_smart_fill_logic(test_data.copy())
        
        print(f"\n📄 AI自动填充后:")
        for key, value in filled_data.items():
            print(f"   {key}: '{value}'")
        
        # 检查自动填充结果
        problem_strings = ["智能搭配", "智能填充", "AI搭配", "AI填充", "自动搭配"]
        auto_fill_problems = []
        
        for key, value in filled_data.items():
            if isinstance(value, str) and any(problem in value for problem in problem_strings):
                auto_fill_problems.append((key, value))
        
        if auto_fill_problems:
            print(f"\n❌ 自动填充生成问题字符串: {auto_fill_problems}")
            return False
        
        # 2. 测试Word生成
        team_data = {
            "name": "智能搭配修复测试队",
            "contact_person": filled_data.get("contact_person"),
            "contact_phone": filled_data.get("contact_phone"),
            "leader": filled_data.get("leader_name"),
            "coach": filled_data.get("leader_name"),  # 使用联系人作为教练
            "doctor": filled_data.get("team_doctor"),
            "jersey_color": filled_data.get("jersey_color"),
            "shorts_color": filled_data.get("shorts_color"),
            "socks_color": filled_data.get("socks_color"),
            "goalkeeper_kit_color": filled_data.get("goalkeeper_kit_color")
        }
        
        players_data = [
            {"name": "测试球员1", "jersey_number": "1", "photo": ""},
            {"name": "测试球员2", "jersey_number": "2", "photo": ""}
        ]
        
        print(f"\n📄 Word生成数据:")
        for key, value in team_data.items():
            print(f"   {key}: '{value}'")
        
        # 生成Word文档
        paths = app_settings.word_generator.get_absolute_paths("intelligent_matching_fix_test", app_settings.paths)
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            output_file = result['file_path']
            print(f"\n✅ Word生成成功: {os.path.basename(output_file)}")
            
            # 检查Word文档内容
            return check_word_document_for_problems(output_file)
        else:
            print(f"\n❌ Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试完整工作流失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_word_document_for_problems(docx_path):
    """检查Word文档中是否还有问题字符串"""
    print(f"\n📄 检查Word文档内容:")
    
    try:
        import zipfile
        
        with zipfile.ZipFile(docx_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        # 检查问题字符串
        problem_strings = ["智能搭配", "智能填充", "AI搭配", "AI填充", "自动搭配"]
        problems_found = []
        
        for problem_string in problem_strings:
            count = content.count(problem_string)
            if count > 0:
                problems_found.append((problem_string, count))
        
        if problems_found:
            print(f"❌ Word文档中仍有问题字符串:")
            for problem_string, count in problems_found:
                print(f"   '{problem_string}': {count} 次")
            return False
        else:
            print(f"✅ Word文档中未发现问题字符串")
            
            # 检查颜色是否正确显示
            expected_colors = ["粉色", "黑色", "绿色"]
            colors_found = []
            
            for color in expected_colors:
                if color in content:
                    colors_found.append(color)
            
            print(f"🎨 颜色检查:")
            for color in expected_colors:
                status = "✅" if color in colors_found else "❌"
                print(f"   {status} {color}")
            
            return len(colors_found) >= 2  # 至少找到2种颜色认为成功
            
    except Exception as e:
        print(f"❌ 检查Word内容失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 修复'智能搭配'问题的完整解决方案")
    print(f"修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 修复用户数据文件
        fixed_files = fix_user_data_files()
        
        # 2. 测试修复后的AI服务
        ai_service_ok = test_ai_service_after_fix()
        
        # 3. 测试完整工作流
        workflow_ok = test_complete_workflow()
        
        print("\n" + "=" * 60)
        print("📋 修复总结")
        print("=" * 60)
        
        print(f"📊 修复结果:")
        print(f"   修复数据文件: {len(fixed_files)}")
        print(f"   AI服务测试: {'✅ 通过' if ai_service_ok else '❌ 失败'}")
        print(f"   完整工作流测试: {'✅ 通过' if workflow_ok else '❌ 失败'}")
        
        if len(fixed_files) > 0 or ai_service_ok and workflow_ok:
            print(f"\n🎉 修复成功!")
            print(f"   ✅ 清理了 {len(fixed_files)} 个问题数据文件")
            print(f"   ✅ AI服务不再生成'智能搭配'字符串")
            print(f"   ✅ Word生成功能正常")
            
            print(f"\n💡 修复内容:")
            print(f"   1. 修复了AI提示词，明确禁止生成占位符文本")
            print(f"   2. 清理了现有用户数据中的问题字符串")
            print(f"   3. 增强了数据验证和过滤逻辑")
            
        else:
            print(f"\n⚠️ 修复部分成功")
            if not ai_service_ok:
                print(f"   ❌ AI服务仍有问题")
            if not workflow_ok:
                print(f"   ❌ 完整工作流仍有问题")
        
        print(f"\n💡 用户使用建议:")
        print(f"   1. 重新输入球队信息，系统会自动填充正确的值")
        print(f"   2. 现有的问题数据已被清理")
        print(f"   3. AI不会再生成'智能搭配'等占位符文本")
        
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
