#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试天依369用户的Word生成问题
"""

import os
import sys
import json
import zipfile
import re
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_tianyi369_data():
    """分析天依369的数据"""
    print("=" * 60)
    print("🔍 分析天依369的数据")
    print("=" * 60)
    
    user_data_file = "data/user_c61aa17e3868/enhanced_ai_data/天依369_ai_data.json"
    
    if not os.path.exists(user_data_file):
        print(f"❌ 数据文件不存在: {user_data_file}")
        return None
    
    with open(user_data_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    extracted_info = data.get("extracted_info", {})
    basic_info = extracted_info.get("basic_info", {})
    kit_colors = extracted_info.get("kit_colors", {})
    additional_info = extracted_info.get("additional_info", {})
    
    print(f"📄 原始数据:")
    print(f"   basic_info: {basic_info}")
    print(f"   kit_colors: {kit_colors}")
    print(f"   additional_info: {additional_info}")
    
    # 检查空字段
    empty_fields = []
    all_fields = {
        **basic_info,
        **kit_colors,
        **additional_info
    }
    
    for key, value in all_fields.items():
        if not value or value == "":
            empty_fields.append(key)
    
    if empty_fields:
        print(f"\n❌ 发现空字段: {empty_fields}")
    else:
        print(f"\n✅ 所有字段都有值")
    
    return data

def test_word_generation_with_empty_fields():
    """测试包含空字段的Word生成"""
    print(f"\n" + "=" * 60)
    print("🧪 测试包含空字段的Word生成")
    print("=" * 60)
    
    try:
        # 读取天依369的数据
        user_data_file = "data/user_c61aa17e3868/enhanced_ai_data/天依369_ai_data.json"
        
        with open(user_data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        extracted_info = data.get("extracted_info", {})
        basic_info = extracted_info.get("basic_info", {})
        kit_colors = extracted_info.get("kit_colors", {})
        additional_info = extracted_info.get("additional_info", {})
        
        # 准备Word生成数据（模拟工作流的处理）
        team_data = {
            "name": basic_info.get("team_name", "天依369"),
            "contact_person": basic_info.get("contact_person"),
            "contact_phone": basic_info.get("contact_phone"),
            "leader": basic_info.get("leader_name"),
            "coach": additional_info.get("coach_name", basic_info.get("leader_name")),
            "doctor": basic_info.get("team_doctor"),
            "jersey_color": kit_colors.get("jersey_color"),
            "shorts_color": kit_colors.get("shorts_color"),
            "socks_color": kit_colors.get("socks_color"),
            "goalkeeper_kit_color": kit_colors.get("goalkeeper_kit_color")
        }
        
        players_data = [
            {"name": "张三", "jersey_number": "1", "photo": ""}
        ]
        
        print(f"📄 准备的team_data:")
        for key, value in team_data.items():
            status = "❌ 空" if not value else "✅ 有值"
            print(f"   {key}: '{value}' {status}")
        
        # 生成Word文档
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        paths = app_settings.word_generator.get_absolute_paths("tianyi369_debug", app_settings.paths)
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        # 检查WordGeneratorService准备的JSON数据
        json_data = word_service._prepare_json_data(team_data, players_data)
        
        print(f"\n📄 WordGeneratorService准备的JSON数据:")
        team_info = json_data.get("teamInfo", {})
        for key, value in team_info.items():
            status = "❌ 空" if not value else "✅ 有值"
            print(f"   {key}: '{value}' {status}")
        
        # 生成Word文档
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            output_file = result['file_path']
            print(f"\n✅ Word生成成功: {os.path.basename(output_file)}")
            
            # 分析生成的文档
            return analyze_generated_document_for_empty_fields(output_file, team_info)
        else:
            print(f"\n❌ Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_generated_document_for_empty_fields(docx_path, expected_data):
    """分析生成的文档中的空字段处理"""
    print(f"\n📄 分析生成的文档中的空字段处理:")
    
    try:
        with zipfile.ZipFile(docx_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        # 检查期望的数据是否出现在文档中
        print(f"\n📊 数据出现情况:")
        missing_data = []
        empty_data = []
        
        for key, value in expected_data.items():
            if not value or value == "":
                empty_data.append(key)
                print(f"   ⚠️ {key}: 空值")
            elif str(value) in content:
                print(f"   ✅ {key}: '{value}' 已出现")
            else:
                print(f"   ❌ {key}: '{value}' 未出现")
                missing_data.append((key, value))
        
        # 提取所有文本内容
        text_pattern = r'<w:t[^>]*>([^<]*)</w:t>'
        texts = re.findall(text_pattern, content)
        
        # 过滤有意义的文本
        meaningful_texts = []
        for text in texts:
            clean_text = text.strip()
            if clean_text and len(clean_text) > 0:
                meaningful_texts.append(clean_text)
        
        print(f"\n📊 文档中的实际内容（前30个）:")
        for i, text in enumerate(meaningful_texts[:30]):
            print(f"   {i+1}. '{text}'")
        
        # 检查是否有空的表格单元格
        print(f"\n📊 空字段在文档中的表现:")
        
        # 查找可能的空单元格模式
        empty_cell_indicators = ["", " ", "　", "null", "None", "undefined"]
        
        for indicator in empty_cell_indicators:
            if indicator in content:
                print(f"   ⚠️ 发现可能的空值指示符: '{indicator}'")
        
        # 检查表格结构
        cell_pattern = r'<w:tc[^>]*>(.*?)</w:tc>'
        cells = re.findall(cell_pattern, content, re.DOTALL)
        
        empty_cells = 0
        for cell in cells:
            cell_texts = re.findall(text_pattern, cell)
            cell_content = ''.join(cell_texts).strip()
            
            if not cell_content:
                empty_cells += 1
        
        print(f"   📊 总单元格数: {len(cells)}")
        print(f"   📊 空单元格数: {empty_cells}")
        
        return {
            'empty_data_count': len(empty_data),
            'missing_data_count': len(missing_data),
            'empty_cells_count': empty_cells,
            'total_cells_count': len(cells)
        }
        
    except Exception as e:
        print(f"❌ 分析文档失败: {e}")
        return None

def test_with_filled_data():
    """测试用填充数据替换空字段"""
    print(f"\n" + "=" * 60)
    print("🧪 测试用填充数据替换空字段")
    print("=" * 60)
    
    try:
        # 手动构造完整的数据
        team_data = {
            "name": "天依369",
            "contact_person": "赵六",
            "contact_phone": "18454432036",
            "leader": "赵六",  # 填充空字段
            "coach": "赵六",   # 填充空字段
            "doctor": "赵六",  # 填充空字段
            "jersey_color": "粉色",
            "shorts_color": "黑色",
            "socks_color": "粉色",  # 填充空字段
            "goalkeeper_kit_color": "绿色"
        }
        
        players_data = [
            {"name": "张三", "jersey_number": "1", "photo": ""}
        ]
        
        print(f"📄 填充后的数据:")
        for key, value in team_data.items():
            print(f"   {key}: '{value}'")
        
        # 生成Word文档
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        paths = app_settings.word_generator.get_absolute_paths("tianyi369_filled", app_settings.paths)
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            output_file = result['file_path']
            print(f"\n✅ 填充数据Word生成成功: {os.path.basename(output_file)}")
            
            # 简单检查生成的文档
            with zipfile.ZipFile(output_file, 'r') as zip_file:
                with zip_file.open('word/document.xml') as xml_file:
                    content = xml_file.read().decode('utf-8')
            
            keywords = ["天依369", "赵六", "粉色", "黑色", "18454432036"]
            found_count = 0
            
            for keyword in keywords:
                if keyword in content:
                    found_count += 1
                    print(f"   ✅ 找到: '{keyword}'")
                else:
                    print(f"   ❌ 未找到: '{keyword}'")
            
            return found_count == len(keywords)
        else:
            print(f"\n❌ 填充数据Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 填充数据测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 调试天依369用户的Word生成问题")
    print(f"调试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 分析天依369的数据
        data = analyze_tianyi369_data()
        
        # 2. 测试包含空字段的Word生成
        empty_result = test_word_generation_with_empty_fields()
        
        # 3. 测试用填充数据替换空字段
        filled_result = test_with_filled_data()
        
        print("\n" + "=" * 60)
        print("📋 调试总结")
        print("=" * 60)
        
        print(f"📊 测试结果:")
        print(f"   空字段Word生成: {'✅ 成功' if empty_result else '❌ 失败'}")
        print(f"   填充数据Word生成: {'✅ 成功' if filled_result else '❌ 失败'}")
        
        if not empty_result and filled_result:
            print(f"\n🎯 问题诊断:")
            print(f"   空字段导致Word文档显示空白")
            print(f"   填充数据后Word文档正常显示")
            
            print(f"\n💡 解决方案:")
            print(f"   1. 在数据处理阶段自动填充空字段")
            print(f"   2. 使用默认值替换空字段")
            print(f"   3. 提示用户补充缺失信息")
            print(f"   4. 在Word生成前进行数据验证和填充")
            
        elif empty_result and filled_result:
            print(f"\n✅ 系统正常工作")
            print(f"   空字段和填充数据都能正确处理")
            
        else:
            print(f"\n❌ 系统存在其他问题")
            print(f"   需要进一步调查Word生成机制")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
