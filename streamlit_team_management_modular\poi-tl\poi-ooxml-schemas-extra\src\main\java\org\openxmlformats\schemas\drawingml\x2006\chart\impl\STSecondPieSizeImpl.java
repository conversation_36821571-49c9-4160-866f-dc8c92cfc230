/*
 * XML Type:  ST_SecondPieSize
 * Namespace: http://schemas.openxmlformats.org/drawingml/2006/chart
 * Java type: org.openxmlformats.schemas.drawingml.x2006.chart.STSecondPieSize
 *
 * Automatically generated - do not modify.
 */
package org.openxmlformats.schemas.drawingml.x2006.chart.impl;

import javax.xml.namespace.QName;
import org.apache.xmlbeans.QNameSet;

/**
 * An XML ST_SecondPieSize(@http://schemas.openxmlformats.org/drawingml/2006/chart).
 *
 * This is a union type. Instances are of one of the following types:
 *     org.openxmlformats.schemas.drawingml.x2006.chart.STSecondPieSizePercent
 *     org.openxmlformats.schemas.drawingml.x2006.chart.STSecondPieSizeUShort
 */
public class STSecondPieSizeImpl extends org.apache.xmlbeans.impl.values.XmlUnionImpl implements org.openxmlformats.schemas.drawingml.x2006.chart.STSecondPieSize, org.openxmlformats.schemas.drawingml.x2006.chart.STSecondPieSizePercent, org.openxmlformats.schemas.drawingml.x2006.chart.STSecondPieSizeUShort {
    private static final long serialVersionUID = 1L;

    public STSecondPieSizeImpl(org.apache.xmlbeans.SchemaType sType) {
        super(sType, false);
    }

    protected STSecondPieSizeImpl(org.apache.xmlbeans.SchemaType sType, boolean b) {
        super(sType, b);
    }
}
