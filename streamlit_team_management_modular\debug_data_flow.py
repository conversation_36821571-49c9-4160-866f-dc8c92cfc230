#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试完整的数据流程
"""

import json
import os

def debug_complete_data_flow():
    """调试完整的数据流程"""
    print("🔍 调试完整的数据流程")
    print("=" * 60)
    
    # 1. 检查原始AI数据
    ai_data_file = "data/user_c8c5da29216f/enhanced_ai_data/天依002_ai_data.json"
    
    with open(ai_data_file, 'r', encoding='utf-8') as f:
        ai_chat_data = json.load(f)
    
    print("📄 步骤1: 原始AI数据")
    extracted_info = ai_chat_data.get('extracted_info', {})
    basic_info = extracted_info.get('basic_info', {})
    additional_info = extracted_info.get('additional_info', {})
    
    print(f"   contact_person: '{basic_info.get('contact_person', 'MISSING')}'")
    print(f"   contact_phone: '{basic_info.get('contact_phone', 'MISSING')}'")
    print(f"   leader_name: '{basic_info.get('leader_name', 'MISSING')}'")
    print(f"   coach_name: '{additional_info.get('coach_name', 'MISSING')}'")
    
    # 2. 模拟_convert_ai_chat_data_to_export_format
    print(f"\n📄 步骤2: _convert_ai_chat_data_to_export_format转换")
    
    # 模拟球员数据（简化）
    players = [
        {
            "id": "test-id",
            "name": "张三",
            "jersey_number": "1",
            "photo_info": {"exists": True, "filename": "test.jpg", "absolute_path": "/path/to/test.jpg"}
        }
    ]
    
    export_data = {
        "team_info": {
            "name": "天依002",
            "display_name": "天依002",
            "ai_extracted_info": ai_chat_data.get('extracted_info', {}),  # 这里是关键
            "created_at": ai_chat_data.get('created_at', ''),
            "updated_at": ai_chat_data.get('updated_at', '')
        },
        "players": players,
        "export_time": ai_chat_data.get('updated_at', ''),
        "data_source": "ai_chat_component"
    }
    
    print("   转换后的ai_extracted_info结构:")
    ai_extracted_info = export_data["team_info"]["ai_extracted_info"]
    print(f"   类型: {type(ai_extracted_info)}")
    print(f"   键: {list(ai_extracted_info.keys()) if isinstance(ai_extracted_info, dict) else 'N/A'}")
    
    if isinstance(ai_extracted_info, dict):
        basic_info_from_export = ai_extracted_info.get("basic_info", {})
        additional_info_from_export = ai_extracted_info.get("additional_info", {})
        
        print(f"   basic_info存在: {'✅' if basic_info_from_export else '❌'}")
        print(f"   additional_info存在: {'✅' if additional_info_from_export else '❌'}")
        
        if basic_info_from_export:
            print(f"   contact_person: '{basic_info_from_export.get('contact_person', 'MISSING')}'")
            print(f"   contact_phone: '{basic_info_from_export.get('contact_phone', 'MISSING')}'")
    
    # 3. 模拟fashion_workflow.py中的数据提取
    print(f"\n📄 步骤3: fashion_workflow.py中的数据提取")
    
    team_info = export_data.get("team_info", {})
    ai_extracted_info = team_info.get("ai_extracted_info", {})
    basic_info = ai_extracted_info.get("basic_info", {})
    additional_info = ai_extracted_info.get("additional_info", {})
    
    print(f"   team_info存在: {'✅' if team_info else '❌'}")
    print(f"   ai_extracted_info存在: {'✅' if ai_extracted_info else '❌'}")
    print(f"   basic_info存在: {'✅' if basic_info else '❌'}")
    print(f"   additional_info存在: {'✅' if additional_info else '❌'}")
    
    # 修复后的team_data构建
    team_data = {
        "name": "天依002",
        "leader": basic_info.get("leader_name", ""),
        "coach": additional_info.get("coach_name", ""),
        "doctor": basic_info.get("team_doctor", ""),
        "contact_person": basic_info.get("contact_person", ""),
        "contact_phone": basic_info.get("contact_phone", "")
    }
    
    print(f"\n📄 步骤4: 构建的team_data")
    for key, value in team_data.items():
        print(f"   {key}: '{value}'")
    
    # 4. 模拟WordGeneratorService数据准备
    print(f"\n📄 步骤5: WordGeneratorService数据准备")
    
    # 模拟_prepare_json_data逻辑
    team_name = team_data.get('name', '足球队')
    
    team_info_for_word = {
        "title": f"{team_name}报名表",
        "organizationName": team_name,
        "teamLeader": team_data.get('leader', ''),
        "coach": team_data.get('coach', ''),
        "teamDoctor": team_data.get('doctor', ''),
        "contactPerson": team_data.get('contact_person', team_data.get('leader', '')),
        "contactPhone": team_data.get('contact_phone', '')
    }
    
    print(f"   Word生成用的team_info:")
    for key, value in team_info_for_word.items():
        print(f"   {key}: '{value}'")
    
    # 检查关键字段
    contact_person_ok = bool(team_info_for_word.get('contactPerson'))
    contact_phone_ok = bool(team_info_for_word.get('contactPhone'))
    
    print(f"\n📊 关键字段检查:")
    print(f"   contactPerson有值: {'✅' if contact_person_ok else '❌'}")
    print(f"   contactPhone有值: {'✅' if contact_phone_ok else '❌'}")
    
    # 5. 测试实际Word生成
    print(f"\n📄 步骤6: 测试实际Word生成")
    
    try:
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        paths = app_settings.word_generator.get_absolute_paths("flow_test", app_settings.paths)
        
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        # 模拟球员数据
        players_data = [
            {
                'name': '张三',
                'jersey_number': '1',
                'photo': 'data/user_c8c5da29216f/photos/天依002/test.jpg'
            }
        ]
        
        # 生成Word文档
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print(f"✅ Word生成成功: {os.path.basename(result['file_path'])}")
            
            # 检查生成的文件内容
            return check_word_content(result['file_path'])
        else:
            print(f"❌ Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ Word生成测试失败: {e}")
        return False

def check_word_content(file_path):
    """检查Word内容"""
    print(f"\n🔍 检查Word内容")
    print("=" * 60)
    
    try:
        import zipfile
        import xml.etree.ElementTree as ET
        
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    # 检查联系人信息
                    has_contact_person = "赵六" in full_text
                    has_contact_phone = "18454432036" in full_text
                    
                    print(f"📄 Word内容检查:")
                    print(f"   联系人'赵六': {'✅ 找到' if has_contact_person else '❌ 未找到'}")
                    print(f"   电话'18454432036': {'✅ 找到' if has_contact_phone else '❌ 未找到'}")
                    
                    # 显示联系人上下文
                    if has_contact_person or has_contact_phone:
                        words = full_text.split()
                        for i, word in enumerate(words):
                            if "联系人" in word or "赵六" in word:
                                start = max(0, i-3)
                                end = min(len(words), i+15)
                                context = ' '.join(words[start:end])
                                print(f"   联系人上下文: {context}")
                                break
                    else:
                        # 显示联系人区域的内容
                        words = full_text.split()
                        for i, word in enumerate(words):
                            if "联系人" in word:
                                start = max(0, i-3)
                                end = min(len(words), i+15)
                                context = ' '.join(words[start:end])
                                print(f"   联系人区域: {context}")
                                break
                    
                    return has_contact_person and has_contact_phone
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return False
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 调试完整的数据流程")
    print("=" * 70)
    
    result = debug_complete_data_flow()
    
    print(f"\n📊 最终结果")
    print("=" * 70)
    
    if result:
        print("🎉 数据流程完全正常！")
        print("✅ 从AI数据到Word生成的完整流程工作正常")
        print("✅ 联系人信息正确传递和显示")
        
        print(f"\n💡 这说明:")
        print(f"   1. 数据转换逻辑是正确的")
        print(f"   2. Word生成服务是正常的")
        print(f"   3. 问题可能在Streamlit应用的实际运行环境")
        
        print(f"\n🎯 建议:")
        print(f"   1. 重启Streamlit应用")
        print(f"   2. 清除浏览器缓存")
        print(f"   3. 重新生成Word报名表")
        
    else:
        print("❌ 数据流程仍有问题")
        print("💡 需要进一步调试具体环节")

if __name__ == "__main__":
    main()
