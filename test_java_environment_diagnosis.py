#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java环境和JAR文件诊断测试
Java Environment and JAR File Diagnosis Test

专门诊断Java环境和CommandLineMain类的问题
"""

import os
import sys
import json
import subprocess
import tempfile
from datetime import datetime

# 添加项目路径
sys.path.append('streamlit_team_management_modular')

class JavaEnvironmentDiagnostic:
    """Java环境诊断器"""
    
    def __init__(self):
        self.test_results = {}
        
    def run_java_diagnosis(self):
        """运行Java环境诊断"""
        
        print("🔍 Java环境和JAR文件诊断测试")
        print("=" * 80)
        print(f"⏰ 诊断开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 诊断步骤
        diagnosis_steps = [
            ("1. Java环境检查", self.check_java_environment),
            ("2. J<PERSON>文件检查", self.check_jar_files),
            ("3. CommandLineMain类检查", self.check_commandline_main_class),
            ("4. JAR文件内容分析", self.analyze_jar_contents),
            ("5. Java命令行测试", self.test_java_command_line),
            ("6. 简化Java调用测试", self.test_simplified_java_call),
            ("7. 队徽路径Java处理测试", self.test_logo_path_java_processing),
            ("8. Java环境问题根因分析", self.analyze_java_root_cause)
        ]
        
        for step_name, diagnosis_func in diagnosis_steps:
            print(f"\n{step_name}")
            print("-" * 60)
            try:
                result = diagnosis_func()
                self.test_results[step_name] = result
                if result.get('success'):
                    print(f"✅ {step_name} - 通过")
                    if result.get('findings'):
                        for finding in result['findings']:
                            print(f"   🔍 {finding}")
                else:
                    print(f"❌ {step_name} - 发现问题: {result.get('issue', '未知问题')}")
                    if result.get('details'):
                        for detail in result['details']:
                            print(f"   ⚠️ {detail}")
            except Exception as e:
                print(f"❌ {step_name} - 异常: {e}")
                self.test_results[step_name] = {'success': False, 'error': str(e)}
                import traceback
                traceback.print_exc()
        
        # 生成诊断报告
        self.generate_diagnosis_report()

    def check_java_environment(self):
        """检查Java环境"""
        
        try:
            findings = []
            
            # 检查Java版本
            try:
                result = subprocess.run(
                    ['java', '-version'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    java_version = result.stderr.strip() if result.stderr else result.stdout.strip()
                    findings.append(f"Java可用: {java_version.split()[0] if java_version else 'Unknown'}")
                    
                    # 解析Java版本
                    if 'version' in java_version:
                        version_line = [line for line in java_version.split('\n') if 'version' in line][0]
                        findings.append(f"版本信息: {version_line}")
                else:
                    findings.append(f"Java版本检查失败: 返回码 {result.returncode}")
                    
            except FileNotFoundError:
                return {
                    'success': False,
                    'issue': 'Java未安装或不在PATH中',
                    'details': ['请安装Java 8或更高版本']
                }
            except subprocess.TimeoutExpired:
                return {
                    'success': False,
                    'issue': 'Java命令超时',
                    'details': ['Java环境可能有问题']
                }
            
            # 检查Java classpath支持
            try:
                result = subprocess.run(
                    ['java', '-cp', '.', '-version'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                findings.append(f"Java classpath支持: {'正常' if result.returncode == 0 else '异常'}")
            except Exception as e:
                findings.append(f"Java classpath测试失败: {e}")
            
            return {
                'success': True,
                'findings': findings
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def check_jar_files(self):
        """检查JAR文件"""
        
        try:
            findings = []
            
            # 查找JAR文件
            jar_locations = [
                'word_zc/ai-football-generator/target',
                'word_zc\\ai-football-generator\\target'
            ]
            
            jar_files = []
            for location in jar_locations:
                if os.path.exists(location):
                    for file in os.listdir(location):
                        if file.endswith('.jar'):
                            jar_path = os.path.join(location, file)
                            jar_files.append(jar_path)
            
            findings.append(f"找到JAR文件: {len(jar_files)} 个")
            
            for jar_file in jar_files:
                findings.append(f"JAR文件: {jar_file}")
                
                # 检查文件大小
                try:
                    size = os.path.getsize(jar_file)
                    findings.append(f"  文件大小: {size/1024:.1f}KB")
                    
                    # 检查文件可读性
                    readable = os.access(jar_file, os.R_OK)
                    findings.append(f"  文件可读: {readable}")
                    
                    # 检查文件修改时间
                    mtime = os.path.getmtime(jar_file)
                    findings.append(f"  修改时间: {datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')}")
                    
                except Exception as e:
                    findings.append(f"  文件检查失败: {e}")
            
            if not jar_files:
                return {
                    'success': False,
                    'issue': '未找到JAR文件',
                    'details': ['请确保Java项目已编译并生成JAR文件']
                }
            
            return {
                'success': True,
                'findings': findings,
                'jar_files': jar_files
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def check_commandline_main_class(self):
        """检查CommandLineMain类"""
        
        try:
            findings = []
            
            # 获取JAR文件
            jar_result = self.test_results.get("2. JAR文件检查", {})
            jar_files = jar_result.get('jar_files', [])
            
            if not jar_files:
                return {
                    'success': False,
                    'issue': '没有可用的JAR文件',
                    'details': ['需要先通过JAR文件检查']
                }
            
            # 检查每个JAR文件中的CommandLineMain类
            for jar_file in jar_files:
                findings.append(f"检查JAR文件: {os.path.basename(jar_file)}")
                
                try:
                    # 使用jar命令列出JAR文件内容
                    result = subprocess.run(
                        ['jar', 'tf', jar_file],
                        capture_output=True,
                        text=True,
                        timeout=30
                    )
                    
                    if result.returncode == 0:
                        jar_contents = result.stdout
                        
                        # 检查是否包含CommandLineMain.class
                        if 'CommandLineMain.class' in jar_contents:
                            findings.append(f"  ✅ 包含CommandLineMain.class")
                        else:
                            findings.append(f"  ❌ 不包含CommandLineMain.class")
                        
                        # 检查其他相关类
                        related_classes = ['FootballReportGenerator.class', 'WordGeneratorCore.class', 'JsonDataParser.class']
                        for class_name in related_classes:
                            if class_name in jar_contents:
                                findings.append(f"  ✅ 包含{class_name}")
                            else:
                                findings.append(f"  ❌ 不包含{class_name}")
                    else:
                        findings.append(f"  ❌ JAR内容列表失败: {result.stderr}")
                        
                except FileNotFoundError:
                    findings.append(f"  ⚠️ jar命令不可用，尝试其他方法")
                    
                    # 尝试直接用Java测试类是否存在
                    try:
                        test_result = subprocess.run(
                            ['java', '-cp', jar_file, 'CommandLineMain'],
                            capture_output=True,
                            text=True,
                            timeout=10
                        )
                        
                        if 'Usage:' in test_result.stderr or 'CommandLineMain' in test_result.stderr:
                            findings.append(f"  ✅ CommandLineMain类可访问")
                        else:
                            findings.append(f"  ❌ CommandLineMain类不可访问")
                            findings.append(f"    错误: {test_result.stderr[:100]}")
                            
                    except Exception as e:
                        findings.append(f"  ❌ Java类测试失败: {e}")
                
                except Exception as e:
                    findings.append(f"  ❌ JAR检查异常: {e}")
            
            return {
                'success': True,
                'findings': findings
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def analyze_jar_contents(self):
        """分析JAR文件内容"""
        
        try:
            findings = []
            
            # 获取JAR文件
            jar_result = self.test_results.get("2. JAR文件检查", {})
            jar_files = jar_result.get('jar_files', [])
            
            if not jar_files:
                return {
                    'success': False,
                    'issue': '没有可用的JAR文件'
                }
            
            # 分析主要的JAR文件（通常是最大的或最新的）
            main_jar = max(jar_files, key=lambda x: os.path.getsize(x))
            findings.append(f"分析主JAR文件: {os.path.basename(main_jar)}")
            
            try:
                # 尝试运行JAR文件看是否有帮助信息
                result = subprocess.run(
                    ['java', '-jar', main_jar],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                findings.append(f"直接运行JAR返回码: {result.returncode}")
                
                if result.stdout:
                    findings.append(f"标准输出: {result.stdout[:200]}")
                if result.stderr:
                    findings.append(f"标准错误: {result.stderr[:200]}")
                
            except Exception as e:
                findings.append(f"直接运行JAR失败: {e}")
            
            # 尝试用-cp方式运行CommandLineMain
            try:
                result = subprocess.run(
                    ['java', '-cp', main_jar, 'CommandLineMain'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                findings.append(f"CommandLineMain调用返回码: {result.returncode}")
                
                if result.stdout:
                    findings.append(f"CommandLineMain标准输出: {result.stdout[:200]}")
                if result.stderr:
                    findings.append(f"CommandLineMain标准错误: {result.stderr[:200]}")
                
                # 检查是否显示了使用帮助
                if 'Usage:' in result.stderr or 'CommandLineMain' in result.stderr:
                    findings.append("✅ CommandLineMain类正常响应")
                else:
                    findings.append("❌ CommandLineMain类响应异常")
                
            except Exception as e:
                findings.append(f"CommandLineMain调用失败: {e}")
            
            return {
                'success': True,
                'findings': findings,
                'main_jar': main_jar
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_java_command_line(self):
        """测试Java命令行"""
        
        try:
            findings = []
            
            # 获取主JAR文件
            jar_analysis = self.test_results.get("4. JAR文件内容分析", {})
            main_jar = jar_analysis.get('main_jar')
            
            if not main_jar:
                return {
                    'success': False,
                    'issue': '没有可用的主JAR文件'
                }
            
            # 创建测试JSON文件
            test_data = {
                "teamInfo": {
                    "title": "Java测试队报名表",
                    "organizationName": "Java测试队",
                    "teamLeader": "测试领队",
                    "coach": "测试教练",
                    "teamDoctor": "测试队医",
                    "contactPerson": "测试联系人",
                    "contactPhone": "13800138000",
                    "logoPath": "assets/logos/test_logo.png"
                },
                "players": [
                    {
                        "name": "测试球员1",
                        "jerseyNumber": "1",
                        "jerseyColor": "红色",
                        "shortsColor": "蓝色",
                        "socksColor": "白色",
                        "photoPath": "test_photo_1.jpg"
                    }
                ]
            }
            
            # 写入临时JSON文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
                json.dump(test_data, f, ensure_ascii=False, indent=2)
                test_json_file = f.name
            
            findings.append(f"创建测试JSON文件: {test_json_file}")
            
            # 测试Java命令
            cmd = ['java', '-cp', main_jar, 'CommandLineMain', test_json_file]
            findings.append(f"执行命令: {' '.join(cmd)}")
            
            try:
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=60,
                    encoding='utf-8',
                    errors='ignore'
                )
                
                findings.append(f"命令返回码: {result.returncode}")
                
                if result.stdout:
                    findings.append("标准输出:")
                    for line in result.stdout.split('\n')[:10]:  # 只显示前10行
                        if line.strip():
                            findings.append(f"  {line}")
                
                if result.stderr:
                    findings.append("标准错误:")
                    for line in result.stderr.split('\n')[:10]:  # 只显示前10行
                        if line.strip():
                            findings.append(f"  {line}")
                
                # 检查是否成功
                success_indicators = ['SUCCESS:', 'Generated successfully', 'Word document created']
                java_success = any(indicator in result.stdout + result.stderr for indicator in success_indicators)
                
                findings.append(f"Java执行成功: {java_success}")
                
                # 清理临时文件
                try:
                    os.unlink(test_json_file)
                except:
                    pass
                
                return {
                    'success': True,
                    'findings': findings,
                    'java_success': java_success,
                    'return_code': result.returncode
                }
                
            except subprocess.TimeoutExpired:
                findings.append("❌ Java命令超时（60秒）")
                return {
                    'success': False,
                    'issue': 'Java命令执行超时',
                    'details': findings
                }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_simplified_java_call(self):
        """测试简化的Java调用"""
        
        try:
            findings = []
            
            # 获取主JAR文件
            jar_analysis = self.test_results.get("4. JAR文件内容分析", {})
            main_jar = jar_analysis.get('main_jar')
            
            if not main_jar:
                return {
                    'success': False,
                    'issue': '没有可用的主JAR文件'
                }
            
            # 测试1: 无参数调用（应该显示使用帮助）
            findings.append("测试1: 无参数调用CommandLineMain")
            
            try:
                result = subprocess.run(
                    ['java', '-cp', main_jar, 'CommandLineMain'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                findings.append(f"  返回码: {result.returncode}")
                
                if 'Usage:' in result.stderr or 'ERROR:Usage:' in result.stderr:
                    findings.append("  ✅ 正确显示使用帮助")
                else:
                    findings.append("  ❌ 未显示预期的使用帮助")
                    if result.stderr:
                        findings.append(f"  错误输出: {result.stderr[:100]}")
                
            except Exception as e:
                findings.append(f"  ❌ 调用失败: {e}")
            
            # 测试2: 错误文件路径调用
            findings.append("测试2: 错误文件路径调用")
            
            try:
                result = subprocess.run(
                    ['java', '-cp', main_jar, 'CommandLineMain', 'nonexistent.json'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                findings.append(f"  返回码: {result.returncode}")
                
                if 'file not found' in result.stderr.lower() or 'ERROR:JSON file not found' in result.stderr:
                    findings.append("  ✅ 正确处理文件不存在错误")
                else:
                    findings.append("  ❌ 未正确处理文件不存在错误")
                    if result.stderr:
                        findings.append(f"  错误输出: {result.stderr[:100]}")
                
            except Exception as e:
                findings.append(f"  ❌ 调用失败: {e}")
            
            return {
                'success': True,
                'findings': findings
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_logo_path_java_processing(self):
        """测试队徽路径Java处理"""
        
        try:
            findings = []
            
            # 获取主JAR文件
            jar_analysis = self.test_results.get("4. JAR文件内容分析", {})
            main_jar = jar_analysis.get('main_jar')
            
            if not main_jar:
                return {
                    'success': False,
                    'issue': '没有可用的主JAR文件'
                }
            
            # 创建包含队徽路径的测试数据
            test_logo_path = "assets/logos/test_logo_java.png"
            
            test_data = {
                "teamInfo": {
                    "title": "队徽路径测试队报名表",
                    "organizationName": "队徽路径测试队",
                    "teamLeader": "测试领队",
                    "coach": "测试教练",
                    "teamDoctor": "测试队医",
                    "contactPerson": "测试联系人",
                    "contactPhone": "13800138000",
                    "logoPath": test_logo_path  # 关键：队徽路径
                },
                "players": [
                    {
                        "name": "测试球员1",
                        "jerseyNumber": "1",
                        "jerseyColor": "红色",
                        "shortsColor": "蓝色",
                        "socksColor": "白色",
                        "photoPath": "test_photo_1.jpg"
                    }
                ]
            }
            
            findings.append(f"测试队徽路径: {test_logo_path}")
            
            # 写入临时JSON文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
                json.dump(test_data, f, ensure_ascii=False, indent=2)
                test_json_file = f.name
            
            findings.append(f"创建包含队徽路径的JSON文件: {test_json_file}")
            
            # 执行Java程序
            cmd = ['java', '-cp', main_jar, 'CommandLineMain', test_json_file]
            
            try:
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=60,
                    encoding='utf-8',
                    errors='ignore'
                )
                
                findings.append(f"Java执行返回码: {result.returncode}")
                
                # 分析输出中的队徽相关信息
                output_text = result.stdout + result.stderr
                
                if 'logoPath' in output_text:
                    findings.append("✅ Java程序处理了logoPath字段")
                else:
                    findings.append("❌ Java程序未处理logoPath字段")
                
                if test_logo_path in output_text:
                    findings.append("✅ Java程序识别了队徽路径")
                else:
                    findings.append("❌ Java程序未识别队徽路径")
                
                # 检查是否有队徽相关的错误
                logo_errors = [
                    'logo not found',
                    'image not found',
                    'logoPath',
                    'Failed to load logo'
                ]
                
                for error in logo_errors:
                    if error.lower() in output_text.lower():
                        findings.append(f"⚠️ 发现队徽相关信息: {error}")
                
                # 显示关键输出
                if result.stderr:
                    findings.append("Java错误输出（前5行）:")
                    for line in result.stderr.split('\n')[:5]:
                        if line.strip():
                            findings.append(f"  {line}")
                
                # 清理临时文件
                try:
                    os.unlink(test_json_file)
                except:
                    pass
                
                return {
                    'success': True,
                    'findings': findings,
                    'java_return_code': result.returncode
                }
                
            except subprocess.TimeoutExpired:
                findings.append("❌ Java命令超时")
                return {
                    'success': False,
                    'issue': 'Java命令执行超时',
                    'details': findings
                }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def analyze_java_root_cause(self):
        """分析Java环境问题根因"""
        
        try:
            findings = []
            root_causes = []
            
            findings.append("综合分析Java环境诊断结果...")
            
            # 分析各个测试结果
            java_env_ok = self.test_results.get("1. Java环境检查", {}).get('success', False)
            jar_files_ok = self.test_results.get("2. JAR文件检查", {}).get('success', False)
            commandline_main_ok = self.test_results.get("3. CommandLineMain类检查", {}).get('success', False)
            jar_analysis_ok = self.test_results.get("4. JAR文件内容分析", {}).get('success', False)
            java_cmd_ok = self.test_results.get("5. Java命令行测试", {}).get('success', False)
            simplified_ok = self.test_results.get("6. 简化Java调用测试", {}).get('success', False)
            logo_processing_ok = self.test_results.get("7. 队徽路径Java处理测试", {}).get('success', False)
            
            findings.append(f"Java环境: {'✅' if java_env_ok else '❌'}")
            findings.append(f"JAR文件: {'✅' if jar_files_ok else '❌'}")
            findings.append(f"CommandLineMain类: {'✅' if commandline_main_ok else '❌'}")
            findings.append(f"JAR内容分析: {'✅' if jar_analysis_ok else '❌'}")
            findings.append(f"Java命令行测试: {'✅' if java_cmd_ok else '❌'}")
            findings.append(f"简化调用测试: {'✅' if simplified_ok else '❌'}")
            findings.append(f"队徽处理测试: {'✅' if logo_processing_ok else '❌'}")
            
            # 根据测试结果确定根因
            if not java_env_ok:
                root_causes.append("Java环境问题：Java未正确安装或配置")
            
            if not jar_files_ok:
                root_causes.append("JAR文件问题：JAR文件不存在或不可访问")
            
            if not commandline_main_ok:
                root_causes.append("CommandLineMain类问题：类不存在或JAR文件损坏")
            
            # 检查具体的Java执行结果
            java_cmd_result = self.test_results.get("5. Java命令行测试", {})
            if java_cmd_result.get('return_code') != 0:
                root_causes.append("Java程序执行失败：可能是数据格式或程序逻辑问题")
            
            # 检查简化调用的结果
            simplified_result = self.test_results.get("6. 简化Java调用测试", {})
            simplified_findings = simplified_result.get('findings', [])
            
            if any('未显示预期的使用帮助' in finding for finding in simplified_findings):
                root_causes.append("CommandLineMain类响应异常：可能是类路径或依赖问题")
            
            # 最可能的根因
            if root_causes:
                findings.append(f"\n🔍 发现的根因:")
                for i, cause in enumerate(root_causes, 1):
                    findings.append(f"  {i}. {cause}")
            else:
                findings.append("未发现明确的Java环境问题")
            
            # 提供修复建议
            suggestions = []
            if "Java环境问题" in str(root_causes):
                suggestions.append("重新安装Java 8或更高版本，确保java命令在PATH中")
            if "JAR文件问题" in str(root_causes):
                suggestions.append("重新编译Java项目，确保生成正确的JAR文件")
            if "CommandLineMain类问题" in str(root_causes):
                suggestions.append("检查Java源代码编译是否成功，确保所有类都包含在JAR中")
            if "程序执行失败" in str(root_causes):
                suggestions.append("检查传递给Java程序的JSON数据格式是否正确")
            
            if suggestions:
                findings.append(f"\n💡 修复建议:")
                for i, suggestion in enumerate(suggestions, 1):
                    findings.append(f"  {i}. {suggestion}")
            
            return {
                'success': True,
                'findings': findings,
                'root_causes': root_causes,
                'suggestions': suggestions
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def generate_diagnosis_report(self):
        """生成诊断报告"""
        
        print(f"\n📋 Java环境诊断报告")
        print("=" * 80)
        
        # 获取根因分析结果
        root_cause_result = self.test_results.get("8. Java环境问题根因分析", {})
        
        if root_cause_result.get('success'):
            root_causes = root_cause_result.get('root_causes', [])
            suggestions = root_cause_result.get('suggestions', [])
            
            if root_causes:
                print("🔍 发现的根本原因:")
                for i, cause in enumerate(root_causes, 1):
                    print(f"   {i}. {cause}")
            
            if suggestions:
                print(f"\n💡 修复建议:")
                for i, suggestion in enumerate(suggestions, 1):
                    print(f"   {i}. {suggestion}")
        
        # 统计诊断结果
        total_diagnoses = len(self.test_results)
        successful_diagnoses = sum(1 for result in self.test_results.values() if result.get('success'))
        
        print(f"\n📊 诊断统计:")
        print(f"   总诊断项: {total_diagnoses}")
        print(f"   成功完成: {successful_diagnoses}")
        print(f"   完成率: {successful_diagnoses/total_diagnoses*100:.1f}%")
        
        print(f"\n⏰ 诊断完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 保存详细诊断报告
        self.save_diagnosis_report()

    def save_diagnosis_report(self):
        """保存诊断报告"""
        
        report_data = {
            'diagnosis_time': datetime.now().isoformat(),
            'test_results': self.test_results,
            'summary': {
                'total_diagnoses': len(self.test_results),
                'successful_diagnoses': sum(1 for result in self.test_results.values() if result.get('success')),
                'root_causes': self.test_results.get("8. Java环境问题根因分析", {}).get('root_causes', []),
                'suggestions': self.test_results.get("8. Java环境问题根因分析", {}).get('suggestions', [])
            }
        }
        
        report_file = f"java_environment_diagnosis_report_{int(datetime.now().timestamp())}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"📄 详细诊断报告已保存: {report_file}")

if __name__ == "__main__":
    diagnostic = JavaEnvironmentDiagnostic()
    diagnostic.run_java_diagnosis()
