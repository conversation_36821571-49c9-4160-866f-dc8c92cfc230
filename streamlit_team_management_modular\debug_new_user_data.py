#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试新用户数据和Word生成问题
"""

import zipfile
import xml.etree.ElementTree as ET
import os
import json

def check_new_user_word_document():
    """检查新用户生成的Word文档"""
    print("🔍 检查新用户生成的Word文档")
    print("=" * 60)
    
    word_file = "data/user_c8c5da29216f/word_output/足球队_registration_1756543809367.docx"
    
    if not os.path.exists(word_file):
        print(f"❌ Word文件不存在: {word_file}")
        return False
    
    try:
        with zipfile.ZipFile(word_file, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    print("📄 Word文档内容分析:")
                    
                    # 检查联系人信息
                    has_contact_person_zhaoliu = "赵六" in full_text
                    has_contact_phone = "18454432036" in full_text
                    
                    print(f"   联系人'赵六': {'✅ 找到' if has_contact_person_zhaoliu else '❌ 未找到'}")
                    print(f"   电话'18454432036': {'✅ 找到' if has_contact_phone else '❌ 未找到'}")
                    
                    # 检查占位符是否还存在
                    has_placeholder_person = "{{contactPerson}}" in content
                    has_placeholder_phone = "{{contactPhone}}" in content
                    
                    print(f"\n📄 占位符检查:")
                    print(f"   {{{{contactPerson}}}}: {'⚠️ 仍存在' if has_placeholder_person else '✅ 已替换'}")
                    print(f"   {{{{contactPhone}}}}: {'⚠️ 仍存在' if has_placeholder_phone else '✅ 已替换'}")
                    
                    # 显示联系人相关的上下文
                    print(f"\n📄 联系人相关内容:")
                    words = full_text.split()
                    contact_found = False
                    
                    for i, word in enumerate(words):
                        if "联系人" in word:
                            start = max(0, i-3)
                            end = min(len(words), i+15)
                            context = ' '.join(words[start:end])
                            print(f"   联系人上下文: {context}")
                            contact_found = True
                            break
                    
                    if not contact_found:
                        print("   ❌ 未找到联系人相关内容")
                    
                    # 显示文档的主要内容结构
                    print(f"\n📄 文档主要内容:")
                    lines = full_text.split('\n')
                    content_lines = [line.strip() for line in lines if line.strip()]
                    
                    for i, line in enumerate(content_lines[:15]):  # 显示前15行
                        if line:
                            print(f"   第{i+1}行: {line}")
                    
                    return {
                        'has_contact_person': has_contact_person_zhaoliu,
                        'has_contact_phone': has_contact_phone,
                        'has_placeholders': any([has_placeholder_person, has_placeholder_phone]),
                        'contact_found': contact_found,
                        'full_text': full_text
                    }
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return False
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_ai_data_structure():
    """检查AI数据结构"""
    print(f"\n🔍 检查AI数据结构")
    print("=" * 60)
    
    ai_data_file = "data/user_c8c5da29216f/enhanced_ai_data/天依002_ai_data.json"
    
    if not os.path.exists(ai_data_file):
        print(f"❌ AI数据文件不存在: {ai_data_file}")
        return False
    
    try:
        with open(ai_data_file, 'r', encoding='utf-8') as f:
            ai_data = json.load(f)
        
        print("📄 AI数据结构:")
        print(json.dumps(ai_data, ensure_ascii=False, indent=2))
        
        # 检查数据结构
        extracted_info = ai_data.get('extracted_info', {})
        basic_info = extracted_info.get('basic_info', {})
        additional_info = extracted_info.get('additional_info', {})
        
        print(f"\n📄 关键字段:")
        print(f"   contact_person: '{basic_info.get('contact_person', 'MISSING')}'")
        print(f"   contact_phone: '{basic_info.get('contact_phone', 'MISSING')}'")
        print(f"   leader_name: '{basic_info.get('leader_name', 'MISSING')}'")
        print(f"   coach_name: '{additional_info.get('coach_name', 'MISSING')}'")
        print(f"   team_doctor: '{basic_info.get('team_doctor', 'MISSING')}'")
        
        return ai_data
        
    except Exception as e:
        print(f"❌ 检查AI数据失败: {e}")
        return False

def test_data_conversion_process():
    """测试数据转换过程"""
    print(f"\n🔍 测试数据转换过程")
    print("=" * 60)
    
    try:
        # 加载AI数据
        ai_data_file = "data/user_c8c5da29216f/enhanced_ai_data/天依002_ai_data.json"
        with open(ai_data_file, 'r', encoding='utf-8') as f:
            ai_chat_data = json.load(f)
        
        # 模拟fashion_workflow_service中的数据转换
        print("📄 模拟数据转换过程:")
        
        # 步骤1: 模拟_convert_ai_chat_data_to_export_format
        team_name = "天依002"
        extracted_info = ai_chat_data.get('extracted_info', {})
        
        # 这里需要检查实际的转换逻辑
        converted_data = {
            "team_info": {
                "ai_extracted_info": {
                    "basic_info": extracted_info.get('basic_info', {}),
                    "additional_info": extracted_info.get('additional_info', {})
                }
            }
        }
        
        print("   转换后的数据结构:")
        print(json.dumps(converted_data, ensure_ascii=False, indent=2))
        
        # 步骤2: 模拟fashion_workflow.py中的team_data构建
        team_info = converted_data.get("team_info", {})
        ai_extracted_info = team_info.get("ai_extracted_info", {})
        basic_info = ai_extracted_info.get("basic_info", {})
        additional_info = ai_extracted_info.get("additional_info", {})
        
        # 使用修复后的逻辑
        team_data = {
            "name": team_name,
            "leader": basic_info.get("leader_name", ""),
            "coach": additional_info.get("coach_name", ""),  # 从additional_info获取
            "doctor": basic_info.get("team_doctor", ""),
            "contact_person": basic_info.get("contact_person", ""),
            "contact_phone": basic_info.get("contact_phone", "")
        }
        
        print(f"\n📄 构建的team_data:")
        for key, value in team_data.items():
            print(f"   {key}: '{value}'")
        
        # 步骤3: 测试WordGeneratorService
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        paths = app_settings.word_generator.get_absolute_paths("debug_test", app_settings.paths)
        
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        # 模拟球员数据
        players_data = [
            {
                'name': '测试球员',
                'jersey_number': '1',
                'photo': 'data/user_c8c5da29216f/photos/天依002/test.jpg'
            }
        ]
        
        # 检查数据准备
        json_data = word_service._prepare_json_data(team_data, players_data)
        team_info = json_data['teamInfo']
        
        print(f"\n📄 WordGeneratorService准备的数据:")
        print(f"   contactPerson: '{team_info.get('contactPerson', 'MISSING')}'")
        print(f"   contactPhone: '{team_info.get('contactPhone', 'MISSING')}'")
        print(f"   teamLeader: '{team_info.get('teamLeader', 'MISSING')}'")
        print(f"   coach: '{team_info.get('coach', 'MISSING')}'")
        
        # 生成Word文档测试
        print(f"\n🚀 测试Word文档生成:")
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print(f"✅ Word生成成功: {os.path.basename(result['file_path'])}")
            
            # 检查生成的文件内容
            return check_generated_word_content(result['file_path'])
        else:
            print(f"❌ Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def check_generated_word_content(file_path):
    """检查生成的Word文档内容"""
    print(f"\n🔍 检查新生成的Word文档内容")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    # 检查联系人信息
                    has_contact_person = "赵六" in full_text
                    has_contact_phone = "18454432036" in full_text
                    
                    print(f"📄 新文件内容检查:")
                    print(f"   联系人'赵六': {'✅ 找到' if has_contact_person else '❌ 未找到'}")
                    print(f"   电话'18454432036': {'✅ 找到' if has_contact_phone else '❌ 未找到'}")
                    
                    # 显示联系人上下文
                    if has_contact_person or has_contact_phone:
                        words = full_text.split()
                        for i, word in enumerate(words):
                            if "联系人" in word or "赵六" in word:
                                start = max(0, i-3)
                                end = min(len(words), i+15)
                                context = ' '.join(words[start:end])
                                print(f"   联系人上下文: {context}")
                                break
                    
                    return has_contact_person and has_contact_phone
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return False
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_actual_workflow_service():
    """检查实际的工作流服务"""
    print(f"\n🔍 检查实际的工作流服务")
    print("=" * 60)
    
    try:
        import streamlit as st
        
        # 模拟session state
        if 'user_id' not in st.session_state:
            st.session_state.user_id = 'user_c8c5da29216f'
        
        from services.fashion_workflow_service import FashionWorkflowService
        
        workflow_service = FashionWorkflowService('user_c8c5da29216f')
        
        # 测试AI数据加载
        ai_data = workflow_service._load_ai_export_data("天依002")
        
        if ai_data:
            print("✅ AI数据加载成功")
            print("📄 加载的AI数据结构:")
            
            team_info = ai_data.get("team_info", {})
            ai_extracted_info = team_info.get("ai_extracted_info", {})
            basic_info = ai_extracted_info.get("basic_info", {})
            
            print(f"   contact_person: '{basic_info.get('contact_person', 'MISSING')}'")
            print(f"   contact_phone: '{basic_info.get('contact_phone', 'MISSING')}'")
            print(f"   leader_name: '{basic_info.get('leader_name', 'MISSING')}'")
            
            return ai_data
        else:
            print("❌ AI数据加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 检查工作流服务失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 调试新用户数据和Word生成问题")
    print("=" * 70)
    
    # 1. 检查新用户生成的Word文档
    word_result = check_new_user_word_document()
    
    # 2. 检查AI数据结构
    ai_data = check_ai_data_structure()
    
    # 3. 检查实际工作流服务
    workflow_data = check_actual_workflow_service()
    
    # 4. 测试数据转换过程
    conversion_result = test_data_conversion_process()
    
    # 综合分析
    print(f"\n📊 综合分析结果")
    print("=" * 70)
    
    if word_result:
        if word_result.get('has_contact_person') and word_result.get('has_contact_phone'):
            print("✅ 新用户Word文档中包含联系人信息")
        else:
            print("❌ 新用户Word文档中缺少联系人信息")
            
            if word_result.get('has_placeholders'):
                print("   ⚠️ 文档中仍有未替换的占位符")
            else:
                print("   ⚠️ 占位符已替换但联系人信息为空")
    
    if ai_data:
        basic_info = ai_data.get('extracted_info', {}).get('basic_info', {})
        if basic_info.get('contact_person') and basic_info.get('contact_phone'):
            print("✅ AI数据中包含联系人信息")
        else:
            print("❌ AI数据中缺少联系人信息")
    
    if workflow_data:
        print("✅ 工作流服务能够加载AI数据")
    else:
        print("❌ 工作流服务无法加载AI数据")
    
    if conversion_result:
        print("✅ 数据转换和Word生成测试成功")
    else:
        print("❌ 数据转换和Word生成测试失败")
    
    # 最终诊断
    print(f"\n🎯 问题诊断:")
    
    if ai_data and not (word_result and word_result.get('has_contact_person')):
        print("🔍 AI数据正确，但Word生成有问题")
        print("💡 可能原因:")
        print("   1. 数据转换逻辑有问题")
        print("   2. 工作流服务的数据映射有问题")
        print("   3. 模板占位符仍有问题")
        
        if conversion_result:
            print("   ✅ 直接调用Word生成服务成功")
            print("   💡 问题可能在工作流服务的数据转换逻辑")
        else:
            print("   ❌ 直接调用Word生成服务也失败")
            print("   💡 问题可能在模板或Word生成服务本身")

if __name__ == "__main__":
    main()
