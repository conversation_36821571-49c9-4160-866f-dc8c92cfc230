#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置文件 - 使用15人模板
"""

import os
from dataclasses import dataclass
from typing import Dict, Any


@dataclass
class PathSettings:
    """路径配置 - 统一使用data目录结构"""
    DATA_FOLDER: str = 'data'
    TEMP_FOLDER: str = 'temp'
    ASSETS_FOLDER: str = 'assets'
    CACHE_FOLDER: str = 'cache'

    def ensure_directories(self) -> None:
        """确保系统级目录存在"""
        for folder in [self.DATA_FOLDER, self.TEMP_FOLDER,
                      self.ASSETS_FOLDER, self.CACHE_FOLDER]:
            if not os.path.exists(folder):
                os.makedirs(folder)

    def get_user_data_path(self, user_id: str, data_type: str = "") -> str:
        """获取用户数据路径"""
        user_folder = os.path.join(self.DATA_FOLDER, user_id)
        if data_type:
            return os.path.join(user_folder, data_type)
        else:
            return user_folder


@dataclass
class WordGeneratorSettings:
    """Word生成器配置 - 使用15人模板"""
    JAR_PATH: str = "../word_zc/ai-football-generator/target/word-generator.jar"
    TEMPLATE_PATH: str = "../word_zc/template_15players_fixed.docx"  # 使用修复版15人模板
    TEMP_DIR: str = "temp"
    MAX_FILES: int = 20
    TIMEOUT: int = 60

    def get_absolute_paths(self, user_id: str = None, paths_instance=None) -> Dict[str, str]:
        """获取绝对路径"""
        paths = {
            'jar_path': os.path.abspath(self.JAR_PATH),
            'template_path': os.path.abspath(self.TEMPLATE_PATH),
            'temp_dir': os.path.abspath(self.TEMP_DIR)
        }

        if user_id and paths_instance:
            user_word_output = paths_instance.get_user_data_path(user_id, "word_output")
            paths['output_dir'] = os.path.abspath(user_word_output)
        elif paths_instance:
            default_output = paths_instance.get_user_data_path("default_user", "word_output")
            paths['output_dir'] = os.path.abspath(default_output)
        else:
            paths['output_dir'] = os.path.abspath(self.TEMP_DIR)

        return paths


@dataclass
class AppSettings:
    """应用程序主要配置"""
    PAGE_TITLE: str = "淄川五人制球队管理系统"
    PAGE_ICON: str = "⚽"
    LAYOUT: str = "wide"
    INITIAL_SIDEBAR_STATE: str = "expanded"

    def __post_init__(self):
        """初始化后处理"""
        self.paths = PathSettings()
        self.word_generator = WordGeneratorSettings()
        self.paths.ensure_directories()


# 测试配置实例
test_app_settings = AppSettings()
