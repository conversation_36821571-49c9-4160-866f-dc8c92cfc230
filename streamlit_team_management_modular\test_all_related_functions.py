#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面测试修改配置文件后所有相关功能
"""

import sys
import os
import shutil
import importlib
from pathlib import Path

def backup_and_modify_config():
    """备份并修改配置文件"""
    config_file = "config/settings.py"
    backup_file = "config/settings_original_backup.py"
    
    print("🔧 修改配置文件...")
    
    # 备份原始配置文件
    if not os.path.exists(backup_file):
        shutil.copy2(config_file, backup_file)
        print(f"✅ 已备份原始配置文件")
    
    # 读取并修改配置文件
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修改模板路径
    old_template_path = 'TEMPLATE_PATH: str = "../word_zc/ai-football-generator/template.docx"'
    new_template_path = 'TEMPLATE_PATH: str = "../word_zc/template_15players_fixed.docx"'
    
    if old_template_path in content:
        modified_content = content.replace(old_template_path, new_template_path)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print(f"✅ 已修改配置文件，使用15人模板")
        return True
    else:
        print(f"❌ 未找到目标配置行")
        return False

def clear_module_cache():
    """清除模块缓存"""
    print("🧹 清除模块缓存...")
    modules_to_clear = [
        'config.settings',
        'word_generator_service',
        'services.fashion_workflow_service'
    ]
    
    for module in modules_to_clear:
        if module in sys.modules:
            del sys.modules[module]
            print(f"✅ 已清除 {module} 缓存")

def test_config_loading():
    """测试配置加载"""
    print("\n🧪 测试1: 配置加载")
    print("=" * 50)
    
    try:
        from config.settings import app_settings
        
        # 获取配置路径
        paths = app_settings.word_generator.get_absolute_paths("test_user", app_settings.paths)
        
        print(f"📄 模板路径: {paths['template_path']}")
        print(f"📁 JAR路径: {paths['jar_path']}")
        print(f"📂 输出目录: {paths['output_dir']}")
        
        # 检查文件存在性
        template_exists = os.path.exists(paths['template_path'])
        jar_exists = os.path.exists(paths['jar_path'])
        
        print(f"✅ 模板文件存在: {template_exists}")
        print(f"✅ JAR文件存在: {jar_exists}")
        
        if template_exists and "template_15players" in paths['template_path']:
            print("🎯 配置已正确指向15人模板!")
            return True
        else:
            print("❌ 配置未正确指向15人模板")
            return False
            
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

def test_word_generator_service():
    """测试Word生成服务"""
    print("\n🧪 测试2: Word生成服务")
    print("=" * 50)
    
    try:
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 获取配置路径
        paths = app_settings.word_generator.get_absolute_paths("test_user", app_settings.paths)
        
        # 创建Word生成服务
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        print("✅ WordGeneratorService创建成功")
        
        # 准备测试数据
        team_data = {
            'name': '测试所有功能队',
            'leader': '张三',
            'coach': '李四',
            'doctor': '王五'
        }
        
        players_data = [
            {
                'name': '测试球员1',
                'jersey_number': '10',
                'photo': 'word_zc/ai-football-generator/java_word_photos/player1.png'
            }
        ]
        
        # 生成Word文档
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print("✅ Word生成成功!")
            print(f"📄 文件路径: {result['file_path']}")
            return True
        else:
            print(f"❌ Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ Word生成服务测试失败: {e}")
        return False

def test_fashion_workflow_service():
    """测试时尚工作流服务"""
    print("\n🧪 测试3: 时尚工作流服务")
    print("=" * 50)
    
    try:
        from services.fashion_workflow_service import FashionWorkflowService
        
        print("✅ FashionWorkflowService导入成功")
        
        # 检查自动生成Word文档的方法是否存在
        if hasattr(FashionWorkflowService, '_auto_generate_word_document'):
            print("✅ _auto_generate_word_document方法存在")
            return True
        else:
            print("❌ _auto_generate_word_document方法不存在")
            return False
            
    except Exception as e:
        print(f"❌ 时尚工作流服务测试失败: {e}")
        return False

def test_setup_word_template():
    """测试Word模板设置"""
    print("\n🧪 测试4: Word模板设置")
    print("=" * 50)
    
    try:
        from setup_word_template import check_current_template_setup
        
        template_path, template_exists = check_current_template_setup()
        
        if template_exists and "template_15players" in template_path:
            print("✅ 模板设置检查通过")
            return True
        else:
            print("❌ 模板设置检查失败")
            return False
            
    except Exception as e:
        print(f"❌ 模板设置测试失败: {e}")
        return False

def test_hardcoded_paths():
    """测试硬编码路径的文件"""
    print("\n🧪 测试5: 硬编码路径检查")
    print("=" * 50)
    
    hardcoded_files = [
        "test_complete_word_generation.py",
        "test_word_integration.py", 
        "test_final_integration.py"
    ]
    
    issues_found = []
    
    for file_name in hardcoded_files:
        if os.path.exists(file_name):
            try:
                with open(file_name, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否有硬编码的模板路径
                if 'template_path = "../word_zc/ai-football-generator/template.docx"' in content:
                    issues_found.append(file_name)
                    print(f"⚠️ {file_name} 包含硬编码模板路径")
                else:
                    print(f"✅ {file_name} 无硬编码路径问题")
                    
            except Exception as e:
                print(f"❌ 检查 {file_name} 失败: {e}")
    
    if issues_found:
        print(f"\n⚠️ 发现 {len(issues_found)} 个文件包含硬编码路径")
        print("💡 这些文件可能需要更新以使用配置文件")
        return False
    else:
        print("✅ 未发现硬编码路径问题")
        return True

def restore_original_config():
    """恢复原始配置文件"""
    config_file = "config/settings.py"
    backup_file = "config/settings_original_backup.py"
    
    if os.path.exists(backup_file):
        shutil.copy2(backup_file, config_file)
        print("✅ 已恢复原始配置文件")
        return True
    else:
        print("❌ 备份文件不存在，无法恢复")
        return False

def main():
    """主测试函数"""
    print("🎯 全面测试修改配置文件后的相关功能")
    print("=" * 60)
    
    test_results = []
    
    try:
        # 1. 修改配置文件
        if not backup_and_modify_config():
            print("❌ 配置文件修改失败，终止测试")
            return
        
        # 2. 清除模块缓存
        clear_module_cache()
        
        # 3. 运行各项测试
        tests = [
            ("配置加载", test_config_loading),
            ("Word生成服务", test_word_generator_service),
            ("时尚工作流服务", test_fashion_workflow_service),
            ("Word模板设置", test_setup_word_template),
            ("硬编码路径检查", test_hardcoded_paths)
        ]
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                test_results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name} 测试异常: {e}")
                test_results.append((test_name, False))
        
        # 4. 恢复原始配置
        restore_original_config()
        
        # 5. 显示测试结果
        print("\n📊 测试结果汇总")
        print("=" * 60)
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
        
        if passed == total:
            print("\n🎉 所有测试通过！修改配置文件后无需修改其他代码")
        else:
            print(f"\n⚠️ 有 {total - passed} 项测试失败，可能需要额外修改")
            
    except Exception as e:
        print(f"❌ 测试过程出现异常: {e}")
        # 确保恢复原始配置
        restore_original_config()

if __name__ == "__main__":
    main()
