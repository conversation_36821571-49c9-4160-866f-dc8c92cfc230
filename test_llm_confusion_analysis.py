#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试LLM在修改代码时可能的混淆和误导问题
Test LLM Confusion and Misleading Issues When Modifying Code
"""

import os
import re
from pathlib import Path

def analyze_llm_confusion_risks():
    """分析LLM可能的混淆风险"""
    print("🤖 分析LLM修改代码时的混淆风险")
    print("=" * 80)
    
    confusion_risks = {
        "文件路径混淆": {
            "risk_level": "🔴 高风险",
            "description": "LLM可能修改错误的文件路径",
            "examples": [
                {
                    "intended": "streamlit_team_management_modular/services/ai_image_generation_service.py",
                    "confused_with": "word_zc/ai-football-generator/simple_python_test.py",
                    "reason": "两个都包含Python文件和AI相关功能"
                },
                {
                    "intended": "streamlit_team_management_modular/photos/",
                    "confused_with": "word_zc/ai-football-generator/photos/",
                    "reason": "完全相同的目录名"
                }
            ]
        },
        
        "功能实现混淆": {
            "risk_level": "🔴 高风险", 
            "description": "LLM可能混淆不同的功能实现",
            "examples": [
                {
                    "intended": "修改Python Word生成服务",
                    "confused_with": "修改Java Word生成核心",
                    "reason": "两套Word生成系统功能相似"
                },
                {
                    "intended": "修改Streamlit照片处理",
                    "confused_with": "修改Java照片裁剪",
                    "reason": "都涉及照片处理功能"
                }
            ]
        },
        
        "依赖关系混淆": {
            "risk_level": "🟡 中风险",
            "description": "LLM可能混淆不同系统的依赖",
            "examples": [
                {
                    "intended": "修改Python依赖配置",
                    "confused_with": "修改Java Maven配置",
                    "reason": "都是依赖管理文件"
                },
                {
                    "intended": "修改Streamlit配置",
                    "confused_with": "修改Java properties配置",
                    "reason": "都是配置文件"
                }
            ]
        }
    }
    
    for risk_type, details in confusion_risks.items():
        print(f"\n🚨 {risk_type}")
        print(f"   风险级别: {details['risk_level']}")
        print(f"   描述: {details['description']}")
        print(f"   具体例子:")
        
        for i, example in enumerate(details['examples'], 1):
            print(f"      {i}. 预期修改: {example['intended']}")
            print(f"         可能混淆: {example['confused_with']}")
            print(f"         混淆原因: {example['reason']}")
    
    return confusion_risks

def test_file_similarity():
    """测试文件相似性导致的混淆"""
    print(f"\n📁 文件相似性混淆测试")
    print("=" * 80)
    
    similar_files = {
        "Python测试文件": {
            "streamlit": [
                "test_ai_image_generation_logs.py",
                "test_logo_generation.py", 
                "test_real_logo_generation.py"
            ],
            "word_zc": [
                "simple_python_test.py",
                "test_python_integration.py"
            ],
            "confusion_risk": "🔴 高风险 - 都是Python测试文件，LLM可能修改错文件"
        },
        
        "配置文件": {
            "streamlit": [
                "config/settings.py",
                "requirements.txt"
            ],
            "word_zc": [
                "config.properties",
                "pom.xml"
            ],
            "confusion_risk": "🟡 中风险 - 不同格式但功能相似"
        },
        
        "模板文件": {
            "streamlit": [
                "data/template_15players.docx"
            ],
            "word_zc": [
                "template.docx",
                "realistic_template.docx",
                "simple_template.docx"
            ],
            "confusion_risk": "🟡 中风险 - 都是Word模板，但用途可能不同"
        },
        
        "照片目录": {
            "streamlit": [
                "photos/"
            ],
            "word_zc": [
                "ai-football-generator/photos/"
            ],
            "confusion_risk": "🔴 高风险 - 完全相同的目录名和用途"
        }
    }
    
    for category, details in similar_files.items():
        print(f"\n📂 {category}")
        print(f"   风险评估: {details['confusion_risk']}")
        print(f"   Streamlit文件:")
        for file in details['streamlit']:
            print(f"      • streamlit_team_management_modular/{file}")
        print(f"   Word_zc文件:")
        for file in details['word_zc']:
            print(f"      • word_zc/ai-football-generator/{file}")
    
    return similar_files

def simulate_llm_modification_scenarios():
    """模拟LLM修改场景"""
    print(f"\n🎭 模拟LLM修改场景")
    print("=" * 80)
    
    scenarios = {
        "场景1: 修改API日志输出": {
            "user_request": "修改AI图像生成服务的日志输出",
            "correct_target": "streamlit_team_management_modular/services/ai_image_generation_service.py",
            "potential_confusion": [
                "word_zc/ai-football-generator/simple_python_test.py (包含AI相关代码)",
                "word_zc/ai-football-generator/test_python_integration.py (包含Python集成测试)"
            ],
            "confusion_probability": "🔴 高 - 都包含AI和Python关键词"
        },
        
        "场景2: 修改照片处理": {
            "user_request": "修改球员照片处理逻辑",
            "correct_target": "streamlit_team_management_modular/services/photo_service.py",
            "potential_confusion": [
                "word_zc/ai-football-generator/photos/ (照片存储目录)",
                "Java照片裁剪功能"
            ],
            "confusion_probability": "🟡 中 - 功能相似但实现不同"
        },
        
        "场景3: 修改Word生成": {
            "user_request": "修改Word文档生成功能",
            "correct_target": "streamlit_team_management_modular/word_generator_service.py",
            "potential_confusion": [
                "word_zc/ai-football-generator/src/main/java/WordGeneratorCore.java",
                "word_zc/ai-football-generator/PythonIntegrationAdapter.java"
            ],
            "confusion_probability": "🔴 高 - 两套完全不同的Word生成实现"
        },
        
        "场景4: 修改测试文件": {
            "user_request": "修改AI功能测试",
            "correct_target": "streamlit_team_management_modular/test_ai_improvements.py",
            "potential_confusion": [
                "word_zc/ai-football-generator/simple_python_test.py",
                "word_zc/ai-football-generator/test_python_integration.py"
            ],
            "confusion_probability": "🔴 高 - 测试文件名相似，功能重叠"
        }
    }
    
    for scenario_name, details in scenarios.items():
        print(f"\n🎬 {scenario_name}")
        print(f"   用户请求: {details['user_request']}")
        print(f"   正确目标: {details['correct_target']}")
        print(f"   混淆概率: {details['confusion_probability']}")
        print(f"   可能混淆的文件:")
        for confused_file in details['potential_confusion']:
            print(f"      ⚠️ {confused_file}")
    
    return scenarios

def analyze_context_clues():
    """分析上下文线索"""
    print(f"\n🔍 上下文线索分析")
    print("=" * 80)
    
    context_clues = {
        "有助于LLM正确识别的线索": [
            "文件路径中包含 'streamlit_team_management_modular'",
            "Python文件扩展名 .py",
            "import streamlit as st 语句",
            "Streamlit特有的函数调用 (st.info, st.success等)",
            "Python特有的语法和库"
        ],
        
        "可能误导LLM的线索": [
            "相同的目录名 (photos, output)",
            "相似的功能描述 (AI, 图像生成, Word生成)",
            "相同的业务术语 (球员, 队伍, 照片)",
            "类似的文件名模式 (test_*.py)",
            "重叠的技术栈 (都涉及Python)"
        ],
        
        "LLM容易忽略的重要区别": [
            "技术栈差异 (Java vs Python)",
            "架构层次 (核心模块 vs Web界面)",
            "依赖关系 (Maven vs pip)",
            "运行环境 (JVM vs Python解释器)",
            "集成方式 (JPype vs 直接调用)"
        ]
    }
    
    for category, clues in context_clues.items():
        print(f"\n📋 {category}:")
        for clue in clues:
            print(f"   • {clue}")
    
    return context_clues

def generate_llm_guidance_recommendations():
    """生成LLM指导建议"""
    print(f"\n💡 LLM指导建议")
    print("=" * 80)
    
    recommendations = {
        "给用户的建议": [
            "🎯 明确指定完整文件路径，避免歧义",
            "📝 在请求中明确说明是修改Streamlit系统还是Java核心",
            "🏷️ 使用明确的技术栈标识 (Python/Streamlit vs Java/Maven)",
            "📂 提供上下文信息，说明修改的具体模块",
            "⚠️ 提醒LLM注意两个系统的区别"
        ],
        
        "给LLM的提示词建议": [
            "🔍 '请确认您要修改的是Streamlit Python系统，而不是Java核心模块'",
            "📁 '目标文件应该在streamlit_team_management_modular/目录下'",
            "🐍 '这是Python代码修改，不是Java代码'",
            "🌐 '这是Web界面相关的修改，不是后端核心逻辑'",
            "⚡ '请使用Streamlit语法，不是Java语法'"
        ],
        
        "项目结构优化建议": [
            "🏷️ 重命名文件夹，增加明确的技术栈标识",
            "📚 创建README文件，明确说明各目录的用途",
            "🗂️ 分离不同技术栈的代码到不同的根目录",
            "📋 添加.gitignore，避免混淆文件",
            "🔗 创建明确的依赖关系文档"
        ]
    }
    
    for category, items in recommendations.items():
        print(f"\n🎯 {category}:")
        for item in items:
            print(f"   {item}")
    
    return recommendations

def main():
    """主函数"""
    print("🤖 LLM修改代码时的混淆风险分析")
    print("=" * 80)
    
    print("🎯 分析目标:")
    print("   检测两个文件夹是否会误导LLM修改错误的代码")
    print("   评估LLM在代码修改任务中的混淆风险")
    
    # 1. 分析混淆风险
    confusion_risks = analyze_llm_confusion_risks()
    
    # 2. 测试文件相似性
    similar_files = test_file_similarity()
    
    # 3. 模拟修改场景
    scenarios = simulate_llm_modification_scenarios()
    
    # 4. 分析上下文线索
    context_clues = analyze_context_clues()
    
    # 5. 生成指导建议
    recommendations = generate_llm_guidance_recommendations()
    
    # 总结
    print(f"\n🎯 分析结论")
    print("=" * 80)
    
    print("✅ 主要发现:")
    print("   🔴 确实存在高风险的LLM混淆问题")
    print("   📁 相同目录名和相似文件名容易误导")
    print("   🔄 功能重叠导致LLM难以区分正确目标")
    print("   🎯 缺乏明确的上下文线索帮助LLM识别")
    
    print(f"\n⚠️ 高风险场景:")
    print("   1. 修改AI图像生成服务 - 可能修改错Python文件")
    print("   2. 修改Word生成功能 - 可能混淆Java和Python实现")
    print("   3. 修改照片处理 - 可能操作错误的photos目录")
    print("   4. 修改测试文件 - 可能修改错误的测试脚本")
    
    print(f"\n💡 关键建议:")
    print("   🎯 用户应明确指定完整文件路径")
    print("   🏷️ 在请求中明确技术栈 (Python/Streamlit vs Java)")
    print("   📂 提供充分的上下文信息")
    print("   🗂️ 考虑重构项目结构，增加明确标识")
    
    print(f"\n🎉 结论:")
    print("   ✅ 您的担心是正确的！")
    print("   ⚠️ 这两个文件夹确实容易误导LLM")
    print("   🔧 建议采取预防措施，避免修改错误")

if __name__ == "__main__":
    main()
