#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业级队徽Word插入深度分析测试
Enterprise-level Logo Word Insertion Deep Analysis Test

专门分析队徽为什么没有插入到Word文档中的具体原因
"""

import os
import sys
import json
import time
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.append('streamlit_team_management_modular')

class LogoWordInsertionAnalyzer:
    """队徽Word插入分析器"""
    
    def __init__(self):
        self.test_team_name = "队徽插入分析测试队"
        self.analysis_results = {}
        
    def run_deep_analysis(self):
        """运行深度分析"""
        
        print("🔍 企业级队徽Word插入深度分析")
        print("=" * 80)
        print(f"⏰ 分析开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 目标: 找出队徽没有插入Word的具体原因")
        print()
        
        # 分析步骤
        analysis_steps = [
            ("1. Word生成数据流分析", self.analyze_word_generation_data_flow),
            ("2. 队徽路径传递分析", self.analyze_logo_path_transmission),
            ("3. Word模板队徽占位符分析", self.analyze_word_template_logo_placeholder),
            ("4. Java程序队徽处理分析", self.analyze_java_logo_processing),
            ("5. Word生成JSON数据分析", self.analyze_word_generation_json),
            ("6. 队徽文件访问性分析", self.analyze_logo_file_accessibility),
            ("7. Word生成完整流程追踪", self.trace_complete_word_generation_flow),
            ("8. 队徽插入失败根因定位", self.locate_logo_insertion_root_cause)
        ]
        
        for step_name, analysis_func in analysis_steps:
            print(f"\n{step_name}")
            print("-" * 60)
            try:
                result = analysis_func()
                self.analysis_results[step_name] = result
                if result.get('success'):
                    print(f"✅ {step_name} - 完成")
                    if result.get('findings'):
                        for finding in result['findings']:
                            print(f"   🔍 {finding}")
                else:
                    print(f"❌ {step_name} - 发现问题: {result.get('issue', '未知问题')}")
                    if result.get('details'):
                        for detail in result['details']:
                            print(f"   ⚠️ {detail}")
            except Exception as e:
                print(f"❌ {step_name} - 异常: {e}")
                self.analysis_results[step_name] = {'success': False, 'error': str(e)}
                import traceback
                traceback.print_exc()
        
        # 生成深度分析报告
        self.generate_deep_analysis_report()

    def analyze_word_generation_data_flow(self):
        """分析Word生成数据流"""
        
        try:
            from services.fashion_workflow_service import FashionWorkflowService
            
            # 创建工作流程服务
            self.workflow_service = FashionWorkflowService()
            
            # 生成测试队徽和数据
            logo_path = self.workflow_service._auto_generate_team_logo(self.test_team_name)
            
            if not logo_path:
                return {
                    'success': False,
                    'issue': '队徽生成失败',
                    'details': ['无法生成测试队徽进行分析']
                }
            
            # 获取球队数据
            team_data = self.workflow_service.team_service.load_team_data_for_user(
                self.workflow_service.user_id, self.test_team_name
            )
            
            findings = []
            findings.append(f"队徽文件路径: {logo_path}")
            findings.append(f"队徽文件存在: {os.path.exists(logo_path)}")
            
            if team_data:
                findings.append(f"球队数据存在: True")
                findings.append(f"球队数据中logo_path: {team_data.get('logo_path', 'None')}")
                
                if 'team_info' in team_data:
                    team_info_logo = team_data['team_info'].get('logo_path', 'None')
                    findings.append(f"team_info中logo_path: {team_info_logo}")
                
                # 检查数据传递到Word生成的路径
                logo_in_root = 'logo_path' in team_data
                logo_in_team_info = 'team_info' in team_data and 'logo_path' in team_data['team_info']
                
                findings.append(f"根级别包含logo_path: {logo_in_root}")
                findings.append(f"team_info包含logo_path: {logo_in_team_info}")
            else:
                findings.append(f"球队数据存在: False")
            
            return {
                'success': True,
                'findings': findings,
                'logo_path': logo_path,
                'team_data': team_data
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def analyze_logo_path_transmission(self):
        """分析队徽路径传递"""
        
        try:
            # 获取前一步的数据
            prev_result = self.analysis_results.get("1. Word生成数据流分析", {})
            logo_path = prev_result.get('logo_path')
            team_data = prev_result.get('team_data')
            
            if not logo_path or not team_data:
                return {
                    'success': False,
                    'issue': '缺少前置数据',
                    'details': ['需要先完成数据流分析']
                }
            
            findings = []
            
            # 模拟Word生成调用
            mock_player_mapping = {
                'ai_player_1': 'temp_files/mock_player_1.jpg'
            }
            
            # 检查_auto_generate_word_document方法的调用
            findings.append("模拟Word生成调用...")
            
            # 检查传递给Word生成的参数
            findings.append(f"传递的team_name: {self.test_team_name}")
            findings.append(f"传递的logo_path: {logo_path}")
            findings.append(f"传递的player_mapping: {len(mock_player_mapping)} 个球员")
            
            # 检查Word生成方法内部的logo_path处理
            findings.append("检查Word生成方法内部处理...")
            
            # 模拟调用Word生成
            try:
                word_result = self.workflow_service._auto_generate_word_document(
                    self.test_team_name,
                    mock_player_mapping,
                    logo_path
                )
                
                findings.append(f"Word生成调用结果: {word_result.get('success', False)}")
                if not word_result.get('success'):
                    findings.append(f"Word生成失败原因: {word_result.get('error', '未知')}")
                
            except Exception as e:
                findings.append(f"Word生成调用异常: {e}")
            
            return {
                'success': True,
                'findings': findings,
                'word_generation_attempted': True
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def analyze_word_template_logo_placeholder(self):
        """分析Word模板队徽占位符"""
        
        try:
            findings = []
            
            # 查找Word模板文件
            template_locations = [
                'word_zc',
                'streamlit_team_management_modular/templates',
                'templates'
            ]
            
            template_files = []
            for location in template_locations:
                if os.path.exists(location):
                    for root, dirs, files in os.walk(location):
                        for file in files:
                            if file.endswith('.docx'):
                                template_files.append(os.path.join(root, file))
            
            findings.append(f"找到Word模板文件: {len(template_files)} 个")
            
            for template_file in template_files:
                findings.append(f"模板文件: {template_file}")
                
                # 检查模板文件大小和修改时间
                try:
                    stat = os.stat(template_file)
                    findings.append(f"  文件大小: {stat.st_size/1024:.1f}KB")
                    findings.append(f"  修改时间: {datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')}")
                except Exception as e:
                    findings.append(f"  文件信息获取失败: {e}")
            
            # 检查是否有队徽相关的占位符
            findings.append("检查队徽占位符...")
            
            # 这里可以添加更详细的模板分析
            # 但由于是Word文档，需要特殊的库来解析
            findings.append("注意: Word模板中应该包含队徽占位符，如 {{logo}} 或类似标记")
            
            return {
                'success': True,
                'findings': findings,
                'template_files': template_files
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def analyze_java_logo_processing(self):
        """分析Java程序队徽处理"""
        
        try:
            findings = []
            
            # 查找Java相关文件
            java_locations = [
                'word_zc',
                'word_zc/ai-football-generator'
            ]
            
            java_files = []
            for location in java_locations:
                if os.path.exists(location):
                    for root, dirs, files in os.walk(location):
                        for file in files:
                            if file.endswith('.java'):
                                java_files.append(os.path.join(root, file))
            
            findings.append(f"找到Java源文件: {len(java_files)} 个")
            
            # 查找JAR文件
            jar_files = []
            for location in java_locations:
                if os.path.exists(location):
                    for root, dirs, files in os.walk(location):
                        for file in files:
                            if file.endswith('.jar'):
                                jar_files.append(os.path.join(root, file))
            
            findings.append(f"找到JAR文件: {len(jar_files)} 个")
            
            # 检查Java文件中的队徽处理逻辑
            logo_related_files = []
            for java_file in java_files:
                try:
                    with open(java_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if 'logo' in content.lower() or 'image' in content.lower():
                            logo_related_files.append(java_file)
                except Exception:
                    pass
            
            findings.append(f"包含队徽/图像处理的Java文件: {len(logo_related_files)} 个")
            for file in logo_related_files:
                findings.append(f"  - {file}")
            
            # 检查WordGeneratorService中的JAR路径配置
            try:
                from word_generator_service import WordGeneratorService
                findings.append("WordGeneratorService类可导入")
            except ImportError as e:
                findings.append(f"WordGeneratorService导入失败: {e}")
            
            return {
                'success': True,
                'findings': findings,
                'java_files': java_files,
                'jar_files': jar_files,
                'logo_related_files': logo_related_files
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def analyze_word_generation_json(self):
        """分析Word生成JSON数据"""
        
        try:
            findings = []
            
            # 获取前面的数据
            data_flow_result = self.analysis_results.get("1. Word生成数据流分析", {})
            logo_path = data_flow_result.get('logo_path')
            team_data = data_flow_result.get('team_data')
            
            if not logo_path or not team_data:
                return {
                    'success': False,
                    'issue': '缺少测试数据',
                    'details': ['需要先完成数据流分析']
                }
            
            # 模拟WordGeneratorService的数据准备过程
            findings.append("模拟WordGeneratorService数据准备...")
            
            try:
                # 导入WordGeneratorService
                sys.path.append('streamlit_team_management_modular')
                from word_generator_service import WordGeneratorService
                
                # 创建模拟的球员数据
                mock_players_data = [
                    {
                        'name': '队员1',
                        'jersey_number': '1',
                        'photo': 'temp_files/mock_player_1.jpg'
                    }
                ]
                
                # 创建WordGeneratorService实例（模拟）
                # 注意：这里可能会因为JAR路径等问题失败，但我们主要关注数据准备
                findings.append("检查数据准备逻辑...")
                
                # 模拟_prepare_json_data方法
                team_name = team_data.get('name', self.test_team_name)
                
                mock_json_data = {
                    "teamInfo": {
                        "title": f"{team_name}报名表",
                        "organizationName": team_name,
                        "teamLeader": team_data.get('leader', ''),
                        "coach": team_data.get('coach', ''),
                        "teamDoctor": team_data.get('team_doctor', ''),
                        "contactPerson": team_data.get('contact_person', ''),
                        "contactPhone": team_data.get('contact_phone', ''),
                        "logoPath": logo_path  # 关键：队徽路径
                    },
                    "players": []
                }
                
                findings.append(f"模拟JSON数据中logoPath: {mock_json_data['teamInfo']['logoPath']}")
                findings.append(f"logoPath是否为空: {not mock_json_data['teamInfo']['logoPath']}")
                findings.append(f"logoPath文件是否存在: {os.path.exists(mock_json_data['teamInfo']['logoPath']) if mock_json_data['teamInfo']['logoPath'] else False}")
                
                # 检查路径格式
                if logo_path:
                    findings.append(f"队徽路径格式: {logo_path}")
                    findings.append(f"是否为绝对路径: {os.path.isabs(logo_path)}")
                    findings.append(f"路径分隔符: {'\\\\' if '\\\\' in logo_path else '/' if '/' in logo_path else '未知'}")
                
                return {
                    'success': True,
                    'findings': findings,
                    'mock_json_data': mock_json_data
                }
                
            except ImportError as e:
                findings.append(f"WordGeneratorService导入失败: {e}")
                return {
                    'success': False,
                    'issue': 'WordGeneratorService不可用',
                    'details': findings
                }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def analyze_logo_file_accessibility(self):
        """分析队徽文件访问性"""
        
        try:
            findings = []
            
            # 获取队徽路径
            data_flow_result = self.analysis_results.get("1. Word生成数据流分析", {})
            logo_path = data_flow_result.get('logo_path')
            
            if not logo_path:
                return {
                    'success': False,
                    'issue': '没有队徽路径',
                    'details': ['需要先生成队徽']
                }
            
            findings.append(f"分析队徽文件: {logo_path}")
            
            # 检查文件存在性
            file_exists = os.path.exists(logo_path)
            findings.append(f"文件存在: {file_exists}")
            
            if file_exists:
                # 检查文件属性
                stat = os.stat(logo_path)
                findings.append(f"文件大小: {stat.st_size/1024:.1f}KB")
                findings.append(f"修改时间: {datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 检查文件权限
                readable = os.access(logo_path, os.R_OK)
                findings.append(f"文件可读: {readable}")
                
                # 检查文件格式
                file_ext = os.path.splitext(logo_path)[1].lower()
                findings.append(f"文件扩展名: {file_ext}")
                
                # 尝试用PIL打开图片
                try:
                    from PIL import Image
                    with Image.open(logo_path) as img:
                        findings.append(f"图片尺寸: {img.size}")
                        findings.append(f"图片模式: {img.mode}")
                        findings.append(f"图片格式: {img.format}")
                except Exception as e:
                    findings.append(f"图片打开失败: {e}")
                
                # 检查路径中的特殊字符
                if '\\' in logo_path:
                    findings.append("路径包含反斜杠（Windows风格）")
                if ' ' in logo_path:
                    findings.append("路径包含空格")
                if any(ord(c) > 127 for c in logo_path):
                    findings.append("路径包含非ASCII字符（中文等）")
                
                # 转换为绝对路径
                abs_path = os.path.abspath(logo_path)
                findings.append(f"绝对路径: {abs_path}")
                
                # 检查Java程序是否能访问这个路径
                findings.append("检查Java程序访问性...")
                
                # 检查路径是否在项目目录内
                current_dir = os.getcwd()
                if abs_path.startswith(current_dir):
                    findings.append("文件在项目目录内")
                else:
                    findings.append("文件在项目目录外")
            
            return {
                'success': True,
                'findings': findings,
                'file_accessible': file_exists and os.access(logo_path, os.R_OK) if file_exists else False
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def trace_complete_word_generation_flow(self):
        """追踪完整Word生成流程"""
        
        try:
            findings = []
            
            # 获取前面的数据
            data_flow_result = self.analysis_results.get("1. Word生成数据流分析", {})
            logo_path = data_flow_result.get('logo_path')
            
            if not logo_path:
                return {
                    'success': False,
                    'issue': '缺少队徽路径',
                    'details': ['需要先完成数据流分析']
                }
            
            findings.append("追踪完整Word生成流程...")
            
            # 步骤1: 检查工作流程服务调用
            findings.append("步骤1: 工作流程服务调用")
            findings.append(f"  - 调用_auto_generate_word_document")
            findings.append(f"  - 传入team_name: {self.test_team_name}")
            findings.append(f"  - 传入logo_path: {logo_path}")
            
            # 步骤2: 检查球队数据加载
            findings.append("步骤2: 球队数据加载")
            team_data = self.workflow_service.team_service.load_team_data_for_user(
                self.workflow_service.user_id, self.test_team_name
            )
            findings.append(f"  - 球队数据加载: {'成功' if team_data else '失败'}")
            
            if team_data:
                # 步骤3: 检查队徽路径添加
                findings.append("步骤3: 队徽路径添加到球队数据")
                logo_added = 'logo_path' in team_data
                findings.append(f"  - logo_path已添加: {logo_added}")
                if logo_added:
                    findings.append(f"  - logo_path值: {team_data['logo_path']}")
                
                # 步骤4: 检查球员数据准备
                findings.append("步骤4: 球员数据准备")
                players = team_data.get('players', [])
                findings.append(f"  - 球员数量: {len(players)}")
                
                valid_players = [p for p in players if p.get('name') and p.get('jersey_number')]
                findings.append(f"  - 有效球员: {len(valid_players)}")
                
                # 步骤5: 检查Word生成服务调用
                findings.append("步骤5: Word生成服务调用")
                
                try:
                    # 查找WordGeneratorService的配置
                    findings.append("  - 查找Word生成配置...")
                    
                    # 检查JAR文件路径
                    jar_locations = [
                        'word_zc/ai-football-generator/target',
                        'word_zc/ai-football-generator',
                        'word_zc'
                    ]
                    
                    jar_found = False
                    for location in jar_locations:
                        if os.path.exists(location):
                            for file in os.listdir(location):
                                if file.endswith('.jar'):
                                    jar_path = os.path.join(location, file)
                                    findings.append(f"  - 找到JAR文件: {jar_path}")
                                    jar_found = True
                    
                    if not jar_found:
                        findings.append("  - 未找到JAR文件")
                    
                    # 检查模板文件
                    template_locations = ['word_zc', 'templates']
                    template_found = False
                    for location in template_locations:
                        if os.path.exists(location):
                            for root, dirs, files in os.walk(location):
                                for file in files:
                                    if file.endswith('.docx'):
                                        template_path = os.path.join(root, file)
                                        findings.append(f"  - 找到模板文件: {template_path}")
                                        template_found = True
                    
                    if not template_found:
                        findings.append("  - 未找到模板文件")
                    
                except Exception as e:
                    findings.append(f"  - Word生成配置检查异常: {e}")
            
            return {
                'success': True,
                'findings': findings,
                'flow_traced': True
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def locate_logo_insertion_root_cause(self):
        """定位队徽插入失败根因"""
        
        try:
            findings = []
            root_causes = []
            
            findings.append("综合分析所有检查结果...")
            
            # 分析各个步骤的结果
            data_flow_ok = self.analysis_results.get("1. Word生成数据流分析", {}).get('success', False)
            path_transmission_ok = self.analysis_results.get("2. 队徽路径传递分析", {}).get('success', False)
            template_ok = self.analysis_results.get("3. Word模板队徽占位符分析", {}).get('success', False)
            java_ok = self.analysis_results.get("4. Java程序队徽处理分析", {}).get('success', False)
            json_ok = self.analysis_results.get("5. Word生成JSON数据分析", {}).get('success', False)
            file_ok = self.analysis_results.get("6. 队徽文件访问性分析", {}).get('success', False)
            flow_ok = self.analysis_results.get("7. Word生成完整流程追踪", {}).get('success', False)
            
            findings.append(f"数据流分析: {'✅' if data_flow_ok else '❌'}")
            findings.append(f"路径传递分析: {'✅' if path_transmission_ok else '❌'}")
            findings.append(f"模板分析: {'✅' if template_ok else '❌'}")
            findings.append(f"Java处理分析: {'✅' if java_ok else '❌'}")
            findings.append(f"JSON数据分析: {'✅' if json_ok else '❌'}")
            findings.append(f"文件访问性分析: {'✅' if file_ok else '❌'}")
            findings.append(f"流程追踪: {'✅' if flow_ok else '❌'}")
            
            # 根据分析结果确定根因
            if not data_flow_ok:
                root_causes.append("数据流问题：队徽生成或球队数据创建失败")
            
            if not file_ok:
                root_causes.append("文件访问问题：队徽文件不存在或不可访问")
            
            if not json_ok:
                root_causes.append("数据准备问题：JSON数据中队徽路径缺失或错误")
            
            if not java_ok:
                root_causes.append("Java环境问题：JAR文件或Java程序不可用")
            
            # 检查具体的失败点
            json_result = self.analysis_results.get("5. Word生成JSON数据分析", {})
            if json_result.get('issue') == 'WordGeneratorService不可用':
                root_causes.append("关键问题：WordGeneratorService无法导入或初始化")
            
            # 检查Word生成调用结果
            path_result = self.analysis_results.get("2. 队徽路径传递分析", {})
            if path_result.get('word_generation_attempted'):
                root_causes.append("Word生成调用失败：可能是数据验证或Java程序问题")
            
            # 最可能的根因分析
            if root_causes:
                findings.append(f"\n🔍 发现的根因:")
                for i, cause in enumerate(root_causes, 1):
                    findings.append(f"  {i}. {cause}")
            else:
                findings.append("未发现明确的根因，需要进一步调查")
            
            # 提供修复建议
            suggestions = []
            if "WordGeneratorService不可用" in str(root_causes):
                suggestions.append("检查WordGeneratorService的导入路径和依赖")
            if "JAR文件" in str(root_causes):
                suggestions.append("确保Java项目已编译并生成JAR文件")
            if "文件访问" in str(root_causes):
                suggestions.append("检查队徽文件的路径和权限")
            if "数据验证" in str(root_causes):
                suggestions.append("检查传递给Java程序的数据格式")
            
            if suggestions:
                findings.append(f"\n💡 修复建议:")
                for i, suggestion in enumerate(suggestions, 1):
                    findings.append(f"  {i}. {suggestion}")
            
            return {
                'success': True,
                'findings': findings,
                'root_causes': root_causes,
                'suggestions': suggestions
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def generate_deep_analysis_report(self):
        """生成深度分析报告"""
        
        print(f"\n📋 企业级队徽Word插入深度分析报告")
        print("=" * 80)
        
        # 获取根因分析结果
        root_cause_result = self.analysis_results.get("8. 队徽插入失败根因定位", {})
        
        if root_cause_result.get('success'):
            root_causes = root_cause_result.get('root_causes', [])
            suggestions = root_cause_result.get('suggestions', [])
            
            if root_causes:
                print("🔍 发现的根本原因:")
                for i, cause in enumerate(root_causes, 1):
                    print(f"   {i}. {cause}")
            
            if suggestions:
                print(f"\n💡 修复建议:")
                for i, suggestion in enumerate(suggestions, 1):
                    print(f"   {i}. {suggestion}")
        
        # 统计分析结果
        total_analyses = len(self.analysis_results)
        successful_analyses = sum(1 for result in self.analysis_results.values() if result.get('success'))
        
        print(f"\n📊 分析统计:")
        print(f"   总分析项: {total_analyses}")
        print(f"   成功完成: {successful_analyses}")
        print(f"   完成率: {successful_analyses/total_analyses*100:.1f}%")
        
        print(f"\n⏰ 分析完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 保存详细分析报告
        self.save_analysis_report()

    def save_analysis_report(self):
        """保存分析报告"""
        
        report_data = {
            'analysis_time': datetime.now().isoformat(),
            'test_team_name': self.test_team_name,
            'analysis_results': self.analysis_results,
            'summary': {
                'total_analyses': len(self.analysis_results),
                'successful_analyses': sum(1 for result in self.analysis_results.values() if result.get('success')),
                'root_causes': self.analysis_results.get("8. 队徽插入失败根因定位", {}).get('root_causes', []),
                'suggestions': self.analysis_results.get("8. 队徽插入失败根因定位", {}).get('suggestions', [])
            }
        }
        
        report_file = f"logo_word_insertion_deep_analysis_report_{int(time.time())}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"📄 详细分析报告已保存: {report_file}")

if __name__ == "__main__":
    analyzer = LogoWordInsertionAnalyzer()
    analyzer.run_deep_analysis()
