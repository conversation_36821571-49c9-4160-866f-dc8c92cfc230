#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI图像生成服务的日志输出
Test AI Image Generation Service Log Output
"""

import os
import re

def analyze_ai_image_generation_service():
    """分析AI图像生成服务的日志输出"""
    print("🔍 分析AI图像生成服务的日志输出")
    print("=" * 60)
    
    file_path = "streamlit_team_management_modular/services/ai_image_generation_service.py"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # 查找所有streamlit输出
        st_outputs = []
        for i, line in enumerate(lines, 1):
            if re.search(r'st\.(info|success|error|warning)\(', line):
                st_outputs.append({
                    'line': i,
                    'content': line.strip(),
                    'type': extract_st_type(line),
                    'message': extract_message(line)
                })
        
        print(f"📊 找到 {len(st_outputs)} 个streamlit输出语句:")
        
        # 按类型分组
        by_type = {}
        for output in st_outputs:
            output_type = output['type']
            if output_type not in by_type:
                by_type[output_type] = []
            by_type[output_type].append(output)
        
        for output_type, outputs in by_type.items():
            print(f"\n📋 {output_type.upper()} 输出 ({len(outputs)} 个):")
            for output in outputs:
                print(f"   第{output['line']}行: {output['message']}")
        
        return st_outputs
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return []

def extract_st_type(line):
    """提取streamlit输出类型"""
    match = re.search(r'st\.(\w+)', line)
    return match.group(1) if match else 'unknown'

def extract_message(line):
    """提取消息内容"""
    # 尝试提取f字符串或普通字符串
    patterns = [
        r'st\.\w+\(f?"([^"]*)"',
        r"st\.\w+\(f?'([^']*)'",
        r'st\.\w+\(f?"""([^"]*)"""',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, line)
        if match:
            return match.group(1)
    
    return line.strip()

def identify_problematic_outputs():
    """识别问题输出"""
    print(f"\n🎯 识别问题输出")
    print("=" * 60)
    
    # 从用户截图中看到的具体输出
    screenshot_outputs = [
        "🎨 正在调用302.ai OpenAI格式API生成队徽...",
        "📡 API响应状态: {response.status_code}",
        "📋 API响应数据: {result}"
    ]
    
    file_path = "streamlit_team_management_modular/services/ai_image_generation_service.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        print("🔍 找到的问题输出:")
        for target in screenshot_outputs:
            for i, line in enumerate(lines, 1):
                if target.replace("{response.status_code}", "").replace("{result}", "") in line:
                    print(f"   第{i}行: {line.strip()}")
                    print(f"   🎯 这就是用户看到的输出！")
        
        return True
        
    except Exception as e:
        print(f"❌ 识别失败: {e}")
        return False

def find_method_structure():
    """查找方法结构"""
    print(f"\n🔍 查找方法结构")
    print("=" * 60)
    
    file_path = "streamlit_team_management_modular/services/ai_image_generation_service.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        methods = []
        for i, line in enumerate(lines, 1):
            if re.match(r'\s*def\s+\w+', line):
                method_name = re.search(r'def\s+(\w+)', line).group(1)
                methods.append({
                    'name': method_name,
                    'line': i,
                    'content': line.strip()
                })
        
        print(f"📋 找到 {len(methods)} 个方法:")
        for method in methods:
            print(f"   第{method['line']}行: {method['name']}()")
        
        # 查找包含问题输出的方法
        print(f"\n🎯 包含问题输出的方法:")
        problem_lines = [118, 121, 125]  # 从之前的分析得出
        
        for line_num in problem_lines:
            for method in methods:
                if method['line'] < line_num:
                    current_method = method['name']
            print(f"   第{line_num}行 -> {current_method}() 方法")
        
        return methods
        
    except Exception as e:
        print(f"❌ 查找失败: {e}")
        return []

def generate_fix_recommendations():
    """生成修复建议"""
    print(f"\n💡 修复建议")
    print("=" * 60)
    
    print("🎯 问题根源:")
    print("- ai_image_generation_service.py 中的 _generate_with_openai_format() 方法")
    print("- 第118行: st.info(f'🎨 正在调用302.ai OpenAI格式API生成队徽...')")
    print("- 第121行: st.info(f'📡 API响应状态: {response.status_code}')")
    print("- 第125行: st.info(f'📋 API响应数据: {result}')")
    
    print(f"\n🔧 修复方案:")
    print("方案1: 添加verbose参数控制")
    print("```python")
    print("def _generate_with_openai_format(self, prompt: str, team_name: str, verbose: bool = False):")
    print("    if verbose:")
    print("        st.info(f'🎨 正在调用302.ai OpenAI格式API生成队徽...')")
    print("    # API调用逻辑...")
    print("    if verbose:")
    print("        st.info(f'📡 API响应状态: {response.status_code}')")
    print("        st.info(f'📋 API响应数据: {result}')")
    print("```")
    
    print(f"\n方案2: 使用st.spinner()包装")
    print("```python")
    print("def _generate_with_openai_format(self, prompt: str, team_name: str):")
    print("    with st.spinner('🎨 正在生成队徽...'):")
    print("        # API调用逻辑，移除所有中间输出")
    print("        response = requests.post(...)")
    print("        result = response.json()")
    print("    # 只显示最终结果")
    print("    if success:")
    print("        st.success('✅ 队徽生成成功')")
    print("```")
    
    print(f"\n方案3: 使用日志级别控制")
    print("```python")
    print("def _log(self, message, level='info', show=True):")
    print("    if not show:")
    print("        return")
    print("    if level == 'info':")
    print("        st.info(message)")
    print("    # 使用: self._log('API调用中...', show=self.verbose)")
    print("```")

def check_other_methods():
    """检查其他方法的输出"""
    print(f"\n🔍 检查其他方法的输出")
    print("=" * 60)
    
    file_path = "streamlit_team_management_modular/services/ai_image_generation_service.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找所有方法及其输出
        methods_with_outputs = {}
        current_method = None
        
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            # 检查是否是方法定义
            if re.match(r'\s*def\s+\w+', line):
                method_match = re.search(r'def\s+(\w+)', line)
                if method_match:
                    current_method = method_match.group(1)
                    methods_with_outputs[current_method] = []
            
            # 检查是否是streamlit输出
            elif current_method and re.search(r'st\.(info|success|error|warning)\(', line):
                methods_with_outputs[current_method].append({
                    'line': i,
                    'content': line.strip()
                })
        
        print("📊 各方法的输出统计:")
        for method, outputs in methods_with_outputs.items():
            if outputs:
                print(f"\n📋 {method}() - {len(outputs)} 个输出:")
                for output in outputs[:3]:  # 只显示前3个
                    print(f"   第{output['line']}行: {output['content'][:80]}...")
                if len(outputs) > 3:
                    print(f"   ... 还有 {len(outputs)-3} 个输出")
        
        return methods_with_outputs
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return {}

def main():
    """主函数"""
    print("🔧 AI图像生成服务日志输出分析")
    print("=" * 60)
    
    # 1. 分析所有输出
    st_outputs = analyze_ai_image_generation_service()
    
    # 2. 识别问题输出
    identify_problematic_outputs()
    
    # 3. 查找方法结构
    methods = find_method_structure()
    
    # 4. 检查其他方法
    methods_with_outputs = check_other_methods()
    
    # 5. 生成修复建议
    generate_fix_recommendations()
    
    print(f"\n🎯 分析完成！")
    print(f"📊 总结:")
    print(f"- 找到 {len(st_outputs)} 个streamlit输出语句")
    print(f"- 主要问题在 _generate_with_openai_format() 方法")
    print(f"- 需要修改第118、121、125行的输出")
    print(f"- 建议使用st.spinner()方案")

if __name__ == "__main__":
    main()
