#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接调试"自动填充"问题
"""

import os
import sys
import json
import tempfile
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from word_generator_service import WordGeneratorService
from config.settings import app_settings

def test_direct_word_generation():
    """直接测试Word生成，看看"自动填充"从哪里来"""
    print("=" * 60)
    print("🔍 直接测试Word生成")
    print("=" * 60)
    
    # 创建测试数据 - 确保没有"自动填充"字符串
    team_data = {
        "name": "直接测试队",
        "contact_person": "李小明",
        "contact_phone": "13912345678",
        "leader": "李小明",
        "coach": "李小明", 
        "doctor": "李小明",
        "jersey_color": "粉色",
        "shorts_color": "黑色",
        "socks_color": "粉色",
        "goalkeeper_kit_color": "绿色"
    }
    
    players_data = [
        {"name": "球员1", "jersey_number": "1", "photo": ""},
        {"name": "球员2", "jersey_number": "2", "photo": ""}
    ]
    
    print(f"📄 输入的team_data:")
    for key, value in team_data.items():
        print(f"   {key}: '{value}'")
    
    # 检查输入数据中是否有"自动填充"
    auto_fill_in_input = []
    for key, value in team_data.items():
        if value == "自动填充":
            auto_fill_in_input.append(key)
    
    if auto_fill_in_input:
        print(f"❌ 输入数据中发现'自动填充': {auto_fill_in_input}")
    else:
        print(f"✅ 输入数据中无'自动填充'字符串")
    
    try:
        # 创建WordGeneratorService
        paths = app_settings.word_generator.get_absolute_paths("debug_test", app_settings.paths)
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        print(f"\n📄 Word服务配置:")
        print(f"   JAR路径: {paths['jar_path']}")
        print(f"   模板路径: {paths['template_path']}")
        print(f"   输出目录: {paths['output_dir']}")
        
        # 检查_prepare_json_data方法的输出
        json_data = word_service._prepare_json_data(team_data, players_data)
        
        print(f"\n📄 准备的JSON数据:")
        team_info = json_data.get("teamInfo", {})
        for key, value in team_info.items():
            print(f"   {key}: '{value}'")
        
        # 检查JSON数据中是否有"自动填充"
        auto_fill_in_json = []
        for key, value in team_info.items():
            if value == "自动填充":
                auto_fill_in_json.append(key)
        
        if auto_fill_in_json:
            print(f"❌ JSON数据中发现'自动填充': {auto_fill_in_json}")
        else:
            print(f"✅ JSON数据中无'自动填充'字符串")
        
        # 生成Word文档
        print(f"\n🚀 生成Word文档...")
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            output_file = result['file_path']
            print(f"✅ Word生成成功: {os.path.basename(output_file)}")
            
            # 检查生成的Word文档内容
            return check_word_content_for_auto_fill(output_file)
        else:
            print(f"❌ Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_word_content_for_auto_fill(docx_path):
    """检查Word文档中是否有"自动填充"字符串"""
    print(f"\n📄 检查Word文档内容中的'自动填充':")
    
    try:
        import zipfile
        
        with zipfile.ZipFile(docx_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        # 查找"自动填充"字符串
        auto_fill_count = content.count("自动填充")
        
        if auto_fill_count > 0:
            print(f"❌ 在Word文档中发现 {auto_fill_count} 个'自动填充'字符串")
            
            # 查找上下文
            import re
            pattern = r'.{0,50}自动填充.{0,50}'
            matches = re.findall(pattern, content)
            
            print(f"📄 '自动填充'出现的上下文:")
            for i, match in enumerate(matches[:5]):  # 只显示前5个
                print(f"   {i+1}. {match}")
            
            return False
        else:
            print(f"✅ Word文档中未发现'自动填充'字符串")
            
            # 检查颜色是否正确显示
            colors_to_check = ["粉色", "黑色", "绿色"]
            colors_found = []
            
            for color in colors_to_check:
                if color in content:
                    colors_found.append(color)
            
            print(f"🎨 颜色检查:")
            for color in colors_to_check:
                status = "✅" if color in colors_found else "❌"
                print(f"   {status} {color}")
            
            return len(colors_found) == len(colors_to_check)
            
    except Exception as e:
        print(f"❌ 检查Word内容失败: {e}")
        return False

def test_template_file():
    """测试模板文件本身是否包含自动填充字符串"""
    print(f"\n" + "=" * 60)
    print("🔍 检查模板文件")
    print("=" * 60)
    
    try:
        paths = app_settings.word_generator.get_absolute_paths("template_test", app_settings.paths)
        template_path = paths['template_path']
        
        print(f"📄 模板文件: {template_path}")
        
        if not os.path.exists(template_path):
            print(f"❌ 模板文件不存在")
            return False
        
        import zipfile
        
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        # 查找"自动填充"字符串
        auto_fill_count = content.count("自动填充")
        
        if auto_fill_count > 0:
            print(f"❌ 模板文件中发现 {auto_fill_count} 个'自动填充'字符串")
            
            # 查找上下文
            import re
            pattern = r'.{0,50}自动填充.{0,50}'
            matches = re.findall(pattern, content)
            
            print(f"📄 模板中'自动填充'出现的位置:")
            for i, match in enumerate(matches[:3]):
                print(f"   {i+1}. {match}")
            
            print(f"\n💡 这说明问题在模板文件中!")
            print(f"   模板文件本身包含'自动填充'文本")
            print(f"   需要修改模板文件，将'自动填充'替换为占位符")
            
            return False
        else:
            print(f"✅ 模板文件中未发现'自动填充'字符串")
            
            # 检查占位符
            placeholders = ["{{jerseyColor}}", "{{shortsColor}}", "{{socksColor}}", "{{goalkeeperKitColor}}"]
            placeholders_found = []
            
            for placeholder in placeholders:
                if placeholder in content:
                    placeholders_found.append(placeholder)
            
            print(f"📄 占位符检查:")
            for placeholder in placeholders:
                status = "✅" if placeholder in placeholders_found else "❌"
                print(f"   {status} {placeholder}")
            
            return len(placeholders_found) == len(placeholders)
            
    except Exception as e:
        print(f"❌ 检查模板文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 调试'自动填充'问题")
    print(f"调试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 检查模板文件
        template_ok = test_template_file()
        
        # 2. 直接测试Word生成
        word_ok = test_direct_word_generation()
        
        print("\n" + "=" * 60)
        print("📋 调试结果总结")
        print("=" * 60)
        
        print(f"📊 测试结果:")
        print(f"   模板文件检查: {'✅ 正常' if template_ok else '❌ 有问题'}")
        print(f"   Word生成测试: {'✅ 正常' if word_ok else '❌ 有问题'}")
        
        if not template_ok:
            print(f"\n🎯 问题根源: 模板文件")
            print(f"   模板文件中包含'自动填充'文本")
            print(f"   需要手动编辑模板文件，将'自动填充'替换为占位符")
            
            print(f"\n💡 解决方案:")
            print(f"   1. 打开模板文件 (Word文档)")
            print(f"   2. 查找所有'自动填充'文本")
            print(f"   3. 替换为对应的占位符:")
            print(f"      - 球衣颜色处的'自动填充' → {{{{jerseyColor}}}}")
            print(f"      - 球裤颜色处的'自动填充' → {{{{shortsColor}}}}")
            print(f"      - 球袜颜色处的'自动填充' → {{{{socksColor}}}}")
            print(f"      - 守门员服装颜色处的'自动填充' → {{{{goalkeeperKitColor}}}}")
            print(f"   4. 保存模板文件")
            
        elif not word_ok:
            print(f"\n🎯 问题根源: Word生成过程")
            print(f"   数据传递或处理过程中出现问题")
            
        else:
            print(f"\n✅ 所有测试通过")
            print(f"   '自动填充'问题已解决")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
