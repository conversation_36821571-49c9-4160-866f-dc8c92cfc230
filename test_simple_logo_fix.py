#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单队徽修复测试
Simple Logo Fix Test

测试不重新编译Java包的简单修复方案
"""

import os
import sys
from datetime import datetime

# 添加项目路径
sys.path.append('streamlit_team_management_modular')

def test_simple_logo_fix():
    """测试简单队徽修复"""
    
    print("🔧 简单队徽修复测试（无需编译Java）")
    print("=" * 80)
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # 步骤1: 创建工作流程服务
        print("步骤1: 创建工作流程服务")
        print("-" * 40)
        
        from services.fashion_workflow_service import FashionWorkflowService
        workflow_service = FashionWorkflowService()
        
        print(f"✅ 工作流程服务创建成功")
        
        # 步骤2: 生成队徽
        print(f"\n步骤2: 生成队徽")
        print("-" * 40)
        
        test_team_name = "简单修复测试队"
        logo_path = workflow_service._auto_generate_team_logo(test_team_name)
        
        print(f"✅ 队徽生成: {logo_path}")
        print(f"   文件存在: {os.path.exists(logo_path)}")
        print(f"   文件大小: {os.path.getsize(logo_path)/1024:.1f}KB")
        
        # 步骤3: 创建球队数据（包含队徽路径）
        print(f"\n步骤3: 创建球队数据")
        print("-" * 40)
        
        team_data = {
            'name': test_team_name,
            'leader': '测试领队',
            'coach': '测试教练',
            'doctor': '测试队医',
            'contact_person': '测试联系人',
            'contact_phone': '13800138000',
            'logo_path': logo_path,  # 关键：队徽路径
            'players': [
                {
                    'name': '测试球员1',
                    'jersey_number': '1',
                    'photo': ''
                },
                {
                    'name': '测试球员2',
                    'jersey_number': '2', 
                    'photo': ''
                }
            ]
        }
        
        print(f"✅ 球队数据准备完成")
        print(f"   队徽路径: {team_data['logo_path']}")
        print(f"   球员数量: {len(team_data['players'])}")
        
        # 步骤4: 测试WordGeneratorService
        print(f"\n步骤4: 测试WordGeneratorService")
        print("-" * 40)
        
        from word_generator_service import WordGeneratorService
        
        # 创建WordGeneratorService实例
        jar_path = "word_zc/ai-football-generator/target/word-generator.jar"
        template_path = "word_zc/template_15players_fixed.docx"
        output_dir = "word_zc/ai-football-generator/output"

        word_service = WordGeneratorService(jar_path, template_path, output_dir)
        
        # 准备数据
        players_data = team_data['players']
        
        # 调用_prepare_json_data方法
        json_data = word_service._prepare_json_data(team_data, players_data)
        
        print(f"✅ JSON数据准备完成")
        
        # 检查队徽字段
        team_info = json_data.get('teamInfo', {})
        logo_field = team_info.get('@teamLogoPhoto', '')
        
        print(f"🔍 队徽字段检查:")
        print(f"   @teamLogoPhoto字段存在: {'@teamLogoPhoto' in team_info}")
        print(f"   @teamLogoPhoto值: {logo_field}")
        print(f"   队徽文件存在: {os.path.exists(logo_field) if logo_field else False}")
        
        # 步骤5: 生成Word文档
        print(f"\n步骤5: 生成Word文档")
        print("-" * 40)
        
        # 准备球员映射
        player_mapping = {}
        for i, player in enumerate(players_data, 1):
            ai_player_key = f"ai_player_{i}"
            player_mapping[ai_player_key] = player.get('photo', '')
        
        # 调用Word生成
        word_result = workflow_service._auto_generate_word_document(
            test_team_name,
            player_mapping,
            logo_path
        )
        
        print(f"📊 Word生成结果:")
        print(f"   成功: {word_result.get('success', False)}")
        
        if word_result.get('success'):
            word_file = word_result.get('file_path', '')
            print(f"   文件路径: {word_file}")
            
            if word_file and os.path.exists(word_file):
                file_size = os.path.getsize(word_file) / 1024
                print(f"   文件大小: {file_size:.1f}KB")
                print(f"   修改时间: {datetime.fromtimestamp(os.path.getmtime(word_file)).strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 检查文件大小
                if file_size > 50:
                    print(f"   ✅ 文件大小合理，可能包含队徽")
                else:
                    print(f"   ⚠️ 文件大小较小，可能未包含队徽")
            else:
                print(f"   ❌ Word文件不存在")
        else:
            error_msg = word_result.get('error', '未知错误')
            print(f"   ❌ 生成失败: {error_msg}")
        
        # 步骤6: 最终评估
        print(f"\n步骤6: 最终评估")
        print("-" * 40)
        
        success_criteria = {
            '队徽生成': os.path.exists(logo_path),
            '队徽字段添加': '@teamLogoPhoto' in team_info,
            '队徽路径正确': logo_field == logo_path,
            'Word生成成功': word_result.get('success', False)
        }
        
        print(f"📊 成功标准检查:")
        success_count = 0
        for criterion, passed in success_criteria.items():
            status = "✅" if passed else "❌"
            print(f"   {status} {criterion}")
            if passed:
                success_count += 1
        
        total_criteria = len(success_criteria)
        success_rate = success_count / total_criteria * 100
        
        print(f"\n🎯 最终结果:")
        print(f"   成功项: {success_count}/{total_criteria}")
        print(f"   成功率: {success_rate:.1f}%")
        
        if success_rate >= 75:
            print("🎉 简单修复成功！队徽功能已修复！")
            print("   ✅ 无需重新编译Java包")
            print("   ✅ 只修改了Python代码")
            print("   ✅ 利用了现有的模板字段")
        elif success_rate >= 50:
            print("⚠️ 部分成功，还需要一些调整")
        else:
            print("❌ 修复失败，需要其他方案")
        
        print(f"\n⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return {
            'success_rate': success_rate,
            'logo_field_added': '@teamLogoPhoto' in team_info,
            'word_generated': word_result.get('success', False),
            'word_file': word_result.get('file_path', '') if word_result.get('success') else None
        }
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = test_simple_logo_fix()
    
    if result and result['success_rate'] >= 75:
        print(f"\n🎊 简单修复方案成功！")
        print(f"📋 修复总结:")
        print(f"   ✅ 只修改了一个Python文件：word_generator_service.py")
        print(f"   ✅ 添加了一行代码：'@teamLogoPhoto': team_data.get('logo_path', '')")
        print(f"   ✅ 利用了模板中已有的队徽占位符")
        print(f"   ✅ 无需重新编译Java包")
        print(f"   ✅ 无需修改Java源代码")
        
        if result.get('word_file'):
            print(f"\n📄 生成的Word文件: {os.path.basename(result['word_file'])}")
            print(f"   请检查该文件确认队徽是否正确显示。")
    else:
        print(f"\n⚠️ 简单修复方案需要进一步调整。")
