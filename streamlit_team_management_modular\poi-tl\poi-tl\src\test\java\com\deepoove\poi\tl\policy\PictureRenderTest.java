package com.deepoove.poi.tl.policy;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.Pictures;
import com.deepoove.poi.util.BufferedImageUtils;
import com.deepoove.poi.util.ByteUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

@DisplayName("Picture Render test case")
public class PictureRenderTest {

    BufferedImage bufferImage;

    String imageBase64;

    @BeforeEach
    public void init() {
        bufferImage = BufferedImageUtils.newBufferImage(100, 100);
        Graphics2D g = (Graphics2D) bufferImage.getGraphics();
        g.setColor(Color.CYAN);
        g.fillRect(0, 0, 100, 100);
        g.setColor(Color.BLACK);
        g.drawString("Java Image", 0, 50);
        g.dispose();
        bufferImage.flush();

        StringBuilder sb = new StringBuilder();
        sb.append("data:image/png;base64,");
        sb.append(
            "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");
        imageBase64 = sb.toString();
    }

    @Test
    public void testPictureRender() throws Exception {
        Map<String, Object> datas = new HashMap<String, Object>();
        // local file path
        datas.put("localPicture", Pictures.ofLocal("src/test/resources/sayi.png").size(120, 120).create());
        // input stream
        datas.put("localBytePicture",
            Pictures.ofStream(new FileInputStream("src/test/resources/logo.png")).size(100, 120).create());
        // network url
        datas.put("urlPicture", "http://deepoove.com/images/icecream.png");
        // java bufferedImage
        datas.put("bufferImagePicture", Pictures.ofBufferedImage(bufferImage, PictureType.PNG).size(100, 100).create());
        // base64
        datas.put("base64Image", Pictures.ofBase64(imageBase64, PictureType.PNG).size(100, 100).center().create());
        // svg
        datas.put("svgPicture", Pictures.ofUrl("http://deepoove.com/images/%E8%8C%84%E5%AD%90.svg").create());
        // alt attribute for not exist image
        datas.put("image", Pictures.ofLocal("not_exist_image.png").altMeta("No Image!").create());

        XWPFTemplate.compile("src/test/resources/template/render_picture.docx")
            .render(datas)
            .writeToFile("target/out_render_picture.docx");

    }

    @Test
    public void guessPictureType() throws IOException {
        assertEquals(PictureType.SVG,
            PictureType.suggestFileType(ByteUtils.getLocalByteArray(new File("src/test/resources/picture/p.svg"))));
        assertEquals(PictureType.GIF,
            PictureType.suggestFileType(ByteUtils.getLocalByteArray(new File("src/test/resources/picture/p.gif"))));
        assertEquals(PictureType.PNG,
            PictureType.suggestFileType(ByteUtils.getLocalByteArray(new File("src/test/resources/picture/p.png"))));
        assertEquals(PictureType.JPEG,
            PictureType.suggestFileType(ByteUtils.getLocalByteArray(new File("src/test/resources/picture/p.jpg"))));
        assertEquals(PictureType.BMP,
            PictureType.suggestFileType(ByteUtils.getLocalByteArray(new File("src/test/resources/picture/p.bmp"))));
        assertEquals(PictureType.TIFF,
            PictureType.suggestFileType(ByteUtils.getLocalByteArray(new File("src/test/resources/picture/p.tif"))));
        assertEquals(PictureType.PNG,
            PictureType.suggestFileType(ByteUtils.getUrlByteArray("https://deepoove.com/images/icecream.png")));
        assertEquals(PictureType.SVG, PictureType
            .suggestFileType(ByteUtils.getUrlByteArray("https://deepoove.com/images/%E8%8C%84%E5%AD%90.svg")));
    }

    @Test
    public void svgToPngScaleTest() throws IOException {
        Map<String, Object> datas = new HashMap<>();
        Path path = Paths.get("src/test/resources/picture/svg_scale.svg");
        datas.put("beforeScale", Pictures.ofStream(Files.newInputStream(path)).size(200, 20).svgScale(1).create());
        datas.put("afterScale", Pictures.ofStream(Files.newInputStream(path)).size(200, 20).svgScale(3).create());

        XWPFTemplate.compile("src/test/resources/template/reference_picture_svg.docx")
                    .render(datas)
                    .writeToFile("target/out_render_picture_svg.docx");
    }
}
