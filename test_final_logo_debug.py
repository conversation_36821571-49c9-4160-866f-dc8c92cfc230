#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终队徽调试测试
Final Logo Debug Test

深入调试Word生成失败的具体原因
"""

import os
import sys
import json
import tempfile
import subprocess
from datetime import datetime

# 添加项目路径
sys.path.append('streamlit_team_management_modular')

def test_final_logo_debug():
    """最终队徽调试测试"""
    
    print("🔧 最终队徽调试测试")
    print("=" * 80)
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # 步骤1: 生成真实的队徽文件
        print("步骤1: 生成真实的队徽文件")
        print("-" * 40)
        
        from services.fashion_workflow_service import FashionWorkflowService
        workflow_service = FashionWorkflowService()
        
        test_team_name = "最终调试测试队"
        logo_path = workflow_service._auto_generate_team_logo(test_team_name)
        
        print(f"✅ 队徽生成: {logo_path}")
        print(f"   文件存在: {os.path.exists(logo_path)}")
        print(f"   文件大小: {os.path.getsize(logo_path)/1024:.1f}KB")
        
        # 转换为绝对路径
        abs_logo_path = os.path.abspath(logo_path)
        print(f"   绝对路径: {abs_logo_path}")
        
        # 步骤2: 创建包含真实队徽的测试数据
        print(f"\n步骤2: 创建测试数据")
        print("-" * 40)
        
        test_data = {
            "teamInfo": {
                "title": f"{test_team_name}报名表",
                "organizationName": test_team_name,
                "teamLeader": "测试领队",
                "coach": "测试教练",
                "teamDoctor": "测试队医",
                "contactPerson": "测试联系人",
                "contactPhone": "13800138000",
                "logoPath": abs_logo_path,  # 使用绝对路径
                "jerseyColor": "红色",
                "shortsColor": "蓝色",
                "socksColor": "白色",
                "goalkeeperKitColor": "绿色"
            },
            "players": [
                {
                    "name": "测试球员1",
                    "jerseyNumber": "1",
                    "jerseyColor": "红色",
                    "shortsColor": "蓝色",
                    "socksColor": "白色",
                    "photoPath": ""  # 空照片路径
                }
            ],
            "config": {
                "templatePath": os.path.abspath("word_zc/template_15players_fixed.docx"),
                "outputDir": os.path.abspath("word_zc/ai-football-generator/output"),
                "photosDir": os.path.abspath("photos")
            }
        }
        
        print(f"✅ 测试数据准备完成")
        print(f"   logoPath: {test_data['teamInfo']['logoPath']}")
        print(f"   templatePath: {test_data['config']['templatePath']}")
        print(f"   outputDir: {test_data['config']['outputDir']}")
        
        # 确保输出目录存在
        os.makedirs(test_data['config']['outputDir'], exist_ok=True)
        
        # 步骤3: 写入JSON文件
        print(f"\n步骤3: 写入JSON文件")
        print("-" * 40)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
            test_json_file = f.name
        
        print(f"✅ JSON文件: {test_json_file}")
        
        # 验证JSON文件内容
        with open(test_json_file, 'r', encoding='utf-8') as f:
            json_content = f.read()
            print(f"   JSON大小: {len(json_content)} 字符")
            if 'logoPath' in json_content:
                print("   ✅ JSON包含logoPath字段")
            else:
                print("   ❌ JSON不包含logoPath字段")
        
        # 步骤4: 调用Java程序（详细模式）
        print(f"\n步骤4: 调用Java程序（详细模式）")
        print("-" * 40)
        
        jar_path = "word_zc/ai-football-generator/target/word-generator.jar"
        cmd = ['java', '-cp', jar_path, 'CommandLineMain', test_json_file]
        
        print(f"🚀 执行命令: {' '.join(cmd)}")
        print(f"   JAR文件存在: {os.path.exists(jar_path)}")
        print(f"   JAR文件大小: {os.path.getsize(jar_path)/1024/1024:.1f}MB")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=120,
            encoding='utf-8',
            errors='ignore'
        )
        
        print(f"\n📊 Java程序执行结果:")
        print(f"   返回码: {result.returncode}")
        
        # 步骤5: 详细分析输出
        print(f"\n步骤5: 详细分析输出")
        print("-" * 40)
        
        output_text = result.stdout + result.stderr
        
        print(f"📄 完整输出:")
        for i, line in enumerate(output_text.split('\n'), 1):
            if line.strip():
                print(f"   {i:2d}: {line}")
        
        # 检查关键信息
        print(f"\n🔍 关键信息检查:")
        
        if '队徽路径=' in output_text:
            print("   ✅ 队徽路径被解析")
            for line in output_text.split('\n'):
                if '队徽路径=' in line:
                    print(f"      {line.strip()}")
        else:
            print("   ❌ 队徽路径未被解析")
        
        if 'SUCCESS:' in output_text:
            print("   ✅ Word生成成功")
        elif 'ERROR:' in output_text:
            print("   ❌ Word生成失败")
            for line in output_text.split('\n'):
                if 'ERROR:' in line:
                    print(f"      {line.strip()}")
        
        # 检查模板文件
        template_path = test_data['config']['templatePath']
        if os.path.exists(template_path):
            print(f"   ✅ 模板文件存在: {os.path.basename(template_path)}")
            print(f"      大小: {os.path.getsize(template_path)/1024:.1f}KB")
        else:
            print(f"   ❌ 模板文件不存在: {template_path}")
        
        # 步骤6: 检查输出文件
        print(f"\n步骤6: 检查输出文件")
        print("-" * 40)
        
        output_dir = test_data['config']['outputDir']
        if os.path.exists(output_dir):
            files = [f for f in os.listdir(output_dir) if f.endswith('.docx')]
            files.sort(key=lambda x: os.path.getmtime(os.path.join(output_dir, x)), reverse=True)
            
            if files:
                latest_file = files[0]
                latest_path = os.path.join(output_dir, latest_file)
                print(f"✅ 最新Word文件: {latest_file}")
                print(f"   大小: {os.path.getsize(latest_path)/1024:.1f}KB")
                print(f"   修改时间: {datetime.fromtimestamp(os.path.getmtime(latest_path)).strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 检查是否是刚生成的文件（5分钟内）
                file_age = datetime.now().timestamp() - os.path.getmtime(latest_path)
                if file_age < 300:  # 5分钟
                    print(f"   ✅ 文件是新生成的（{file_age:.0f}秒前）")
                else:
                    print(f"   ⚠️ 文件较旧（{file_age/60:.1f}分钟前）")
            else:
                print("❌ 输出目录中没有Word文件")
        else:
            print(f"❌ 输出目录不存在: {output_dir}")
        
        # 清理临时文件
        try:
            os.unlink(test_json_file)
        except:
            pass
        
        print(f"\n⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 返回结果
        return {
            'java_return_code': result.returncode,
            'logo_parsed': '队徽路径=' in output_text,
            'word_success': 'SUCCESS:' in output_text,
            'has_error': 'ERROR:' in output_text
        }
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_final_logo_debug()
