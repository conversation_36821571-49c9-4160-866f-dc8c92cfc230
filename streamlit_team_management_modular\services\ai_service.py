#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI服务
AI Service

提供AI相关的业务逻辑处理
"""

from typing import List, Dict, Any, Optional
import streamlit as st
import json
import uuid
import os
from datetime import datetime
from openai import OpenAI

from config.settings import app_settings
from config.ai_schemas import FUNCTION_DEFINITIONS
from services.enhanced_ai_assistant import enhanced_ai_assistant
from services.field_metadata import field_metadata_manager, FieldSource, FieldStatus
from services.fashion_api_service import fashion_api_service, async_fashion_api_service
# 缓存已移除以解决数据一致性问题

# 换装功能配置 - 使用新的fashion_api_service
FASHION_TRYON_AVAILABLE = True
print("✅ 换装功能导入成功")


class AIService:
    """AI服务（兼容版本，支持基础和增强功能）"""

    def __init__(self, user_id: str = None):
        self.user_id = user_id or st.session_state.get('user_id', '')
        self.client = None
        self._initialize_client()
        # 延迟导入增强服务，避免循环导入
        self._enhanced_service = None
    
    def _initialize_client(self):
        """初始化OpenAI客户端"""
        try:
            # 使用配置文件的方法获取API Key
            api_key = app_settings.ai.get_openai_api_key()

            if api_key:
                self.client = OpenAI(
                    api_key=api_key,
                    base_url=app_settings.ai.OPENAI_BASE_URL
                )
            else:
                st.warning("未配置OpenAI API密钥，AI功能将不可用")
        except Exception as e:
            st.error(f"初始化AI客户端失败: {e}")
    
    @property
    def enhanced_service(self):
        """获取增强服务实例（延迟加载）"""
        if self._enhanced_service is None:
            try:
                from services.enhanced_ai_service import EnhancedAIService
                self._enhanced_service = EnhancedAIService(self.user_id)
            except ImportError as e:
                st.warning(f"增强AI功能不可用: {e}")
                self._enhanced_service = None
        return self._enhanced_service

    def is_available(self) -> bool:
        """
        检查AI服务是否可用

        Returns:
            bool: AI服务是否可用
        """
        return self.client is not None

    def has_enhanced_features(self) -> bool:
        """检查是否支持增强功能"""
        return self.enhanced_service is not None
    
    def get_system_prompt(self, team_name: str, team_stats: Dict[str, Any] = None) -> str:
        """
        获取系统提示词

        Args:
            team_name: 球队名称
            team_stats: 球队统计信息

        Returns:
            str: 系统提示词
        """
        # 默认统计信息
        if team_stats is None:
            team_stats = {
                'total_players': 0,
                'players_with_photos': 0,
                'completion_rate': 0.0,
                'is_complete': False
            }

        # 生成智能建议
        smart_suggestions = self._generate_smart_suggestions(team_stats)

        # 生成团队状态描述
        team_status = self._get_team_status_description(team_stats)

        return app_settings.ai.SYSTEM_PROMPT_TEMPLATE.format(
            team_name=team_name,
            total_players=team_stats['total_players'],
            players_with_photos=team_stats['players_with_photos'],
            completion_rate=team_stats['completion_rate'],
            team_status=team_status,
            smart_suggestions=smart_suggestions
        )
    
    def _generate_smart_suggestions(self, team_stats: Dict[str, Any]) -> str:
        """
        根据球队状态生成智能建议

        Args:
            team_stats: 球队统计信息

        Returns:
            str: 智能建议文本
        """
        suggestions = []

        total_players = team_stats['total_players']
        players_with_photos = team_stats['players_with_photos']
        completion_rate = team_stats['completion_rate']

        if total_players == 0:
            suggestions.append("- 建议先添加球员信息，然后再收集比赛报名信息")
        elif players_with_photos == 0:
            suggestions.append("- 建议为球员上传照片，这样可以生成完整的报名表")
        elif completion_rate < 100:
            missing_photos = total_players - players_with_photos
            suggestions.append(f"- 还有{missing_photos}名球员未上传照片，建议完善后再生成报名表")
        else:
            suggestions.append("- 球员信息已完整，可以直接收集比赛信息并生成报名表")

        if total_players >= 5:
            suggestions.append("- 球员数量充足，可以参加正式比赛")
        elif total_players > 0:
            suggestions.append(f"- 当前有{total_players}名球员，建议至少准备5名球员参赛")

        return "\n".join(suggestions) if suggestions else "- 准备开始收集比赛报名信息"

    def _get_team_status_description(self, team_stats: Dict[str, Any]) -> str:
        """
        获取球队状态描述

        Args:
            team_stats: 球队统计信息

        Returns:
            str: 状态描述
        """
        total_players = team_stats['total_players']
        completion_rate = team_stats['completion_rate']

        if total_players == 0:
            return "新建球队，尚未添加球员"
        elif completion_rate == 100:
            return "球员信息完整，可生成报名表"
        elif completion_rate >= 50:
            return "球员信息基本完整，建议补充照片"
        else:
            return "球员信息不完整，需要补充照片"

    def initialize_chat_messages(self, team_name: str, team_stats: Dict[str, Any] = None) -> List[Dict[str, str]]:
        """
        初始化聊天消息

        Args:
            team_name: 球队名称
            team_stats: 球队统计信息

        Returns:
            List[Dict[str, str]]: 初始化的消息列表
        """
        system_prompt = self.get_system_prompt(team_name, team_stats)

        # 根据球队状态生成个性化的初始消息
        initial_message = self._generate_initial_message(team_name, team_stats)

        return [
            {"role": "system", "content": system_prompt},
            {
                "role": "assistant",
                "content": initial_message
            }
        ]

    def _generate_initial_message(self, team_name: str, team_stats: Dict[str, Any] = None) -> str:
        """
        生成个性化的初始消息

        Args:
            team_name: 球队名称
            team_stats: 球队统计信息

        Returns:
            str: 初始消息
        """
        if team_stats is None:
            team_stats = {
                'total_players': 0,
                'players_with_photos': 0,
                'completion_rate': 0.0,
                'is_complete': False
            }

        total_players = team_stats['total_players']
        completion_rate = team_stats['completion_rate']

        # 基础问候 - 明确说明正在为当前球队收集基本信息
        message = f"您好！我是AI智能助手，正在为球队「{team_name}」建立基本档案。\n\n"
        message += f"📋 **重要说明**：我已经知道您的球队名称是「{team_name}」，所有收集的信息都会自动保存到这个球队下。\n\n"
        message += f"🎯 **简化流程**：我只需要您提供3项核心信息，其他信息系统会智能处理：\n"
        message += f"   1️⃣ 联系人姓名（通常是队长或负责人）\n"
        message += f"   2️⃣ 联系电话（11位手机号码）\n"
        message += f"   3️⃣ 球衣主色调（如：红色、蓝色、白色等）\n\n"
        message += f"✨ **智能填充**：领队、队医等其他信息会自动填充，球裤球袜颜色会智能搭配。\n\n"

        # 根据球队状态添加个性化信息
        if total_players == 0:
            message += "🔍 我注意到您的球队还没有添加球员信息。建立基本档案后，您可以继续添加球员信息。\n\n"
        elif completion_rate < 50:
            message += f"📊 当前球队有{total_players}名球员，照片信息不完整（完成度：{completion_rate:.1f}%）。\n\n"
        elif completion_rate < 100:
            message += f"📊 当前球队有{total_players}名球员，照片信息基本完整（完成度：{completion_rate:.1f}%）。\n\n"
        else:
            message += f"✅ 太好了！您的球队有{total_players}名球员，所有信息都已完整。\n\n"

        message += "现在让我们开始建立球队档案。请先告诉我：**联系人姓名**是什么？（通常是队长或负责人的姓名）"

        return message
    
    def chat_with_ai(self, message: str, chat_history: List[Dict[str, str]], team_id: str = None) -> str:
        """
        与AI聊天 - 使用增强的AI助手

        Args:
            message: 用户消息
            chat_history: 聊天历史
            team_id: 球队ID

        Returns:
            str: AI回复
        """
        if not self.is_available():
            return "AI服务不可用，请检查配置"

        try:
            # 使用增强的AI助手处理复杂消息
            if enhanced_ai_assistant.is_available():
                response = enhanced_ai_assistant.process_complex_message(message, team_id)

                # 如果增强助手处理成功，返回结果
                if response and not response.startswith("AI服务不可用"):
                    return response

            # 回退到基础AI服务
            messages = chat_history + [{"role": "user", "content": message}]
            return self.generate_response(messages) or "抱歉，我无法处理您的请求"

        except Exception as e:
            return f"AI服务出错: {str(e)}"

    def generate_response(self, messages: List[Dict[str, str]]) -> Optional[str]:
        """
        生成AI回复

        Args:
            messages: 消息历史

        Returns:
            Optional[str]: AI回复，如果失败返回None
        """
        if not self.is_available():
            return "AI服务不可用，请检查配置"

        try:
            response = self.client.chat.completions.create(
                model=app_settings.ai.OPENAI_MODEL,
                messages=messages,
                stream=False
            )
            return response.choices[0].message.content
        except Exception as e:
            st.error(f"AI回复生成失败: {e}")
            return None
    
    def stream_response(self, messages: List[Dict[str, str]]):
        """
        流式生成AI回复
        
        Args:
            messages: 消息历史
            
        Yields:
            str: 流式回复内容
        """
        if not self.is_available():
            yield "AI服务不可用，请检查配置"
            return
        
        try:
            stream = self.client.chat.completions.create(
                model=app_settings.ai.OPENAI_MODEL,
                messages=messages,
                stream=True
            )
            
            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            st.error(f"AI流式回复失败: {e}")
            yield "回复生成失败"
    
    def extract_team_info(self, chat_history: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        从聊天历史中提取球队信息
        
        Args:
            chat_history: 聊天历史
            
        Returns:
            Dict[str, Any]: 提取的球队信息
        """
        # 这里可以实现更复杂的信息提取逻辑
        # 目前返回基本结构
        extracted_info = {
            # 必需字段
            'team_name': '',
            'contact_person': '',
            'contact_phone': '',
            'leader_name': '',
            'team_doctor': '',
            'jersey_color': '',

            # 可选字段（AI自动填充）
            'shorts_color': '',
            'socks_color': '',
            'goalkeeper_kit_color': '',
            'coach_name': '',
            'has_logo': False,
            'logo_description': '',
            'notes': '',

            'extraction_complete': False
        }
        
        # 简单的关键词匹配提取（可以改进为更智能的NLP处理）
        chat_text = ' '.join([msg['content'] for msg in chat_history if msg['role'] == 'user'])

        # 这里可以添加更复杂的信息提取逻辑
        # 例如使用正则表达式、NLP库等

        # 应用智能填充逻辑
        extracted_info = self._apply_smart_fill_logic(extracted_info)

        return extracted_info

    def _apply_smart_fill_logic(self, info: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用智能填充逻辑

        Args:
            info: 提取的信息

        Returns:
            Dict[str, Any]: 应用智能填充后的信息
        """
        # 1. 人员智能填充：一人身兼多职
        info = self._auto_fill_personnel(info)

        # 2. 服装颜色智能填充
        info = self._auto_fill_kit_colors(info)

        return info

    def _auto_fill_personnel(self, info: Dict[str, Any]) -> Dict[str, Any]:
        """
        智能填充人员信息 - 如果只提到一个人名，可以填充到多个角色

        Args:
            info: 球队信息

        Returns:
            Dict[str, Any]: 填充后的信息
        """
        # 收集所有提到的人名
        mentioned_names = []
        for field in ["contact_person", "leader_name", "team_doctor"]:
            name = info.get(field, "").strip()
            # 过滤无效值
            if name and name not in mentioned_names and name not in ["自动填充", "待定", "未知", "暂无"]:
                mentioned_names.append(name)

        # 智能填充逻辑：根据用户期望，联系人自动填充为领队和队医
        contact_person = info.get("contact_person", "").strip()

        # 如果有联系人，自动填充领队和队医
        if contact_person and contact_person not in ["自动填充", "待定", "未知", "暂无"]:
            if not info.get("leader_name", "").strip() or info.get("leader_name") in ["自动填充", "待定", "未知", "暂无"]:
                info["leader_name"] = contact_person

            if not info.get("team_doctor", "").strip() or info.get("team_doctor") in ["自动填充", "待定", "未知", "暂无"]:
                info["team_doctor"] = contact_person

        # 如果只提到一个人名，且联系人为空，则智能填充
        elif len(mentioned_names) == 1:
            single_name = mentioned_names[0]

            # 智能填充逻辑：一人多职
            if not info.get("contact_person", "").strip() or info.get("contact_person") in ["自动填充", "待定", "未知", "暂无"]:
                info["contact_person"] = single_name

            if not info.get("leader_name", "").strip() or info.get("leader_name") in ["自动填充", "待定", "未知", "暂无"]:
                info["leader_name"] = single_name

            if not info.get("team_doctor", "").strip() or info.get("team_doctor") in ["自动填充", "待定", "未知", "暂无"]:
                info["team_doctor"] = single_name

        return info

    def _auto_fill_kit_colors(self, info: Dict[str, Any]) -> Dict[str, Any]:
        """
        智能填充服装颜色

        Args:
            info: 球队信息

        Returns:
            Dict[str, Any]: 填充后的信息
        """
        jersey_color = info.get("jersey_color", "").strip()

        # 检查球衣颜色是否有效
        if jersey_color and jersey_color not in ["自动填充", "待定", "未知", "暂无"]:
            jersey_color_lower = jersey_color.lower()

            # 自动填充球裤颜色
            if not info.get("shorts_color", "").strip() or info.get("shorts_color") in ["自动填充", "待定", "未知", "暂无"]:
                # 根据球衣颜色智能选择球裤颜色
                if jersey_color_lower in ["红", "蓝", "绿", "黄", "红色", "蓝色", "绿色", "黄色", "粉色", "紫色", "橙色"]:
                    info["shorts_color"] = "黑色"
                elif jersey_color_lower in ["白", "白色"]:
                    info["shorts_color"] = "白色"
                else:
                    info["shorts_color"] = "黑色"  # 默认

            # 自动填充球袜颜色（与球衣同色）
            if not info.get("socks_color", "").strip() or info.get("socks_color") in ["自动填充", "待定", "未知", "暂无"]:
                info["socks_color"] = jersey_color

            # 自动填充守门员服装颜色
            if not info.get("goalkeeper_kit_color", "").strip() or info.get("goalkeeper_kit_color") in ["自动填充", "待定", "未知", "暂无"]:
                if jersey_color_lower in ["绿", "绿色"]:
                    info["goalkeeper_kit_color"] = "橙色"  # 避免颜色冲突
                elif jersey_color_lower in ["白", "白色"]:
                    info["goalkeeper_kit_color"] = "黄色"
                else:
                    info["goalkeeper_kit_color"] = "绿色"  # 默认

        return info

    def validate_extracted_info(self, info: Dict[str, Any]) -> List[str]:
        """
        验证提取的信息 - 只验证用户必须提供的3个核心字段

        Args:
            info: 提取的信息

        Returns:
            List[str]: 缺失的信息列表
        """
        # 只验证用户必须提供的3个核心字段
        required_fields = [
            ('contact_person', '联系人姓名'),
            ('contact_phone', '联系电话'),
            ('jersey_color', '球衣颜色')
        ]

        missing = []
        for field, display_name in required_fields:
            if not info.get(field, '').strip():
                missing.append(display_name)

        return missing
    
    def generate_summary(self, info: Dict[str, Any]) -> str:
        """
        生成信息总结
        
        Args:
            info: 球队信息
            
        Returns:
            str: 信息总结
        """
        summary = "📋 **球队信息总结**\n\n"

        # 必需信息
        if info.get('team_name'):
            summary += f"⚽ **球队名称**: {info['team_name']}\n"
        if info.get('contact_person'):
            summary += f"� **联系人**: {info['contact_person']}\n"
        if info.get('contact_phone'):
            summary += f"� **联系电话**: {info['contact_phone']}\n"
        if info.get('leader_name'):
            summary += f"� **领队**: {info['leader_name']}\n"
        if info.get('team_doctor'):
            summary += f"🏥 **队医**: {info['team_doctor']}\n"
        if info.get('jersey_color'):
            summary += f"� **球衣颜色**: {info['jersey_color']}\n"

        # 可选信息
        if info.get('shorts_color'):
            summary += f"🩳 **球裤颜色**: {info['shorts_color']}\n"
        if info.get('socks_color'):
            summary += f"🧦 **球袜颜色**: {info['socks_color']}\n"
        if info.get('goalkeeper_kit_color'):
            summary += f"🥅 **守门员服装**: {info['goalkeeper_kit_color']}\n"
        if info.get('coach_name'):
            summary += f"👨‍� **教练**: {info['coach_name']}\n"
        
        return summary

    def generate_enhanced_response(
        self,
        messages: List[Dict[str, str]],
        use_functions: bool = True,
        use_structured_output: bool = False
    ) -> str:
        """
        生成增强AI响应（支持函数调用和结构化输出）

        Args:
            messages: 对话历史
            use_functions: 是否使用函数调用
            use_structured_output: 是否使用结构化输出

        Returns:
            str: AI回复
        """
        if not self.has_enhanced_features():
            # 回退到基础功能
            return self.generate_response(messages)

        try:
            response, function_results = self.enhanced_service.generate_response_with_functions(
                messages, use_structured_output
            )

            # 如果有函数调用结果，可以在这里处理
            if function_results:
                st.session_state['last_function_results'] = function_results

            return response
        except Exception as e:
            st.error(f"增强AI功能失败，回退到基础功能: {e}")
            return self.generate_response(messages)

    def extract_team_info_from_text(self, text: str) -> Dict[str, Any]:
        """从文本中提取球队信息"""
        if not self.has_enhanced_features():
            return {"error": "增强功能不可用"}

        return self.enhanced_service.extract_info_from_text(text, "team")

    def extract_player_info_from_text(self, text: str) -> Dict[str, Any]:
        """从文本中提取球员信息"""
        if not self.has_enhanced_features():
            return {"error": "增强功能不可用"}

        return self.enhanced_service.extract_info_from_text(text, "player")

    # ==================== 换装功能 ====================

    def is_fashion_tryon_available(self) -> bool:
        """检查换装功能是否可用"""
        return FASHION_TRYON_AVAILABLE

    def fashion_tryon_single(self, model_image_path: str, clothes_image_path: str) -> Dict[str, Any]:
        """单张照片换装处理 - 使用新的换装API服务

        Args:
            model_image_path: 模特照片路径
            clothes_image_path: 衣服照片路径

        Returns:
            Dict: 包含处理结果的字典
        """
        try:
            if not fashion_api_service.is_available():
                return {
                    "success": False,
                    "error": "换装API服务不可用，请检查API配置"
                }

            # 使用新的换装API服务处理完整流程
            result = fashion_api_service.process_single_complete_workflow(
                model_image_path, clothes_image_path
            )

            if result["success"]:
                return {
                    "success": True,
                    "result_path": result["final_result"],
                    "steps": result["steps"],
                    "processing_time": result["processing_time"],
                    "message": "换装处理完成！"
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "换装处理失败"),
                    "steps": result.get("steps", {}),
                    "processing_time": result.get("processing_time", 0)
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"换装处理异常: {str(e)}"
            }

    def fashion_tryon_batch(self, player_images: List[str], clothes_image_path: str, team_name: str = None) -> Dict[str, Any]:
        """批量换装处理 - 使用新的换装API服务（支持用户文件夹）

        Args:
            player_images: 球员照片路径列表
            clothes_image_path: 衣服照片路径
            team_name: 球队名称（用于创建用户专属临时目录）

        Returns:
            Dict: 包含批量处理结果的字典
        """
        try:
            if not fashion_api_service.is_available():
                return {
                    "success": False,
                    "error": "换装API服务不可用，请检查API配置"
                }

            # 创建用户专属的临时目录
            from services.auth_service import AuthService
            auth_service = AuthService()
            user_id = auth_service.get_current_user_id()

            if user_id and team_name:
                # 用户专属的临时目录
                user_temp_dir = os.path.join(
                    auth_service.get_user_data_path(user_id),
                    "temp_fashion",
                    team_name
                )
                os.makedirs(user_temp_dir, exist_ok=True)

                # 设置fashion_api_service的临时目录
                original_temp_dir = fashion_api_service.temp_dir
                fashion_api_service.temp_dir = user_temp_dir

                # 添加日志信息
                import streamlit as st
                st.info(f"🔧 设置用户专属临时目录: {user_temp_dir}")

                try:
                    # 使用新的批量换装API服务
                    result = fashion_api_service.process_batch_fashion_tryon(
                        player_images, clothes_image_path
                    )

                    # 确保结果中包含临时目录信息
                    if result and isinstance(result, dict):
                        result["user_temp_dir"] = user_temp_dir
                        result["team_name"] = team_name

                    return result
                finally:
                    # 恢复原始临时目录
                    fashion_api_service.temp_dir = original_temp_dir
                    st.info(f"🔄 恢复默认临时目录: {original_temp_dir}")
            else:
                # 使用默认的批量换装API服务
                import streamlit as st
                st.warning(f"⚠️ 缺少用户ID({user_id})或球队名称({team_name})，使用默认临时目录")
                result = fashion_api_service.process_batch_fashion_tryon(
                    player_images, clothes_image_path
                )
                return result

        except Exception as e:
            return {
                "success": False,
                "error": f"批量换装处理异常: {str(e)}"
            }

    def fashion_tryon_batch_async(self, player_images: List[str], clothes_image_path: str) -> Dict[str, Any]:
        """异步批量换装处理

        Args:
            player_images: 球员照片路径列表
            clothes_image_path: 衣服照片路径

        Returns:
            Dict: 包含异步批量处理结果的字典
        """
        try:
            if not fashion_api_service.is_available():
                return {
                    "success": False,
                    "error": "换装API服务不可用，请检查API配置"
                }

            # 使用异步批量换装API服务
            async def run_async_batch():
                async with async_fashion_api_service as service:
                    return await service.process_batch_async(player_images, clothes_image_path)

            # 在Streamlit中运行异步任务
            import asyncio
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            result = loop.run_until_complete(run_async_batch())
            return result

        except Exception as e:
            return {
                "success": False,
                "error": f"异步批量换装处理异常: {str(e)}"
            }
