#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载302.ai处理结果并测试白底合成
"""

import requests
import os
from PIL import Image

def download_with_different_methods(url: str, filename: str):
    """尝试不同方法下载文件"""
    print(f"📥 尝试下载: {url}")
    
    methods = [
        {"name": "标准下载", "verify": True, "timeout": 30},
        {"name": "禁用SSL验证", "verify": False, "timeout": 30},
        {"name": "长超时", "verify": False, "timeout": 60},
        {"name": "无超时", "verify": False, "timeout": None}
    ]
    
    for method in methods:
        try:
            print(f"\n🔄 尝试方法: {method['name']}")
            
            kwargs = {
                "timeout": method["timeout"],
                "verify": method["verify"]
            }
            if method["timeout"] is None:
                kwargs.pop("timeout")
            
            response = requests.get(url, **kwargs)
            
            if response.status_code == 200:
                with open(filename, 'wb') as f:
                    f.write(response.content)
                
                print(f"✅ 下载成功: {filename}")
                print(f"📊 文件大小: {len(response.content)/1024:.1f}KB")
                return True
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 下载失败: {e}")
    
    return False

def analyze_and_test_white_background(image_path: str):
    """分析图片并测试白底合成"""
    if not os.path.exists(image_path):
        print(f"❌ 文件不存在: {image_path}")
        return
    
    print(f"\n📊 分析处理结果: {image_path}")
    
    try:
        # 分析图片属性
        img = Image.open(image_path)
        width, height = img.size
        mode = img.mode
        format_type = img.format
        file_size = os.path.getsize(image_path)
        
        print(f"📏 尺寸: {width}x{height}")
        print(f"🎨 模式: {mode}")
        print(f"📄 格式: {format_type}")
        print(f"💾 大小: {file_size/1024:.1f}KB")
        
        # 如果是RGBA模式，分析透明度
        if mode == 'RGBA':
            alpha = img.split()[-1]
            alpha_values = list(alpha.getdata())
            unique_alpha = set(alpha_values)
            transparent_count = alpha_values.count(0)
            total_pixels = len(alpha_values)
            transparent_ratio = transparent_count / total_pixels * 100
            
            print(f"🔍 透明度分析:")
            print(f"   - 透明像素: {transparent_ratio:.1f}%")
            print(f"   - Alpha范围: {min(unique_alpha)}-{max(unique_alpha)}")
            print(f"   - 总像素: {total_pixels}")
            print(f"   - 透明像素数: {transparent_count}")
            
            # 测试白底合成
            test_white_background_methods(img, image_path)
        else:
            print("⚠️ 图片不是RGBA模式，可能已经有背景")
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def test_white_background_methods(rgba_img: Image.Image, base_path: str):
    """测试不同的白底合成方法"""
    print(f"\n🎯 测试白底合成方法...")
    
    base_name = os.path.splitext(base_path)[0]
    
    try:
        # 方法1: alpha_composite (推荐)
        print("\n📋 方法1: alpha_composite")
        white_bg1 = Image.new('RGBA', rgba_img.size, (255, 255, 255, 255))
        result1 = Image.alpha_composite(white_bg1, rgba_img)
        final1 = result1.convert('RGB')
        output1 = f"{base_name}_white_composite.png"
        final1.save(output1, "PNG")
        print(f"✅ 保存: {output1}")
        
        # 方法2: paste
        print("\n📋 方法2: paste")
        white_bg2 = Image.new("RGB", rgba_img.size, "white")
        white_bg2.paste(rgba_img, (0, 0), rgba_img)
        output2 = f"{base_name}_white_paste.png"
        white_bg2.save(output2, "PNG")
        print(f"✅ 保存: {output2}")
        
        # 方法3: blend (实验性)
        print("\n📋 方法3: blend")
        white_bg3 = Image.new("RGB", rgba_img.size, "white")
        rgb_img = rgba_img.convert("RGB")
        alpha = rgba_img.split()[-1]
        
        # 创建mask
        mask = alpha.point(lambda x: 255 if x > 128 else 0)
        result3 = Image.composite(rgb_img, white_bg3, mask)
        output3 = f"{base_name}_white_blend.png"
        result3.save(output3, "PNG")
        print(f"✅ 保存: {output3}")
        
        # 分析结果
        print(f"\n📊 结果对比:")
        for i, output in enumerate([output1, output2, output3], 1):
            if os.path.exists(output):
                size = os.path.getsize(output)
                print(f"   方法{i}: {os.path.basename(output)} ({size/1024:.1f}KB)")
        
        print(f"\n💡 建议:")
        print(f"   - alpha_composite: 最准确的透明度处理")
        print(f"   - paste: 简单快速，适合大多数情况")
        print(f"   - blend: 实验性方法，可能有边缘效果")
        
    except Exception as e:
        print(f"❌ 白底合成失败: {e}")

def main():
    """主函数"""
    print("📥 下载302.ai处理结果并测试")
    print("=" * 50)
    
    # 从之前的测试结果中获取的URL
    result_url = "https://file.302.ai/gpt/imgs/20250829/5cd4d0f1b4b0787a7e673b1e2da71029.png"
    output_filename = "302ai_v1_result.png"
    
    # 尝试下载
    if download_with_different_methods(result_url, output_filename):
        # 分析和测试
        analyze_and_test_white_background(output_filename)
    else:
        print("❌ 所有下载方法都失败了")
        print("💡 可能的原因:")
        print("   1. 网络连接问题")
        print("   2. SSL证书问题")
        print("   3. 文件URL已过期")
        print("   4. 防火墙阻止")
    
    print("\n🎯 测试完成！")

if __name__ == "__main__":
    main()
