#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面测试所有日志输出源
Comprehensive Test of All Log Output Sources
"""

import os
import re
from pathlib import Path

def find_all_streamlit_outputs():
    """查找所有streamlit输出语句"""
    print("🔍 查找所有streamlit输出语句")
    print("=" * 60)
    
    # 搜索目录
    search_dirs = [
        "streamlit_team_management_modular",
    ]
    
    # 输出模式
    patterns = [
        r'st\.info\(',
        r'st\.success\(',
        r'st\.error\(',
        r'st\.warning\(',
        r'st\.write\(',
        r'st\.text\(',
        r'st\.markdown\(',
        r'st\.json\(',
        r'print\(',
    ]
    
    all_findings = {}
    
    for search_dir in search_dirs:
        if not os.path.exists(search_dir):
            continue
            
        for root, dirs, files in os.walk(search_dir):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    findings = analyze_file_for_outputs(file_path, patterns)
                    if findings:
                        all_findings[file_path] = findings
    
    return all_findings

def analyze_file_for_outputs(file_path, patterns):
    """分析文件中的输出语句"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        findings = []
        
        for i, line in enumerate(lines, 1):
            for pattern in patterns:
                if re.search(pattern, line):
                    findings.append({
                        'line_number': i,
                        'pattern': pattern,
                        'content': line.strip(),
                        'is_api_related': is_api_related_line(line)
                    })
        
        return findings
        
    except Exception as e:
        return [{'error': str(e)}]

def is_api_related_line(line):
    """判断是否是API相关的输出"""
    api_keywords = [
        '任务ID', 'task_id', 'API', '响应', '状态码', 'status',
        '提交', '等待', '下载', '处理', '背景移除', '换装',
        '📤', '📡', '📋', '📥', '⏳', '📊', '✅', '❌',
        '正在为', '正在调用', '正在执行', 'OpenAI', '302.ai'
    ]
    
    return any(keyword in line for keyword in api_keywords)

def find_specific_problematic_outputs():
    """查找具体的问题输出"""
    print(f"\n🎯 查找具体的问题输出")
    print("=" * 60)
    
    # 从用户截图看到的具体输出
    problematic_patterns = [
        "正在为.*自动生成队徽",
        "正在调用302.ai OpenAI格式API生成队徽",
        "API响应状态:",
        "API响应数据:",
        "created.*data.*b64_json"
    ]
    
    search_dirs = ["streamlit_team_management_modular"]
    findings = {}
    
    for search_dir in search_dirs:
        if not os.path.exists(search_dir):
            continue
            
        for root, dirs, files in os.walk(search_dir):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    file_findings = find_patterns_in_file(file_path, problematic_patterns)
                    if file_findings:
                        findings[file_path] = file_findings
    
    return findings

def find_patterns_in_file(file_path, patterns):
    """在文件中查找特定模式"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        findings = []
        
        for i, line in enumerate(lines, 1):
            for pattern in patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    findings.append({
                        'line_number': i,
                        'pattern': pattern,
                        'content': line.strip(),
                        'context': get_context_lines(lines, i-1, 3)
                    })
        
        return findings
        
    except Exception as e:
        return [{'error': str(e)}]

def get_context_lines(lines, line_index, context_size):
    """获取上下文行"""
    start = max(0, line_index - context_size)
    end = min(len(lines), line_index + context_size + 1)
    
    context = []
    for i in range(start, end):
        marker = ">>> " if i == line_index else "    "
        context.append(f"{marker}{i+1:3d}: {lines[i]}")
    
    return '\n'.join(context)

def check_app_py_and_main_files():
    """检查app.py和主要文件"""
    print(f"\n🔍 检查app.py和主要入口文件")
    print("=" * 60)
    
    main_files = [
        "streamlit_team_management_modular/app.py",
        "streamlit_team_management_modular/main.py",
        "streamlit_team_management_modular/__init__.py"
    ]
    
    for file_path in main_files:
        if os.path.exists(file_path):
            print(f"\n📁 检查文件: {file_path}")
            findings = analyze_file_for_outputs(file_path, [r'st\..*\(', r'print\('])
            
            if findings:
                print(f"   找到 {len(findings)} 个输出语句:")
                for finding in findings[:10]:  # 只显示前10个
                    print(f"   第{finding['line_number']}行: {finding['content']}")
                    if finding['is_api_related']:
                        print(f"      🎯 这是API相关输出！")
            else:
                print(f"   ✅ 未找到输出语句")
        else:
            print(f"❌ 文件不存在: {file_path}")

def check_components_and_pages():
    """检查组件和页面文件"""
    print(f"\n🔍 检查组件和页面文件")
    print("=" * 60)
    
    component_dirs = [
        "streamlit_team_management_modular/components",
        "streamlit_team_management_modular/pages",
        "streamlit_team_management_modular/utils"
    ]
    
    for comp_dir in component_dirs:
        if os.path.exists(comp_dir):
            print(f"\n📁 检查目录: {comp_dir}")
            
            for root, dirs, files in os.walk(comp_dir):
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        findings = analyze_file_for_outputs(file_path, [r'st\..*\('])
                        
                        api_related = [f for f in findings if f.get('is_api_related')]
                        if api_related:
                            print(f"   🎯 {file}: {len(api_related)} 个API相关输出")
                            for finding in api_related[:3]:  # 只显示前3个
                                print(f"      第{finding['line_number']}行: {finding['content'][:80]}...")

def analyze_specific_screenshot_outputs():
    """分析截图中的具体输出"""
    print(f"\n🎯 分析截图中的具体输出")
    print("=" * 60)
    
    screenshot_outputs = [
        "正在为 3333332 自动生成队徽",
        "正在调用302.ai OpenAI格式API生成队徽",
        "API响应状态: 200",
        "API响应数据: {'created': 1756456592, 'data': [{'b64_json':"
    ]
    
    print("🔍 在代码中搜索这些具体输出:")
    
    for output in screenshot_outputs:
        print(f"\n搜索: {output}")
        found = search_text_in_codebase(output)
        if found:
            for file_path, line_num, line_content in found:
                print(f"   📍 {file_path}:{line_num}")
                print(f"      {line_content.strip()}")
        else:
            print(f"   ❌ 未找到完全匹配")
            
            # 尝试搜索关键词
            keywords = extract_keywords(output)
            for keyword in keywords:
                keyword_found = search_text_in_codebase(keyword)
                if keyword_found:
                    print(f"   🔍 找到关键词 '{keyword}':")
                    for file_path, line_num, line_content in keyword_found[:3]:
                        print(f"      📍 {file_path}:{line_num}")

def search_text_in_codebase(search_text):
    """在代码库中搜索文本"""
    results = []
    
    for root, dirs, files in os.walk("streamlit_team_management_modular"):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    for i, line in enumerate(lines, 1):
                        if search_text in line:
                            results.append((file_path, i, line))
                            
                except Exception:
                    continue
    
    return results

def extract_keywords(text):
    """提取关键词"""
    keywords = []
    
    # 提取中文关键词
    chinese_patterns = [
        r'正在为.*生成',
        r'正在调用.*API',
        r'API响应',
        r'自动生成队徽'
    ]
    
    for pattern in chinese_patterns:
        matches = re.findall(pattern, text)
        keywords.extend(matches)
    
    # 添加一些明显的关键词
    obvious_keywords = ['正在为', '正在调用', 'API响应状态', 'API响应数据', '自动生成队徽']
    keywords.extend(obvious_keywords)
    
    return list(set(keywords))

def main():
    """主函数"""
    print("🔧 全面测试所有日志输出源")
    print("=" * 60)
    
    # 1. 查找所有streamlit输出
    print("📊 第1步: 查找所有streamlit输出语句")
    all_findings = find_all_streamlit_outputs()
    
    total_files = len(all_findings)
    total_outputs = sum(len(findings) for findings in all_findings.values())
    api_related_outputs = sum(
        len([f for f in findings if f.get('is_api_related', False)]) 
        for findings in all_findings.values()
    )
    
    print(f"📈 统计结果:")
    print(f"- 包含输出的文件: {total_files} 个")
    print(f"- 总输出语句: {total_outputs} 个")
    print(f"- API相关输出: {api_related_outputs} 个")
    
    # 2. 查找具体问题输出
    print(f"\n📊 第2步: 查找具体问题输出")
    problematic = find_specific_problematic_outputs()
    
    if problematic:
        print(f"🎯 找到 {len(problematic)} 个文件包含问题输出:")
        for file_path, findings in problematic.items():
            print(f"\n📁 {file_path}:")
            for finding in findings:
                print(f"   第{finding['line_number']}行: {finding['content']}")
    else:
        print("✅ 未找到明显的问题输出模式")
    
    # 3. 检查主要文件
    check_app_py_and_main_files()
    
    # 4. 检查组件文件
    check_components_and_pages()
    
    # 5. 分析截图输出
    analyze_specific_screenshot_outputs()
    
    print(f"\n🎯 测试完成！")
    print(f"💡 建议:")
    print(f"1. 重点检查包含API相关输出的文件")
    print(f"2. 查看app.py和主要入口文件")
    print(f"3. 检查组件和页面文件中的输出")
    print(f"4. 搜索截图中显示的具体文本")

if __name__ == "__main__":
    main()
