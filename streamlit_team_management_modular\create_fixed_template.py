#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建修复后的模板文件
"""

import zipfile
import xml.etree.ElementTree as ET
import os
import shutil
import re

def create_fixed_template():
    """创建修复后的模板文件"""
    print("🔧 创建修复后的模板文件")
    print("=" * 60)
    
    original_template = "../word_zc/template_15players_fixed.docx"
    fixed_template = "../word_zc/template_15players_fixed.docx"
    
    if not os.path.exists(original_template):
        print(f"❌ 原始模板文件不存在: {original_template}")
        return False
    
    try:
        # 读取原始模板
        with zipfile.ZipFile(original_template, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        print(f"📄 原始内容长度: {len(content)} 字符")
        
        # 修复颜色占位符
        original_content = content
        
        # 1. 修复 jerseyColor
        print(f"\n🔧 修复 jerseyColor 占位符:")
        jersey_pattern = r'<w:t>\{\{</w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidRPr="[^"]*"><w:t>jerseyColor</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidRPr="[^"]*"><w:t>\}\}</w:t>'
        jersey_replacement = '<w:t>{{jerseyColor}}</w:t>'
        
        jersey_matches = re.findall(jersey_pattern, content)
        if jersey_matches:
            content = re.sub(jersey_pattern, jersey_replacement, content)
            print(f"   ✅ 修复了 {len(jersey_matches)} 个 jerseyColor 占位符")
        
        # 2. 修复 shortsColor
        print(f"\n🔧 修复 shortsColor 占位符:")
        shorts_pattern = r'<w:t>\{\{</w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidRPr="[^"]*"><w:t>shortsColor</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidRPr="[^"]*"><w:t>\}\}</w:t>'
        shorts_replacement = '<w:t>{{shortsColor}}</w:t>'
        
        shorts_matches = re.findall(shorts_pattern, content)
        if shorts_matches:
            content = re.sub(shorts_pattern, shorts_replacement, content)
            print(f"   ✅ 修复了 {len(shorts_matches)} 个 shortsColor 占位符")
        
        # 3. 修复 socksColor
        print(f"\n🔧 修复 socksColor 占位符:")
        socks_pattern = r'<w:t>\{\{</w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidRPr="[^"]*"><w:t>socksColor</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidRPr="[^"]*"><w:t>\}\}</w:t>'
        socks_replacement = '<w:t>{{socksColor}}</w:t>'
        
        socks_matches = re.findall(socks_pattern, content)
        if socks_matches:
            content = re.sub(socks_pattern, socks_replacement, content)
            print(f"   ✅ 修复了 {len(socks_matches)} 个 socksColor 占位符")
        
        # 4. 修复 goalkeeperKitColor
        print(f"\n🔧 修复 goalkeeperKitColor 占位符:")
        goalkeeper_pattern = r'<w:t>\{\{</w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidRPr="[^"]*"><w:t>goalkeeperKitColor</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidRPr="[^"]*"><w:t>\}\}</w:t>'
        goalkeeper_replacement = '<w:t>{{goalkeeperKitColor}}</w:t>'
        
        goalkeeper_matches = re.findall(goalkeeper_pattern, content)
        if goalkeeper_matches:
            content = re.sub(goalkeeper_pattern, goalkeeper_replacement, content)
            print(f"   ✅ 修复了 {len(goalkeeper_matches)} 个 goalkeeperKitColor 占位符")
        
        # 创建临时目录
        temp_dir = "temp_fixed_template"
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        os.makedirs(temp_dir)
        
        try:
            # 解压原始模板到临时目录
            with zipfile.ZipFile(original_template, 'r') as zip_file:
                zip_file.extractall(temp_dir)
            
            # 写入修复后的document.xml
            with open(os.path.join(temp_dir, 'word', 'document.xml'), 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 创建修复后的模板
            with zipfile.ZipFile(fixed_template, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_name = os.path.relpath(file_path, temp_dir)
                        zip_file.write(file_path, arc_name)
            
            print(f"✅ 修复后的模板已创建: {os.path.basename(fixed_template)}")
            
            # 验证修复结果
            return verify_fixed_template(fixed_template)
            
        finally:
            # 清理临时目录
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return False

def verify_fixed_template(template_path):
    """验证修复后的模板"""
    print(f"\n🔍 验证修复后的模板")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        # 检查修复后的占位符
        expected_placeholders = [
            '{{jerseyColor}}',
            '{{shortsColor}}',
            '{{socksColor}}',
            '{{goalkeeperKitColor}}'
        ]
        
        print(f"📄 检查修复后的占位符:")
        all_fixed = True
        
        for placeholder in expected_placeholders:
            if placeholder in content:
                print(f"   ✅ {placeholder}: 格式正确")
            else:
                print(f"   ❌ {placeholder}: 仍有问题")
                all_fixed = False
        
        if all_fixed:
            print(f"\n🎉 模板修复完全成功！")
            return True
        else:
            print(f"\n⚠️ 模板修复部分成功")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def test_fixed_template():
    """测试修复后的模板"""
    print(f"\n🔍 测试修复后的模板")
    print("=" * 60)
    
    try:
        import streamlit as st
        
        # 设置session state
        if 'user_id' not in st.session_state:
            st.session_state.user_id = 'user_44ecbeed9db2'
        
        from word_generator_service import WordGeneratorService
        
        # 创建包含颜色信息的测试数据
        team_data = {
            'name': '颜色修复验证队',
            'leader': '测试领队',
            'coach': '测试教练',
            'doctor': '测试队医',
            'contact_person': '测试联系人',
            'contact_phone': '13800138000',
            # 颜色字段
            'jersey_color': '红色',
            'shorts_color': '蓝色',
            'socks_color': '白色',
            'goalkeeper_kit_color': '黄色'
        }
        
        players_data = [
            {
                'name': '测试球员',
                'jersey_number': '1',
                'photo': 'test.jpg'
            }
        ]
        
        print(f"📄 测试数据:")
        print(f"   球衣颜色: '{team_data['jersey_color']}'")
        print(f"   球裤颜色: '{team_data['shorts_color']}'")
        print(f"   球袜颜色: '{team_data['socks_color']}'")
        print(f"   守门员服装颜色: '{team_data['goalkeeper_kit_color']}'")
        
        # 使用修复后的模板
        fixed_template_path = os.path.abspath("../word_zc/template_15players_fixed.docx")
        jar_path = os.path.abspath("../word_zc/ai-football-generator/target/word-generator.jar")
        output_dir = os.path.abspath("data/user_44ecbeed9db2/word_output")
        
        # 创建服务
        word_service = WordGeneratorService(
            jar_path=jar_path,
            template_path=fixed_template_path,
            output_dir=output_dir
        )
        
        # 生成Word
        print(f"\n🚀 使用修复后的模板生成Word文档...")
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print(f"✅ 生成成功: {os.path.basename(result['file_path'])}")
            
            # 检查生成的文件内容
            return check_colors_in_generated_word(result['file_path'], team_data)
        else:
            print(f"❌ 生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_colors_in_generated_word(file_path, team_data):
    """检查生成的Word中的颜色"""
    print(f"\n🔍 检查生成的Word中的颜色")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    print(f"📄 生成的Word文档内容:")
                    print(f"   {full_text}")
                    
                    # 检查各种颜色
                    colors = {
                        'jersey_color': team_data.get('jersey_color', ''),
                        'shorts_color': team_data.get('shorts_color', ''),
                        'socks_color': team_data.get('socks_color', ''),
                        'goalkeeper_kit_color': team_data.get('goalkeeper_kit_color', '')
                    }
                    
                    print(f"\n📄 颜色修复验证:")
                    color_results = {}
                    
                    for color_type, color_value in colors.items():
                        if color_value:
                            has_color = color_value in full_text
                            print(f"   {color_type} '{color_value}': {'✅ 找到' if has_color else '❌ 未找到'}")
                            color_results[color_type] = has_color
                        else:
                            print(f"   {color_type}: ❌ 数据中无此字段")
                            color_results[color_type] = False
                    
                    # 计算成功率
                    successful_colors = sum(1 for result in color_results.values() if result)
                    total_colors = len(color_results)
                    success_rate = (successful_colors / total_colors * 100) if total_colors > 0 else 0
                    
                    print(f"\n📊 颜色字段修复验证结果: {successful_colors}/{total_colors} ({success_rate:.1f}%)")
                    
                    return success_rate >= 75  # 75%以上认为成功
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return False
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 创建修复后的模板文件")
    print("=" * 70)
    print("基于联系人信息修复的成功经验")
    print("=" * 70)
    
    # 1. 创建修复后的模板
    create_success = create_fixed_template()
    
    if create_success:
        # 2. 测试修复后的模板
        test_success = test_fixed_template()
        
        # 综合结果
        print(f"\n📊 颜色占位符修复最终结果")
        print("=" * 70)
        
        if test_success:
            print("🎉 颜色占位符修复完全成功！")
            print("✅ 创建了修复后的模板: template_15players_fixed.docx")
            print("✅ Word生成中的颜色字段正常工作")
            print("✅ 基于联系人信息修复经验的方法有效")
            
            print(f"\n🎯 现在用户可以:")
            print(f"   1. 使用修复后的模板生成包含颜色信息的Word报名表")
            print(f"   2. 在Word报名表中看到正确的球衣、球裤、球袜颜色")
            print(f"   3. 在Word报名表中看到正确的守门员服装颜色")
            
            print(f"\n💡 使用说明:")
            print(f"   请将 template_15players_fixed.docx 替换原来的模板文件")
            print(f"   或者修改配置使用新的模板文件")
        else:
            print("⚠️ 颜色占位符修复部分成功")
            print("✅ 模板格式已修复")
            print("❌ Word生成仍有问题，需要进一步调试")
    else:
        print("❌ 颜色占位符修复失败")
        print("💡 建议检查模板文件和修复逻辑")

if __name__ == "__main__":
    main()
