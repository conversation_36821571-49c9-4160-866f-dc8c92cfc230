#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面测试Word模板中的所有数据填入问题
"""

import zipfile
import xml.etree.ElementTree as ET
import os
import json
import re

def analyze_template_placeholders():
    """分析模板中的所有占位符"""
    print("🔍 分析模板中的所有占位符")
    print("=" * 60)
    
    template_path = "../word_zc/template_15players_fixed.docx"
    
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        return []
    
    try:
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 查找所有占位符
                placeholders = re.findall(r'\{\{[^}]+\}\}', content)
                unique_placeholders = list(set(placeholders))
                
                print(f"📄 模板中的所有占位符 (共{len(unique_placeholders)}个):")
                
                # 按类别分组
                team_placeholders = []
                player_placeholders = []
                other_placeholders = []
                
                for placeholder in sorted(unique_placeholders):
                    if 'player' in placeholder.lower():
                        player_placeholders.append(placeholder)
                    elif any(keyword in placeholder.lower() for keyword in ['team', 'organization', 'contact', 'leader', 'coach', 'doctor']):
                        team_placeholders.append(placeholder)
                    else:
                        other_placeholders.append(placeholder)
                
                print(f"\n📋 团队信息占位符 ({len(team_placeholders)}个):")
                for placeholder in team_placeholders:
                    print(f"   • {placeholder}")
                
                print(f"\n👥 球员信息占位符 ({len(player_placeholders)}个):")
                for placeholder in player_placeholders:
                    print(f"   • {placeholder}")
                
                print(f"\n🔧 其他占位符 ({len(other_placeholders)}个):")
                for placeholder in other_placeholders:
                    print(f"   • {placeholder}")
                
                return unique_placeholders
                
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return []

def test_current_user_data():
    """测试当前用户数据"""
    print(f"\n🔍 测试当前用户数据")
    print("=" * 60)
    
    user_id = 'user_44ecbeed9db2'
    
    # 查找用户数据
    user_data_path = f"data/{user_id}"
    if not os.path.exists(user_data_path):
        print(f"❌ 用户数据路径不存在: {user_data_path}")
        return None
    
    # 查找团队
    teams_path = os.path.join(user_data_path, "teams")
    if not os.path.exists(teams_path):
        print(f"❌ 团队数据路径不存在: {teams_path}")
        return None
    
    team_files = [f for f in os.listdir(teams_path) if f.endswith('.json')]
    if not team_files:
        print(f"❌ 未找到团队数据文件")
        return None
    
    # 使用第一个团队文件
    team_file = team_files[0]
    team_name = team_file.replace('.json', '')
    
    print(f"📄 找到团队: {team_name}")
    
    # 加载团队数据
    with open(os.path.join(teams_path, team_file), 'r', encoding='utf-8') as f:
        team_data = json.load(f)
    
    # 加载AI数据
    ai_data_path = os.path.join(user_data_path, "enhanced_ai_data", f"{team_name}_ai_data.json")
    ai_data = None
    if os.path.exists(ai_data_path):
        with open(ai_data_path, 'r', encoding='utf-8') as f:
            ai_data = json.load(f)
        print(f"✅ 找到AI数据")
    else:
        print(f"⚠️ 未找到AI数据")
    
    return {
        'user_id': user_id,
        'team_name': team_name,
        'team_data': team_data,
        'ai_data': ai_data
    }

def test_word_generation_with_current_data(user_data):
    """使用当前数据测试Word生成"""
    print(f"\n🔍 使用当前数据测试Word生成")
    print("=" * 60)
    
    try:
        import streamlit as st
        
        # 设置session state
        if 'user_id' not in st.session_state:
            st.session_state.user_id = user_data['user_id']
        
        from services.fashion_workflow_service import FashionWorkflowService
        
        # 创建工作流服务
        workflow_service = FashionWorkflowService(user_data['user_id'])
        
        # 测试Word生成
        print(f"📄 测试团队: {user_data['team_name']}")
        
        word_result = workflow_service._auto_generate_word_document(
            user_data['team_name'], {}, None
        )
        
        print(f"   Word生成结果: {word_result}")
        
        if word_result.get('success'):
            print("✅ Word生成成功")
            
            # 检查生成的文件内容
            file_path = word_result.get('file_path')
            if file_path and os.path.exists(file_path):
                return analyze_generated_word_content(file_path, user_data)
            else:
                print("❌ 生成的文件不存在")
                return None
        else:
            print(f"❌ Word生成失败: {word_result.get('error', 'Unknown error')}")
            return None
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_generated_word_content(file_path, user_data):
    """分析生成的Word内容"""
    print(f"\n🔍 分析生成的Word内容")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    print(f"📄 Word文档内容分析:")
                    
                    # 检查各种数据字段
                    analysis_result = {}
                    
                    # 1. 团队基本信息
                    print(f"\n📋 团队基本信息检查:")
                    team_name = user_data['team_name']
                    has_team_name = team_name in full_text
                    print(f"   团队名称'{team_name}': {'✅ 找到' if has_team_name else '❌ 未找到'}")
                    analysis_result['team_name'] = has_team_name
                    
                    # 2. AI数据中的信息
                    if user_data['ai_data']:
                        print(f"\n📋 AI提取信息检查:")
                        extracted_info = user_data['ai_data'].get('extracted_info', {})
                        basic_info = extracted_info.get('basic_info', {})
                        additional_info = extracted_info.get('additional_info', {})
                        
                        # 联系人信息
                        contact_person = basic_info.get('contact_person', '')
                        contact_phone = basic_info.get('contact_phone', '')
                        leader_name = basic_info.get('leader_name', '')
                        team_doctor = basic_info.get('team_doctor', '')
                        coach_name = additional_info.get('coach_name', '')
                        
                        has_contact_person = contact_person and contact_person in full_text
                        has_contact_phone = contact_phone and contact_phone in full_text
                        has_leader = leader_name and leader_name in full_text
                        has_doctor = team_doctor and team_doctor in full_text
                        has_coach = coach_name and coach_name in full_text
                        
                        print(f"   联系人'{contact_person}': {'✅ 找到' if has_contact_person else '❌ 未找到'}")
                        print(f"   联系电话'{contact_phone}': {'✅ 找到' if has_contact_phone else '❌ 未找到'}")
                        print(f"   领队'{leader_name}': {'✅ 找到' if has_leader else '❌ 未找到'}")
                        print(f"   队医'{team_doctor}': {'✅ 找到' if has_doctor else '❌ 未找到'}")
                        print(f"   教练'{coach_name}': {'✅ 找到' if has_coach else '❌ 未找到'}")
                        
                        analysis_result.update({
                            'contact_person': has_contact_person,
                            'contact_phone': has_contact_phone,
                            'leader': has_leader,
                            'doctor': has_doctor,
                            'coach': has_coach
                        })
                    
                    # 3. 球员信息
                    print(f"\n👥 球员信息检查:")
                    players = user_data['team_data'].get('players', [])
                    player_analysis = []
                    
                    for i, player in enumerate(players[:5]):  # 检查前5个球员
                        player_name = player.get('name', '')
                        jersey_number = player.get('jersey_number', '')
                        
                        has_player_name = player_name and player_name in full_text
                        has_jersey_number = jersey_number and jersey_number in full_text
                        
                        print(f"   球员{i+1} '{player_name}' (号码{jersey_number}): 姓名{'✅' if has_player_name else '❌'} 号码{'✅' if has_jersey_number else '❌'}")
                        
                        player_analysis.append({
                            'name': has_player_name,
                            'jersey_number': has_jersey_number
                        })
                    
                    analysis_result['players'] = player_analysis
                    
                    # 4. 检查未替换的占位符
                    print(f"\n🔧 未替换占位符检查:")
                    remaining_placeholders = re.findall(r'\{\{[^}]+\}\}', content)
                    unique_remaining = list(set(remaining_placeholders))
                    
                    if unique_remaining:
                        print(f"   ⚠️ 发现{len(unique_remaining)}个未替换的占位符:")
                        for placeholder in sorted(unique_remaining):
                            print(f"     • {placeholder}")
                        analysis_result['remaining_placeholders'] = unique_remaining
                    else:
                        print(f"   ✅ 所有占位符都已替换")
                        analysis_result['remaining_placeholders'] = []
                    
                    # 5. 显示联系人区域内容
                    print(f"\n📄 联系人区域内容:")
                    words = full_text.split()
                    for i, word in enumerate(words):
                        if "联系人" in word:
                            start = max(0, i-5)
                            end = min(len(words), i+20)
                            context = ' '.join(words[start:end])
                            print(f"   {context}")
                            break
                    
                    return analysis_result
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return None
                
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def generate_comprehensive_report(placeholders, user_data, analysis_result):
    """生成综合报告"""
    print(f"\n📊 综合分析报告")
    print("=" * 70)
    
    if not analysis_result:
        print("❌ 无法生成报告，Word内容分析失败")
        return
    
    # 统计成功率
    total_checks = 0
    successful_checks = 0
    
    print(f"🎯 数据填入状态分析:")
    
    # 团队信息
    print(f"\n📋 团队基本信息:")
    if 'team_name' in analysis_result:
        total_checks += 1
        if analysis_result['team_name']:
            successful_checks += 1
            print(f"   ✅ 团队名称: 正确填入")
        else:
            print(f"   ❌ 团队名称: 未正确填入")
    
    # AI提取信息
    if user_data['ai_data']:
        print(f"\n📋 AI提取信息:")
        ai_fields = ['contact_person', 'contact_phone', 'leader', 'doctor', 'coach']
        for field in ai_fields:
            if field in analysis_result:
                total_checks += 1
                if analysis_result[field]:
                    successful_checks += 1
                    print(f"   ✅ {field}: 正确填入")
                else:
                    print(f"   ❌ {field}: 未正确填入")
    
    # 球员信息
    if 'players' in analysis_result:
        print(f"\n👥 球员信息:")
        for i, player_result in enumerate(analysis_result['players']):
            total_checks += 2  # 姓名和号码
            if player_result['name']:
                successful_checks += 1
                print(f"   ✅ 球员{i+1}姓名: 正确填入")
            else:
                print(f"   ❌ 球员{i+1}姓名: 未正确填入")
            
            if player_result['jersey_number']:
                successful_checks += 1
                print(f"   ✅ 球员{i+1}号码: 正确填入")
            else:
                print(f"   ❌ 球员{i+1}号码: 未正确填入")
    
    # 未替换占位符
    remaining_placeholders = analysis_result.get('remaining_placeholders', [])
    if remaining_placeholders:
        print(f"\n⚠️ 发现问题:")
        print(f"   未替换占位符 ({len(remaining_placeholders)}个):")
        for placeholder in remaining_placeholders:
            print(f"     • {placeholder}")
    
    # 成功率统计
    success_rate = (successful_checks / total_checks * 100) if total_checks > 0 else 0
    print(f"\n📊 数据填入成功率: {successful_checks}/{total_checks} ({success_rate:.1f}%)")
    
    # 问题总结
    print(f"\n🎯 问题总结:")
    if success_rate >= 90:
        print(f"   🎉 数据填入状态良好！")
    elif success_rate >= 70:
        print(f"   ⚠️ 数据填入基本正常，有少量问题需要修复")
    else:
        print(f"   ❌ 数据填入存在较多问题，需要重点修复")
    
    # 修复建议
    if remaining_placeholders:
        print(f"\n💡 修复建议:")
        print(f"   1. 检查未替换占位符的数据映射")
        print(f"   2. 确认AI数据提取是否完整")
        print(f"   3. 验证WordGeneratorService的数据准备逻辑")

def main():
    """主函数"""
    print("🎯 全面测试Word模板中的所有数据填入问题")
    print("=" * 70)
    print("基于联系人信息修复成功的经验，全面检查所有字段")
    print("=" * 70)
    
    # 1. 分析模板占位符
    placeholders = analyze_template_placeholders()
    
    # 2. 测试当前用户数据
    user_data = test_current_user_data()
    
    if not user_data:
        print("❌ 无法获取用户数据，测试终止")
        return
    
    # 3. 测试Word生成
    analysis_result = test_word_generation_with_current_data(user_data)
    
    # 4. 生成综合报告
    generate_comprehensive_report(placeholders, user_data, analysis_result)

if __name__ == "__main__":
    main()
