#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于环境变量的配置文件
"""

import os
from dataclasses import dataclass
from typing import Dict, Any


@dataclass
class WordGeneratorSettings:
    """Word生成器配置 - 支持环境变量"""
    JAR_PATH: str = "../word_zc/ai-football-generator/target/word-generator.jar"
    # 支持环境变量覆盖模板路径
    TEMPLATE_PATH: str = os.getenv("WORD_TEMPLATE_PATH", "../word_zc/ai-football-generator/template.docx")
    TEMP_DIR: str = "temp"
    MAX_FILES: int = 20
    TIMEOUT: int = 60

    def get_absolute_paths(self, user_id: str = None, paths_instance=None) -> Dict[str, str]:
        """获取绝对路径"""
        paths = {
            'jar_path': os.path.abspath(self.JAR_PATH),
            'template_path': os.path.abspath(self.TEMPLATE_PATH),
            'temp_dir': os.path.abspath(self.TEMP_DIR)
        }

        if user_id and paths_instance:
            from config.settings import PathSettings
            paths_obj = PathSettings()
            user_word_output = paths_obj.get_user_data_path(user_id, "word_output")
            paths['output_dir'] = os.path.abspath(user_word_output)
        else:
            paths['output_dir'] = os.path.abspath(self.TEMP_DIR)

        return paths


# 环境变量配置实例
env_word_settings = WordGeneratorSettings()
