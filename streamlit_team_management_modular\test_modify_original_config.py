#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改原始配置文件的方法
"""

import sys
import os
import shutil
from pathlib import Path

def backup_and_modify_config():
    """备份并修改原始配置文件"""
    config_file = "config/settings.py"
    backup_file = "config/settings_original_backup.py"
    
    print("🔧 开始测试修改原始配置文件...")
    
    # 1. 备份原始配置文件
    if not os.path.exists(backup_file):
        shutil.copy2(config_file, backup_file)
        print(f"✅ 已备份原始配置文件: {backup_file}")
    else:
        print(f"ℹ️ 备份文件已存在: {backup_file}")
    
    # 2. 读取原始配置文件
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 3. 修改模板路径
    old_template_path = 'TEMPLATE_PATH: str = "../word_zc/ai-football-generator/template.docx"'
    new_template_path = 'TEMPLATE_PATH: str = "../word_zc/template_15players_fixed.docx"'
    
    if old_template_path in content:
        modified_content = content.replace(old_template_path, new_template_path)
        
        # 4. 写入修改后的配置
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print(f"✅ 已修改配置文件，模板路径改为: ../word_zc/template_15players_fixed.docx")
        return True
    else:
        print(f"❌ 未找到目标配置行: {old_template_path}")
        return False

def test_modified_config():
    """测试修改后的配置"""
    print("🧪 测试修改后的配置...")
    
    try:
        # 重新导入配置（清除缓存）
        if 'config.settings' in sys.modules:
            del sys.modules['config.settings']
        
        from config.settings import app_settings
        from word_generator_service import WordGeneratorService
        
        # 获取配置路径
        word_config = app_settings.word_generator
        paths = word_config.get_absolute_paths("test_user", app_settings.paths)
        
        print(f"📄 当前模板路径: {paths['template_path']}")
        
        # 检查模板文件是否存在
        template_exists = os.path.exists(paths['template_path'])
        print(f"✅ 模板文件存在: {template_exists}")
        
        if not template_exists:
            print("❌ 15人模板文件不存在")
            return False
        
        # 创建Word生成服务并测试
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        # 准备测试数据
        team_data = {
            'name': '测试修改原始配置队',
            'leader': '张三',
            'coach': '李四',
            'doctor': '王五'
        }
        
        players_data = [
            {
                'name': '张雷',
                'jersey_number': '10',
                'photo': 'word_zc/ai-football-generator/java_word_photos/player1.png'
            }
        ]
        
        print("📝 开始生成Word报名表...")
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print(f"🎉 Word报名表生成成功!")
            print(f"📄 文件路径: {result['file_path']}")
            return True
        else:
            print(f"❌ Word报名表生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False

def restore_original_config():
    """恢复原始配置文件"""
    config_file = "config/settings.py"
    backup_file = "config/settings_original_backup.py"
    
    if os.path.exists(backup_file):
        shutil.copy2(backup_file, config_file)
        print(f"✅ 已恢复原始配置文件")
        return True
    else:
        print(f"❌ 备份文件不存在，无法恢复")
        return False

def main():
    """主测试函数"""
    print("🎯 开始测试修改原始配置文件的方法\n")
    
    try:
        # 1. 备份并修改配置
        if not backup_and_modify_config():
            print("❌ 配置文件修改失败")
            return
        
        # 2. 测试修改后的配置
        success = test_modified_config()
        
        # 3. 恢复原始配置
        restore_original_config()
        
        if success:
            print("\n🎯 测试结论: 修改原始配置文件方法可以成功使用15人模板!")
            print("💡 建议: 直接修改 config/settings.py 中的 TEMPLATE_PATH")
        else:
            print("\n❌ 测试失败: 修改原始配置文件方法存在问题")
            
    except Exception as e:
        print(f"❌ 测试过程出现异常: {e}")
        # 确保恢复原始配置
        restore_original_config()

if __name__ == "__main__":
    main()
