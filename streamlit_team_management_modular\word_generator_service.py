#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word生成服务
通过Subprocess调用Java程序生成Word报名表
"""

import subprocess
import json
import tempfile
import os
import shutil
from pathlib import Path
from datetime import datetime
import streamlit as st

class WordGeneratorService:
    """Word生成服务类"""
    
    def __init__(self, jar_path, template_path, output_dir):
        """
        初始化Word生成服务
        
        Args:
            jar_path: Java JAR文件路径
            template_path: Word模板文件路径
            output_dir: 输出目录
        """
        self.jar_path = jar_path
        self.template_path = template_path
        self.output_dir = output_dir
        
        # 确保输出目录存在
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        # 验证必要文件存在
        self._validate_setup()
    
    def _validate_setup(self):
        """验证设置"""
        if not os.path.exists(self.jar_path):
            st.error(f"❌ Java JAR文件不存在: {self.jar_path}")
            st.info("💡 请确保已编译Java项目并生成JAR文件")
            return False
        
        if not os.path.exists(self.template_path):
            st.warning(f"⚠️ Word模板文件不存在: {self.template_path}")
            st.info("💡 将使用默认模板")
        
        return True
    
    def generate_report(self, team_data, players_data):
        """
        生成Word报名表
        
        Args:
            team_data: 球队数据字典
            players_data: 球员数据列表
            
        Returns:
            dict: 生成结果 {'success': bool, 'file_path': str, 'message': str}
        """
        try:
            # 1. 数据验证
            if not self._validate_data(team_data, players_data):
                return {
                    'success': False,
                    'error': 'Data validation failed',
                    'message': '数据验证失败'
                }
            
            # 2. 准备JSON数据
            json_data = self._prepare_json_data(team_data, players_data)
            
            # 3. 写入临时文件
            temp_file = self._write_temp_json(json_data)
            
            try:
                # 4. 调用Java程序
                result = self._call_java_generator(temp_file)
                
                # 5. 处理结果
                if result['success']:
                    return {
                        'success': True,
                        'file_path': result['file_path'],
                        'message': 'Word报名表生成成功',
                        'team_name': team_data.get('name', ''),
                        'player_count': len([p for p in players_data if p.get('name')])
                    }
                else:
                    return {
                        'success': False,
                        'error': result['error'],
                        'message': 'Word报名表生成失败'
                    }
                    
            finally:
                # 清理临时文件
                if os.path.exists(temp_file):
                    try:
                        os.remove(temp_file)
                    except:
                        pass  # 忽略清理错误
                        
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': f'生成过程出错: {e}'
            }
    
    def _validate_data(self, team_data, players_data):
        """验证数据"""
        if not team_data:
            st.error("❌ 球队数据为空")
            return False
        
        if not players_data:
            st.error("❌ 球员数据为空")
            return False
        
        # 检查有效球员数量
        valid_players = [p for p in players_data if p.get('name') and p.get('jersey_number')]
        if len(valid_players) == 0:
            st.error("❌ 没有有效的球员数据")
            return False
        
        return True
    
    def _prepare_json_data(self, team_data, players_data):
        """准备JSON数据"""
        # 处理球队信息
        team_name = team_data.get('name', '足球队')
        
        team_info = {
            "title": f"{team_name}报名表",
            "organizationName": team_name,
            "teamLeader": team_data.get('leader', ''),
            "coach": team_data.get('coach', ''),
            "teamDoctor": team_data.get('doctor', ''),
            "contactPerson": team_data.get('contact_person', team_data.get('leader', '')),
            "contactPhone": team_data.get('contact_phone', ''),
            # 添加颜色字段映射
            "jerseyColor": team_data.get('jersey_color', ''),
            "shortsColor": team_data.get('shorts_color', ''),
            "socksColor": team_data.get('socks_color', ''),
            "goalkeeperKitColor": team_data.get('goalkeeper_kit_color', ''),
            # 添加队徽字段 - 使用模板中的字段名
            "@teamLogoPhoto": team_data.get('logo_path', '')
        }
        
        # 处理球员信息
        players = []
        for player in players_data:
            if player.get('name') and player.get('jersey_number'):
                # 处理照片路径
                photo_path = player.get('photo', '')
                if photo_path and not os.path.isabs(photo_path):
                    # 转换为绝对路径
                    photo_path = os.path.abspath(photo_path)
                
                players.append({
                    "number": str(player.get('jersey_number', '')),
                    "name": player.get('name', ''),
                    "photoPath": photo_path
                })
        
        # 配置信息
        config = {
            "templatePath": os.path.abspath(self.template_path),
            "outputDir": os.path.abspath(self.output_dir),
            "photosDir": os.path.abspath("photos")
        }
        
        return {
            "teamInfo": team_info,
            "players": players,
            "config": config,
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "python_version": "streamlit_integration",
                "team_name": team_name,
                "player_count": len(players)
            }
        }
    
    def _write_temp_json(self, json_data):
        """写入临时JSON文件"""
        with tempfile.NamedTemporaryFile(
            mode='w', 
            suffix='.json', 
            delete=False, 
            encoding='utf-8',
            prefix='word_gen_'
        ) as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
            return f.name
    
    def _call_java_generator(self, json_file_path):
        """调用Java程序"""
        try:
            # 构建命令
            cmd = [
                'java',
                '-cp', self.jar_path,
                'CommandLineMain',
                json_file_path
            ]
            
            # 执行命令
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore',  # 忽略编码错误
                timeout=60  # 60秒超时
            )
            
            # 解析结果
            if result.returncode == 0:
                # 解析成功输出 - 查找SUCCESS行
                output = result.stdout if result.stdout else ""

                # 按行分割，查找SUCCESS行
                for line in output.split('\n'):
                    line = line.strip()
                    if line.startswith('SUCCESS:'):
                        file_path = line[8:].strip()  # 移除 "SUCCESS:" 前缀并去除空格
                        # 转换为标准路径格式
                        file_path = os.path.normpath(file_path)

                        # 如果文件不存在，尝试查找实际生成的文件
                        if not os.path.exists(file_path):
                            actual_file = self._find_actual_generated_file(file_path)
                            if actual_file:
                                file_path = actual_file

                        return {'success': True, 'file_path': file_path}

                # 如果没找到SUCCESS行，但返回码是0，可能是编码问题
                return {'success': False, 'error': 'No SUCCESS line found in output'}
            else:
                # 解析错误输出
                error_msg = result.stderr.strip() if result.stderr else "Unknown error"
                if error_msg.startswith('ERROR:'):
                    error_msg = error_msg[6:]  # 移除 "ERROR:" 前缀
                return {'success': False, 'error': error_msg}
                
        except subprocess.TimeoutExpired:
            return {'success': False, 'error': 'Java process timeout (60s)'}
        except FileNotFoundError:
            return {'success': False, 'error': 'Java not found. Please install Java 8+'}
        except Exception as e:
            return {'success': False, 'error': f'Process execution failed: {e}'}
    
    def get_output_files(self):
        """获取输出目录中的文件列表"""
        try:
            if not os.path.exists(self.output_dir):
                return []
            
            files = []
            for file_path in Path(self.output_dir).glob("*.docx"):
                stat = file_path.stat()
                files.append({
                    'name': file_path.name,
                    'path': str(file_path),
                    'size': stat.st_size,
                    'modified': datetime.fromtimestamp(stat.st_mtime)
                })
            
            # 按修改时间排序（最新的在前）
            files.sort(key=lambda x: x['modified'], reverse=True)
            return files
            
        except Exception as e:
            st.error(f"获取文件列表失败: {e}")
            return []
    
    def cleanup_old_files(self, keep_count=10):
        """清理旧文件，保留最新的几个"""
        try:
            files = self.get_output_files()
            if len(files) > keep_count:
                for file_info in files[keep_count:]:
                    try:
                        os.remove(file_info['path'])
                        st.info(f"🗑️ 已清理旧文件: {file_info['name']}")
                    except:
                        pass
        except Exception as e:
            st.warning(f"清理文件时出错: {e}")

    def _find_actual_generated_file(self, expected_path):
        """
        查找实际生成的文件
        当Java返回的文件名与实际文件名不匹配时使用
        """
        try:
            # 提取时间戳
            import re
            timestamp_match = re.search(r'_(\d+)\.docx$', expected_path)
            if not timestamp_match:
                return None

            timestamp = timestamp_match.group(1)

            # 在输出目录中查找包含相同时间戳的文件
            if os.path.exists(self.output_dir):
                for filename in os.listdir(self.output_dir):
                    if filename.endswith(f'_{timestamp}.docx'):
                        full_path = os.path.join(self.output_dir, filename)
                        if os.path.exists(full_path):
                            return full_path

            return None

        except Exception as e:
            st.warning(f"查找实际文件时出错: {e}")
            return None


def create_word_generator_service():
    """创建Word生成服务实例"""
    from config import WORD_CONFIG
    
    return WordGeneratorService(
        jar_path=WORD_CONFIG["jar_path"],
        template_path=WORD_CONFIG["template_path"],
        output_dir=WORD_CONFIG["output_dir"]
    )
