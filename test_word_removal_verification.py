#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Word生成界面移除结果
Verify Word Generation Interface Removal Results
"""

import os

def verify_word_generator_removal():
    """验证独立Word生成器组件已被移除"""
    print("🗑️ 验证独立Word生成器组件移除")
    print("=" * 80)
    
    # 检查word_generator.py文件是否已删除
    word_generator_file = "streamlit_team_management_modular/components/word_generator.py"
    
    if os.path.exists(word_generator_file):
        print(f"❌ {word_generator_file} 仍然存在")
        return False
    else:
        print(f"✅ {word_generator_file} 已成功移除")
    
    # 检查AI聊天组件中的依赖是否已清理
    ai_chat_file = "streamlit_team_management_modular/components/ai_chat.py"
    
    if os.path.exists(ai_chat_file):
        try:
            with open(ai_chat_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否还有word_generator的导入
            if 'from components.word_generator import' in content:
                print(f"❌ {ai_chat_file} 中仍有word_generator导入")
                return False
            else:
                print(f"✅ {ai_chat_file} 中的word_generator依赖已清理")
            
            # 检查是否还有word_generator的使用
            if 'self.word_generator' in content and 'word_generator已移除' not in content:
                print(f"⚠️ {ai_chat_file} 中可能仍有word_generator的使用")
            else:
                print(f"✅ {ai_chat_file} 中的word_generator使用已清理")
                
        except Exception as e:
            print(f"❌ 检查 {ai_chat_file} 失败: {e}")
            return False
    
    return True

def verify_auto_word_generation_intact():
    """验证自动Word生成功能完整"""
    print(f"\n✅ 验证自动Word生成功能完整")
    print("=" * 80)
    
    # 检查关键文件和函数
    critical_components = {
        "WordGeneratorService": {
            "file": "streamlit_team_management_modular/word_generator_service.py",
            "check": ["class WordGeneratorService", "def generate_report"]
        },
        
        "自动生成函数": {
            "file": "streamlit_team_management_modular/services/fashion_workflow_service.py",
            "check": ["def _auto_generate_word_document"]
        },
        
        "Word配置": {
            "file": "streamlit_team_management_modular/config/settings.py",
            "check": ["class WordGeneratorSettings"]
        }
    }
    
    all_intact = True
    
    for component_name, details in critical_components.items():
        file_path = details["file"]
        checks = details["check"]
        
        print(f"\n🔍 检查 {component_name}")
        
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                missing_components = []
                for check in checks:
                    if check not in content:
                        missing_components.append(check)
                
                if missing_components:
                    print(f"   ❌ 缺少组件: {missing_components}")
                    all_intact = False
                else:
                    print(f"   ✅ 所有组件完整: {checks}")
                    
            except Exception as e:
                print(f"   ❌ 读取文件失败: {e}")
                all_intact = False
        else:
            print(f"   ❌ 文件不存在: {file_path}")
            all_intact = False
    
    return all_intact

def check_remaining_dependencies():
    """检查剩余的依赖问题"""
    print(f"\n🔍 检查剩余的依赖问题")
    print("=" * 80)
    
    # 搜索可能的残留依赖
    dependency_patterns = [
        'from components.word_generator',
        'import word_generator',
        'WordGeneratorComponent',
        'create_word_generator_component',
        'render_word_generation_panel'
    ]
    
    remaining_dependencies = {}
    
    for root, dirs, files in os.walk("streamlit_team_management_modular"):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    found_deps = []
                    lines = content.split('\n')
                    
                    for i, line in enumerate(lines, 1):
                        for pattern in dependency_patterns:
                            # 跳过已经处理的函数调用和注释
                            if (pattern in line and
                                '已移除' not in line and
                                '# Word生成功能已集成' not in line and
                                'render_word_generation_panel' not in line and
                                'def _render_word_generation_panel' not in line):
                                found_deps.append({
                                    'line': i,
                                    'content': line.strip(),
                                    'pattern': pattern
                                })
                    
                    if found_deps:
                        remaining_dependencies[file_path] = found_deps
                        
                except Exception as e:
                    continue
    
    if remaining_dependencies:
        print("⚠️ 发现剩余依赖:")
        for file_path, deps in remaining_dependencies.items():
            print(f"\n📄 {file_path}")
            for dep in deps[:3]:  # 只显示前3个
                print(f"   第{dep['line']}行: {dep['content'][:80]}...")
        return False
    else:
        print("✅ 未发现剩余依赖")
        return True

def test_auto_word_generation_workflow():
    """测试自动Word生成工作流"""
    print(f"\n🧪 测试自动Word生成工作流")
    print("=" * 80)
    
    try:
        # 添加当前目录到Python路径
        import sys
        sys.path.append('streamlit_team_management_modular')

        # 测试导入自动生成服务
        from services.fashion_workflow_service import FashionWorkflowService
        print("✅ FashionWorkflowService 导入成功")
        
        # 检查自动生成函数是否存在
        if hasattr(FashionWorkflowService, '_auto_generate_word_document'):
            print("✅ _auto_generate_word_document 函数存在")
        else:
            print("❌ _auto_generate_word_document 函数不存在")
            return False
        
        # 测试WordGeneratorService导入
        from word_generator_service import WordGeneratorService
        print("✅ WordGeneratorService 导入成功")
        
        # 测试配置导入
        from config.settings import app_settings
        if hasattr(app_settings, 'word_generator'):
            print("✅ Word生成器配置存在")
        else:
            print("❌ Word生成器配置不存在")
            return False
        
        print("✅ 自动Word生成工作流测试通过")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def generate_removal_summary():
    """生成移除总结"""
    print(f"\n📋 Word生成界面移除总结")
    print("=" * 80)
    
    summary = {
        "已移除的组件": [
            "streamlit_team_management_modular/components/word_generator.py - 独立Word生成界面",
            "AI聊天组件中的word_generator导入和调用",
            "测试文件中的word_generator组件测试"
        ],
        
        "保留的功能": [
            "WordGeneratorService - 核心Word生成服务",
            "_auto_generate_word_document() - 自动生成函数",
            "WordGeneratorSettings - 配置类",
            "Java Word生成器后端",
            "工作流中的自动生成逻辑"
        ],
        
        "用户体验改进": [
            "移除了冗余的手动Word生成界面",
            "统一使用自动Word生成功能",
            "简化了用户操作流程",
            "减少了功能重复和混淆"
        ],
        
        "技术改进": [
            "减少了代码冗余",
            "降低了维护成本",
            "简化了测试复杂性",
            "清理了无用的依赖关系"
        ]
    }
    
    for category, items in summary.items():
        print(f"\n📋 {category}:")
        for item in items:
            print(f"   • {item}")
    
    return summary

def main():
    """主函数"""
    print("🔍 Word生成界面移除验证")
    print("=" * 80)
    
    print("🎯 验证目标:")
    print("   确认独立Word生成界面已完全移除")
    print("   确认自动Word生成功能完整保留")
    print("   确认没有残留的依赖问题")
    
    # 1. 验证组件移除
    removal_success = verify_word_generator_removal()
    
    # 2. 验证自动生成功能完整
    auto_generation_intact = verify_auto_word_generation_intact()
    
    # 3. 检查剩余依赖
    no_remaining_deps = check_remaining_dependencies()
    
    # 4. 测试自动生成工作流
    workflow_test_passed = test_auto_word_generation_workflow()
    
    # 5. 生成总结
    summary = generate_removal_summary()
    
    # 最终结果
    print(f"\n🎊 移除验证结果")
    print("=" * 80)
    
    all_checks_passed = all([
        removal_success,
        auto_generation_intact,
        no_remaining_deps,
        workflow_test_passed
    ])
    
    if all_checks_passed:
        print("✅ Word生成界面移除成功！")
        print("✅ 独立界面已完全移除")
        print("✅ 自动生成功能完整保留")
        print("✅ 无残留依赖问题")
        print("✅ 工作流测试通过")
        
        print(f"\n🎯 用户体验提升:")
        print("   📄 不再有冗余的手动Word生成界面")
        print("   🤖 统一使用自动Word生成功能")
        print("   🎯 简化了操作流程")
        print("   ✨ 提升了系统的一致性")
        
    else:
        print("⚠️ 移除过程中发现问题:")
        if not removal_success:
            print("   ❌ 组件移除不完整")
        if not auto_generation_intact:
            print("   ❌ 自动生成功能受损")
        if not no_remaining_deps:
            print("   ❌ 存在残留依赖")
        if not workflow_test_passed:
            print("   ❌ 工作流测试失败")

if __name__ == "__main__":
    main()
