#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
识别并分析容易误导LLM的文件
Identify and Analyze Files That Mislead LLM
"""

import os
from pathlib import Path

def identify_misleading_files():
    """识别容易误导LLM的文件"""
    print("🔍 识别容易误导LLM的文件")
    print("=" * 60)
    
    misleading_files = {
        "高风险误导文件": [
            {
                "path": "word_zc/ai-football-generator/simple_python_test.py",
                "reason": "与AI图像生成服务容易混淆",
                "conflict_with": "streamlit_team_management_modular/services/ai_image_generation_service.py",
                "risk_level": "🔴 高风险"
            },
            {
                "path": "word_zc/ai-football-generator/test_python_integration.py", 
                "reason": "与Streamlit测试文件容易混淆",
                "conflict_with": "streamlit_team_management_modular/test_*.py",
                "risk_level": "🔴 高风险"
            }
        ],
        
        "中风险误导目录": [
            {
                "path": "word_zc/ai-football-generator/photos/",
                "reason": "与Streamlit照片目录完全同名",
                "conflict_with": "streamlit_team_management_modular/photos/",
                "risk_level": "🟡 中风险"
            }
        ],
        
        "其他潜在混淆文件": [
            {
                "path": "word_zc/ai-football-generator/config.properties",
                "reason": "与Streamlit配置文件功能相似",
                "conflict_with": "streamlit_team_management_modular/config/settings.py",
                "risk_level": "🟢 低风险"
            }
        ]
    }
    
    for category, files in misleading_files.items():
        print(f"\n📂 {category}")
        for file_info in files:
            print(f"   {file_info['risk_level']} {file_info['path']}")
            print(f"      原因: {file_info['reason']}")
            print(f"      冲突对象: {file_info['conflict_with']}")
    
    return misleading_files

def check_file_existence():
    """检查文件是否存在"""
    print(f"\n📋 检查文件存在性")
    print("=" * 60)
    
    files_to_check = [
        "word_zc/ai-football-generator/simple_python_test.py",
        "word_zc/ai-football-generator/test_python_integration.py",
        "word_zc/ai-football-generator/photos/",
        "word_zc/ai-football-generator/config.properties"
    ]
    
    existing_files = []
    missing_files = []
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            existing_files.append(file_path)
            print(f"   ✅ 存在: {file_path}")
        else:
            missing_files.append(file_path)
            print(f"   ❌ 不存在: {file_path}")
    
    return existing_files, missing_files

def analyze_file_contents():
    """分析文件内容，确认是否真的容易误导"""
    print(f"\n🔍 分析文件内容")
    print("=" * 60)
    
    files_to_analyze = [
        "word_zc/ai-football-generator/simple_python_test.py",
        "word_zc/ai-football-generator/test_python_integration.py"
    ]
    
    analysis_results = {}
    
    for file_path in files_to_analyze:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 分析内容特征
                features = {
                    "lines": len(content.split('\n')),
                    "has_ai_keywords": any(keyword in content.lower() for keyword in ['ai', 'image', 'generation']),
                    "has_python_imports": 'import' in content,
                    "has_test_functions": 'def test' in content or 'test_' in content,
                    "has_streamlit": 'streamlit' in content.lower() or 'st.' in content
                }
                
                analysis_results[file_path] = features
                
                print(f"\n📄 {file_path}")
                print(f"   行数: {features['lines']}")
                print(f"   包含AI关键词: {'✅' if features['has_ai_keywords'] else '❌'}")
                print(f"   包含Python导入: {'✅' if features['has_python_imports'] else '❌'}")
                print(f"   包含测试函数: {'✅' if features['has_test_functions'] else '❌'}")
                print(f"   包含Streamlit: {'✅' if features['has_streamlit'] else '❌'}")
                
                # 判断误导风险
                risk_score = 0
                if features['has_ai_keywords']: risk_score += 2
                if features['has_python_imports']: risk_score += 1
                if features['has_test_functions']: risk_score += 1
                
                if risk_score >= 3:
                    print(f"   🔴 高误导风险 (评分: {risk_score}/4)")
                elif risk_score >= 2:
                    print(f"   🟡 中误导风险 (评分: {risk_score}/4)")
                else:
                    print(f"   🟢 低误导风险 (评分: {risk_score}/4)")
                    
            except Exception as e:
                print(f"   ❌ 读取失败: {e}")
                analysis_results[file_path] = {"error": str(e)}
        else:
            print(f"   ❌ 文件不存在: {file_path}")
    
    return analysis_results

def generate_deletion_plan():
    """生成删除计划"""
    print(f"\n📋 生成删除计划")
    print("=" * 60)
    
    deletion_plan = {
        "立即删除 (高风险)": [
            {
                "path": "word_zc/ai-football-generator/simple_python_test.py",
                "reason": "容易与AI图像生成服务混淆",
                "backup_needed": True
            },
            {
                "path": "word_zc/ai-football-generator/test_python_integration.py",
                "reason": "容易与Streamlit测试文件混淆", 
                "backup_needed": True
            }
        ],
        
        "考虑删除 (中风险)": [
            {
                "path": "word_zc/ai-football-generator/photos/",
                "reason": "与Streamlit照片目录同名",
                "backup_needed": True,
                "note": "如果包含重要照片，建议重命名而不是删除"
            }
        ],
        
        "保留 (低风险)": [
            {
                "path": "word_zc/ai-football-generator/config.properties",
                "reason": "Java配置文件，技术栈差异明显",
                "backup_needed": False
            }
        ]
    }
    
    for category, files in deletion_plan.items():
        print(f"\n🎯 {category}")
        for file_info in files:
            print(f"   📁 {file_info['path']}")
            print(f"      原因: {file_info['reason']}")
            print(f"      需要备份: {'✅' if file_info['backup_needed'] else '❌'}")
            if 'note' in file_info:
                print(f"      注意: {file_info['note']}")
    
    return deletion_plan

def check_photos_directory():
    """检查photos目录内容"""
    print(f"\n📸 检查photos目录内容")
    print("=" * 60)
    
    photos_dirs = [
        "word_zc/ai-football-generator/photos/",
        "streamlit_team_management_modular/photos/"
    ]
    
    for photos_dir in photos_dirs:
        print(f"\n📂 {photos_dir}")
        if os.path.exists(photos_dir):
            try:
                files = os.listdir(photos_dir)
                print(f"   文件数量: {len(files)}")
                
                if files:
                    print(f"   文件列表:")
                    for file in files[:5]:  # 只显示前5个
                        print(f"      • {file}")
                    if len(files) > 5:
                        print(f"      ... 还有 {len(files)-5} 个文件")
                else:
                    print(f"   📁 目录为空")
                    
            except Exception as e:
                print(f"   ❌ 读取失败: {e}")
        else:
            print(f"   ❌ 目录不存在")

def main():
    """主函数"""
    print("🗑️ 识别容易误导LLM的文件 - 删除计划")
    print("=" * 60)
    
    # 1. 识别误导文件
    misleading_files = identify_misleading_files()
    
    # 2. 检查文件存在性
    existing_files, missing_files = check_file_existence()
    
    # 3. 分析文件内容
    analysis_results = analyze_file_contents()
    
    # 4. 检查photos目录
    check_photos_directory()
    
    # 5. 生成删除计划
    deletion_plan = generate_deletion_plan()
    
    # 总结
    print(f"\n🎯 删除建议总结")
    print("=" * 60)
    
    print("✅ 分析结果:")
    print(f"   📁 存在的文件: {len(existing_files)} 个")
    print(f"   📁 缺失的文件: {len(missing_files)} 个")
    
    if existing_files:
        print(f"\n🗑️ 建议删除的文件:")
        high_risk_files = [
            "word_zc/ai-football-generator/simple_python_test.py",
            "word_zc/ai-football-generator/test_python_integration.py"
        ]
        
        for file_path in high_risk_files:
            if file_path in existing_files:
                print(f"   🔴 {file_path}")
        
        print(f"\n💡 删除这些文件的好处:")
        print(f"   ✅ 消除LLM修改错误文件的风险")
        print(f"   ✅ 简化项目结构")
        print(f"   ✅ 避免功能重复和混淆")
        print(f"   ✅ 提高开发效率")
        
        print(f"\n⚠️ 注意事项:")
        print(f"   📋 删除前建议先备份")
        print(f"   🔍 确认文件不包含重要逻辑")
        print(f"   📂 photos目录如有重要照片建议重命名")
    
    print(f"\n🎉 结论:")
    print(f"   ✅ 您的想法很正确！")
    print(f"   🗑️ 删除这些误导文件是最直接有效的解决方案")
    print(f"   🎯 可以显著降低LLM混淆的风险")

if __name__ == "__main__":
    main()
