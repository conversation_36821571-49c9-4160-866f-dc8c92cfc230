#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在用户数据中查找"自动填充"字符串
"""

import os
import sys
import json
import glob
from datetime import datetime

def search_in_file(file_path):
    """在单个文件中搜索"自动填充"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "自动填充" in content:
            return True, content.count("自动填充")
        return False, 0
        
    except Exception as e:
        return False, 0

def search_in_json_file(file_path):
    """在JSON文件中搜索"自动填充"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        auto_fill_found = find_auto_fill_in_data(data)
        return len(auto_fill_found) > 0, auto_fill_found
        
    except Exception as e:
        return False, []

def find_auto_fill_in_data(data, path=""):
    """递归查找数据中的"自动填充"""
    auto_fill_found = []
    
    if isinstance(data, dict):
        for key, value in data.items():
            current_path = f"{path}.{key}" if path else key
            if isinstance(value, str) and value == "自动填充":
                auto_fill_found.append((current_path, value))
            elif isinstance(value, (dict, list)):
                auto_fill_found.extend(find_auto_fill_in_data(value, current_path))
    elif isinstance(data, list):
        for i, item in enumerate(data):
            current_path = f"{path}[{i}]"
            if isinstance(item, str) and item == "自动填充":
                auto_fill_found.append((current_path, item))
            elif isinstance(item, (dict, list)):
                auto_fill_found.extend(find_auto_fill_in_data(item, current_path))
    
    return auto_fill_found

def search_all_data_files():
    """搜索所有数据文件"""
    print("=" * 60)
    print("🔍 搜索所有数据文件中的'自动填充'")
    print("=" * 60)
    
    data_dir = "data"
    
    # 搜索所有JSON文件
    json_files = []
    for root, dirs, files in os.walk(data_dir):
        for file in files:
            if file.endswith('.json'):
                json_files.append(os.path.join(root, file))
    
    print(f"📄 找到 {len(json_files)} 个JSON文件")
    
    problematic_files = []
    
    for json_file in json_files:
        has_auto_fill, auto_fill_locations = search_in_json_file(json_file)
        
        if has_auto_fill:
            print(f"\n❌ 发现问题文件: {json_file}")
            print(f"   包含 {len(auto_fill_locations)} 个'自动填充':")
            for path, value in auto_fill_locations[:5]:  # 只显示前5个
                print(f"      {path}: '{value}'")
            
            problematic_files.append((json_file, auto_fill_locations))
    
    if not problematic_files:
        print(f"\n✅ 所有JSON文件都正常，未发现'自动填充'字符串")
    
    return problematic_files

def check_recent_word_files():
    """检查最近生成的Word文件"""
    print(f"\n" + "=" * 60)
    print("🔍 检查最近生成的Word文件")
    print("=" * 60)
    
    # 查找所有word_output目录下的docx文件
    word_files = []
    for root, dirs, files in os.walk("data"):
        if "word_output" in root:
            for file in files:
                if file.endswith('.docx'):
                    file_path = os.path.join(root, file)
                    word_files.append(file_path)
    
    # 按修改时间排序，检查最近的几个
    word_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    
    print(f"📄 找到 {len(word_files)} 个Word文件，检查最近的5个:")
    
    problematic_word_files = []
    
    for word_file in word_files[:5]:
        print(f"\n📄 检查: {os.path.basename(word_file)}")
        print(f"   路径: {word_file}")
        print(f"   修改时间: {datetime.fromtimestamp(os.path.getmtime(word_file))}")
        
        try:
            import zipfile
            
            with zipfile.ZipFile(word_file, 'r') as zip_file:
                with zip_file.open('word/document.xml') as xml_file:
                    content = xml_file.read().decode('utf-8')
            
            auto_fill_count = content.count("自动填充")
            
            if auto_fill_count > 0:
                print(f"   ❌ 发现 {auto_fill_count} 个'自动填充'字符串")
                
                # 查找上下文
                import re
                pattern = r'.{0,30}自动填充.{0,30}'
                matches = re.findall(pattern, content)
                
                print(f"   📄 '自动填充'出现的上下文:")
                for i, match in enumerate(matches[:3]):
                    clean_match = match.replace('<', '&lt;').replace('>', '&gt;')
                    print(f"      {i+1}. {clean_match}")
                
                problematic_word_files.append(word_file)
            else:
                print(f"   ✅ 未发现'自动填充'字符串")
                
                # 检查颜色
                colors = ["粉色", "红色", "蓝色", "绿色", "黑色", "白色", "黄色"]
                colors_found = [color for color in colors if color in content]
                
                if colors_found:
                    print(f"   🎨 找到颜色: {colors_found}")
                else:
                    print(f"   ⚠️ 未找到任何颜色信息")
                
        except Exception as e:
            print(f"   ❌ 检查失败: {e}")
    
    return problematic_word_files

def analyze_problem_source():
    """分析问题来源"""
    print(f"\n" + "=" * 60)
    print("🔍 分析问题来源")
    print("=" * 60)
    
    # 检查是否有特定的用户数据包含"自动填充"
    problematic_json = search_all_data_files()
    problematic_word = check_recent_word_files()
    
    print(f"\n📋 分析结果:")
    print(f"   包含'自动填充'的JSON文件: {len(problematic_json)}")
    print(f"   包含'自动填充'的Word文件: {len(problematic_word)}")
    
    if problematic_json:
        print(f"\n🎯 问题根源: 用户数据")
        print(f"   某些用户的数据文件中存储了'自动填充'字符串")
        print(f"   这些数据被传递到Word生成过程中")
        
        print(f"\n💡 解决方案:")
        print(f"   1. 修复这些用户的数据文件")
        print(f"   2. 在数据读取时过滤'自动填充'字符串")
        print(f"   3. 确保AI服务不会生成'自动填充'字符串")
        
    elif problematic_word:
        print(f"\n🎯 问题根源: Word生成过程")
        print(f"   在Word生成过程中产生了'自动填充'字符串")
        
    else:
        print(f"\n✅ 未发现明显的问题源")
        print(f"   可能是特定条件下才出现的问题")

def main():
    """主函数"""
    print("🚀 在用户数据中查找'自动填充'字符串")
    print(f"搜索时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        analyze_problem_source()
        
        print(f"\n" + "=" * 60)
        print("📋 搜索总结")
        print("=" * 60)
        
        print(f"💡 建议:")
        print(f"   1. 如果发现问题数据，需要清理或修复")
        print(f"   2. 在工作流中增强数据验证，过滤'自动填充'")
        print(f"   3. 确保AI服务的自动填充逻辑正确")
        print(f"   4. 如果问题持续，可能需要检查特定的用户操作流程")
        
    except Exception as e:
        print(f"❌ 搜索过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
