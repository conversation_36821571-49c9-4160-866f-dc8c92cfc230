#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试302.ai V1背景消除API效果
Test 302.ai V1 Background Removal API
"""

import os
import requests
import time
import json
from PIL import Image

def test_302ai_removebg_v1(image_path: str):
    """测试302.ai V1背景消除API"""
    
    # API配置
    api_key = "sk-Y5BDQdWlSCr2nQUYvbeKhEIwzzzg5AzGq8nTFR7KArlQuM3o"
    base_url = "https://api.302.ai"
    
    print(f"🧪 测试302.ai V1背景消除API")
    print("=" * 60)
    print(f"📸 测试图片: {image_path}")
    
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return
    
    # 分析原图
    analyze_image_properties(image_path, "原图")
    
    # 步骤1: 调用302.ai V1背景消除API
    print("\n🎯 步骤1: 302.ai V1背景消除...")
    
    url = f"{base_url}/302/submit/removebg"
    headers = {"Authorization": f"Bearer {api_key}"}
    
    try:
        with open(image_path, 'rb') as image_file:
            files = {
                'image': (os.path.basename(image_path), image_file, 'image/jpeg')
            }
            
            print("📤 发送背景消除请求...")
            start_time = time.time()
            
            response = requests.post(
                url, 
                headers=headers, 
                files=files, 
                timeout=60,  # V1可能需要5-15秒
                verify=False
            )
            
            request_time = time.time() - start_time
            print(f"⏱️ 请求耗时: {request_time:.2f}秒")
            
        print(f"📡 响应状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"📊 API响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                # 检查任务状态
                task_id = result.get('id')
                if task_id:
                    print(f"📋 任务ID: {task_id}")
                    
                    # 等待任务完成
                    final_result = wait_for_task_completion(base_url, headers, task_id)
                    
                    if final_result and final_result.get('status') == 'succeeded':
                        output_url = final_result.get('output')
                        if output_url:
                            # 下载结果
                            download_result(output_url, "302ai_v1_result.png")
                        else:
                            print("❌ 未找到输出URL")
                    else:
                        print(f"❌ 任务失败: {final_result}")
                else:
                    print("❌ 未获取到任务ID")
                    
            except json.JSONDecodeError:
                print(f"❌ 响应不是有效的JSON: {response.text}")
                
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"📄 响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def wait_for_task_completion(base_url: str, headers: dict, task_id: str, max_wait: int = 300):
    """等待任务完成"""
    print(f"\n⏳ 等待任务完成 (任务ID: {task_id})...")
    
    # 构建查询URL（可能需要调整）
    query_url = f"{base_url}/302/task/{task_id}"
    
    start_time = time.time()
    check_interval = 5  # 每5秒检查一次
    
    while time.time() - start_time < max_wait:
        try:
            response = requests.get(query_url, headers=headers, timeout=10, verify=False)
            
            if response.status_code == 200:
                result = response.json()
                status = result.get('status', 'unknown')
                
                print(f"📊 任务状态: {status}")
                
                if status == 'succeeded':
                    print("✅ 任务完成！")
                    return result
                elif status == 'failed':
                    print(f"❌ 任务失败: {result.get('error', '未知错误')}")
                    return result
                elif status in ['processing', 'starting', 'running']:
                    print(f"🔄 任务进行中... ({status})")
                else:
                    print(f"⚠️ 未知状态: {status}")
                    
            else:
                print(f"⚠️ 查询状态失败: {response.status_code}")
                
        except Exception as e:
            print(f"⚠️ 查询异常: {e}")
        
        time.sleep(check_interval)
    
    print(f"⏰ 等待超时 ({max_wait}秒)")
    return None

def download_result(url: str, filename: str):
    """下载处理结果"""
    print(f"\n📥 下载结果: {url}")
    
    try:
        response = requests.get(url, timeout=30, verify=False)
        
        if response.status_code == 200:
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ 结果已保存: {filename}")
            
            # 分析结果
            analyze_image_properties(filename, "302.ai V1处理后")
            
            # 添加白底测试
            add_white_background_test(filename)
            
        else:
            print(f"❌ 下载失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 下载异常: {e}")

def add_white_background_test(transparent_image_path: str):
    """测试添加白底背景"""
    print(f"\n🎯 步骤2: 添加白底背景测试...")
    
    try:
        # 打开透明背景图片
        subject = Image.open(transparent_image_path).convert("RGBA")
        width, height = subject.size
        
        print(f"📏 图像尺寸: {width}x{height}")
        print(f"📊 图像模式: {subject.mode}")
        
        # 检查透明度
        alpha = subject.split()[-1]
        alpha_values = list(alpha.getdata())
        transparent_count = alpha_values.count(0)
        total_pixels = len(alpha_values)
        transparent_ratio = transparent_count / total_pixels * 100
        
        print(f"🔍 透明度分析: {transparent_ratio:.1f}%透明像素")
        
        # 方法1: alpha_composite
        white_bg = Image.new('RGBA', subject.size, (255, 255, 255, 255))
        result1 = Image.alpha_composite(white_bg, subject)
        final1 = result1.convert('RGB')
        output1 = "302ai_v1_white_bg_composite.png"
        final1.save(output1, "PNG")
        print(f"✅ 白底合成完成 (alpha_composite): {output1}")
        
        # 方法2: paste
        white_bg2 = Image.new("RGB", subject.size, "white")
        white_bg2.paste(subject, (0, 0), subject)
        output2 = "302ai_v1_white_bg_paste.png"
        white_bg2.save(output2, "PNG")
        print(f"✅ 白底合成完成 (paste): {output2}")
        
        # 分析结果
        analyze_image_properties(output1, "白底合成 (composite)")
        analyze_image_properties(output2, "白底合成 (paste)")
        
    except Exception as e:
        print(f"❌ 白底添加失败: {e}")

def analyze_image_properties(image_path: str, description: str):
    """分析图片属性"""
    try:
        img = Image.open(image_path)
        width, height = img.size
        mode = img.mode
        format_type = img.format
        file_size = os.path.getsize(image_path)
        
        print(f"\n📊 {description}:")
        print(f"   📏 尺寸: {width}x{height}")
        print(f"   🎨 模式: {mode}")
        print(f"   📄 格式: {format_type}")
        print(f"   💾 大小: {file_size/1024:.1f}KB")
        
        if mode == 'RGBA':
            alpha = img.split()[-1]
            alpha_values = list(alpha.getdata())
            unique_alpha = set(alpha_values)
            transparent_count = alpha_values.count(0)
            total_pixels = len(alpha_values)
            transparent_ratio = transparent_count / total_pixels * 100
            print(f"   🔍 透明度: {transparent_ratio:.1f}%透明, Alpha范围{min(unique_alpha)}-{max(unique_alpha)}")
            
    except Exception as e:
        print(f"   ❌ 分析失败: {e}")

def main():
    """主函数"""
    print("🧪 302.ai V1背景消除API测试")
    print("=" * 60)
    
    # 查找测试图片
    test_images = [
        "photos/player1_cropped.png",
        "photos/player2_cropped.jpg", 
        "photos/player3_cropped.jpg",
        "data/user_a13da2c47ed7/uploads/测试AI感知/8b4bfbb0be374765a2884b8771dc1232.jpg"
    ]
    
    test_image = None
    for img_path in test_images:
        if os.path.exists(img_path):
            test_image = img_path
            break
    
    if test_image:
        test_302ai_removebg_v1(test_image)
    else:
        print("❌ 未找到测试图片")
        print("📁 请确保以下路径之一存在测试图片:")
        for path in test_images:
            print(f"   - {path}")
    
    print("\n🎯 测试完成！")
    print("\n💡 对比分析:")
    print("1. 查看原图和处理结果的对比")
    print("2. 比较两种白底合成方法的效果")
    print("3. 检查透明度处理是否正确")

if __name__ == "__main__":
    main()
