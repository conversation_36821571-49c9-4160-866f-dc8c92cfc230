#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析Word生成界面移除计划
Analysis of Word Generation Interface Removal Plan
"""

import os

def analyze_word_components_to_remove():
    """分析需要移除的Word组件"""
    print("🗑️ 分析需要移除的Word组件")
    print("=" * 80)
    
    removal_plan = {
        "需要移除的组件": {
            "独立Word生成界面": {
                "文件": "streamlit_team_management_modular/components/word_generator.py",
                "原因": "用户反馈没有用，功能冗余",
                "影响": "移除手动Word生成界面",
                "保留": "❌ 完全移除"
            },
            
            "Word生成页面导航": {
                "文件": "可能在app.py或其他导航文件中",
                "原因": "独立界面被移除后不再需要",
                "影响": "清理导航链接",
                "保留": "❌ 移除导航入口"
            },
            
            "AI聊天中的Word生成面板": {
                "文件": "streamlit_team_management_modular/components/ai_chat.py",
                "原因": "如果调用了独立界面组件",
                "影响": "清理相关调用",
                "保留": "🔍 需要检查"
            }
        },
        
        "必须保留的组件": {
            "WordGeneratorService": {
                "文件": "streamlit_team_management_modular/word_generator_service.py",
                "原因": "自动Word生成的核心服务",
                "影响": "自动生成功能依赖此服务",
                "保留": "✅ 完全保留"
            },
            
            "自动Word生成函数": {
                "文件": "streamlit_team_management_modular/services/fashion_workflow_service.py",
                "原因": "_auto_generate_word_document()是核心功能",
                "影响": "工作流自动生成Word的核心",
                "保留": "✅ 完全保留"
            },
            
            "Word配置设置": {
                "文件": "streamlit_team_management_modular/config/settings.py",
                "原因": "WordGeneratorSettings配置类",
                "影响": "自动生成需要配置信息",
                "保留": "✅ 完全保留"
            },
            
            "Java Word生成器": {
                "文件": "word_zc/ai-football-generator/",
                "原因": "实际的Word文档生成后端",
                "影响": "所有Word生成都依赖此后端",
                "保留": "✅ 完全保留"
            }
        },
        
        "需要检查的组件": {
            "AI聊天组件": {
                "文件": "streamlit_team_management_modular/components/ai_chat.py",
                "检查": "是否导入或调用了word_generator组件",
                "处理": "如有调用则移除相关代码"
            },
            
            "主应用文件": {
                "文件": "streamlit_team_management_modular/app.py",
                "检查": "是否有Word生成页面的导航",
                "处理": "移除相关导航代码"
            },
            
            "其他工作流组件": {
                "文件": "streamlit_team_management_modular/components/fashion_workflow.py",
                "检查": "是否调用了独立Word生成界面",
                "处理": "确保只使用自动生成功能"
            }
        }
    }
    
    for category, components in removal_plan.items():
        print(f"\n🎯 {category}")
        for component_name, details in components.items():
            print(f"\n   📄 {component_name}")
            for key, value in details.items():
                print(f"      {key}: {value}")
    
    return removal_plan

def check_word_generator_dependencies():
    """检查word_generator组件的依赖关系"""
    print(f"\n🔍 检查word_generator组件的依赖关系")
    print("=" * 80)
    
    # 搜索所有导入word_generator的文件
    dependencies = {}
    
    for root, dirs, files in os.walk("streamlit_team_management_modular"):
        for file in files:
            if file.endswith('.py') and file != 'word_generator.py':
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查是否导入或使用word_generator
                    word_generator_patterns = [
                        'from components.word_generator',
                        'import word_generator',
                        'word_generator',
                        'WordGeneratorComponent',
                        'create_word_generator_component',
                        'render_word_generation_panel'
                    ]
                    
                    found_dependencies = []
                    lines = content.split('\n')
                    
                    for i, line in enumerate(lines, 1):
                        for pattern in word_generator_patterns:
                            if pattern in line:
                                found_dependencies.append({
                                    'line': i,
                                    'content': line.strip(),
                                    'pattern': pattern
                                })
                    
                    if found_dependencies:
                        dependencies[file_path] = found_dependencies
                        
                except Exception as e:
                    continue
    
    print("📋 发现的word_generator依赖:")
    if dependencies:
        for file_path, deps in dependencies.items():
            print(f"\n📄 {file_path}")
            for dep in deps[:3]:  # 只显示前3个
                print(f"   第{dep['line']}行: {dep['content'][:80]}...")
    else:
        print("   ✅ 未发现其他文件依赖word_generator组件")
    
    return dependencies

def generate_removal_steps():
    """生成移除步骤"""
    print(f"\n📋 生成移除步骤")
    print("=" * 80)
    
    removal_steps = {
        "步骤1": {
            "操作": "备份word_generator.py文件",
            "命令": "cp word_generator.py word_generator.py.backup",
            "原因": "安全起见，先备份再删除"
        },
        
        "步骤2": {
            "操作": "检查并移除word_generator的导入",
            "文件": "需要检查的文件列表",
            "原因": "避免导入错误"
        },
        
        "步骤3": {
            "操作": "移除word_generator.py文件",
            "命令": "rm streamlit_team_management_modular/components/word_generator.py",
            "原因": "删除冗余的独立界面组件"
        },
        
        "步骤4": {
            "操作": "清理相关的导航和调用",
            "文件": "app.py, ai_chat.py等",
            "原因": "确保没有死链接或调用"
        },
        
        "步骤5": {
            "操作": "测试自动Word生成功能",
            "验证": "确保_auto_generate_word_document()正常工作",
            "原因": "确保核心功能不受影响"
        }
    }
    
    for step, details in removal_steps.items():
        print(f"\n📋 {step}")
        for key, value in details.items():
            print(f"   {key}: {value}")
    
    return removal_steps

def verify_auto_word_generation_intact():
    """验证自动Word生成功能完整性"""
    print(f"\n✅ 验证自动Word生成功能完整性")
    print("=" * 80)
    
    # 检查关键文件是否存在
    critical_files = [
        "streamlit_team_management_modular/word_generator_service.py",
        "streamlit_team_management_modular/services/fashion_workflow_service.py",
        "streamlit_team_management_modular/config/settings.py"
    ]
    
    integrity_check = {}
    
    for file_path in critical_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查关键函数/类是否存在
                key_components = []
                
                if 'word_generator_service.py' in file_path:
                    key_components = ['class WordGeneratorService', 'def generate_report']
                elif 'fashion_workflow_service.py' in file_path:
                    key_components = ['def _auto_generate_word_document']
                elif 'settings.py' in file_path:
                    key_components = ['class WordGeneratorSettings']
                
                found_components = []
                for component in key_components:
                    if component in content:
                        found_components.append(component)
                
                integrity_check[file_path] = {
                    'exists': True,
                    'key_components': key_components,
                    'found_components': found_components,
                    'integrity': len(found_components) == len(key_components)
                }
                
            except Exception as e:
                integrity_check[file_path] = {
                    'exists': True,
                    'error': str(e),
                    'integrity': False
                }
        else:
            integrity_check[file_path] = {
                'exists': False,
                'integrity': False
            }
    
    print("📋 关键文件完整性检查:")
    for file_path, check in integrity_check.items():
        status = "✅" if check['integrity'] else "❌"
        print(f"\n{status} {file_path}")
        if check.get('exists'):
            if check.get('found_components'):
                print(f"   找到组件: {check['found_components']}")
            if check.get('error'):
                print(f"   错误: {check['error']}")
        else:
            print(f"   文件不存在")
    
    return integrity_check

def main():
    """主函数"""
    print("🗑️ Word生成界面移除计划分析")
    print("=" * 80)
    
    print("🎯 目标:")
    print("   移除独立的Word生成界面 (用户反馈没有用)")
    print("   保留自动Word生成功能 (核心功能)")
    print("   确保不影响工作流的自动生成")
    
    # 1. 分析需要移除的组件
    removal_plan = analyze_word_components_to_remove()
    
    # 2. 检查依赖关系
    dependencies = check_word_generator_dependencies()
    
    # 3. 生成移除步骤
    removal_steps = generate_removal_steps()
    
    # 4. 验证自动生成功能完整性
    integrity_check = verify_auto_word_generation_intact()
    
    # 总结
    print(f"\n🎯 移除计划总结")
    print("=" * 80)
    
    print("🗑️ 将要移除:")
    print("   📄 streamlit_team_management_modular/components/word_generator.py")
    print("   🔗 相关的导入和调用")
    print("   🧭 可能的导航链接")
    
    print(f"\n✅ 将要保留:")
    print("   🔧 WordGeneratorService (核心服务)")
    print("   🤖 _auto_generate_word_document() (自动生成函数)")
    print("   ⚙️ WordGeneratorSettings (配置)")
    print("   ☕ Java Word生成器后端")
    
    print(f"\n⚠️ 注意事项:")
    print("   1. 移除前先备份文件")
    print("   2. 检查并清理所有依赖")
    print("   3. 测试自动生成功能正常")
    print("   4. 确保工作流不受影响")
    
    if dependencies:
        print(f"\n🔍 发现依赖文件:")
        for file_path in dependencies.keys():
            print(f"   📄 {file_path}")
        print("   需要先清理这些依赖再删除组件")
    else:
        print(f"\n✅ 未发现依赖，可以安全移除")

if __name__ == "__main__":
    main()
