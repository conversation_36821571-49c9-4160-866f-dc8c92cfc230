#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试实际的工作流服务调用
"""

import json
import os

def debug_actual_workflow_call():
    """调试实际的工作流服务调用"""
    print("🔍 调试实际的工作流服务调用")
    print("=" * 60)
    
    try:
        # 模拟实际的Streamlit环境
        import streamlit as st
        
        # 设置session state
        if 'user_id' not in st.session_state:
            st.session_state.user_id = 'user_e358df6703a9'
        
        from services.fashion_workflow_service import FashionWorkflowService
        
        # 创建工作流服务
        workflow_service = FashionWorkflowService('user_e358df6703a9')
        
        # 测试AI数据加载
        print("📄 步骤1: 测试AI数据加载")
        ai_data = workflow_service._load_ai_export_data("新建项目201")
        
        if ai_data:
            print("✅ AI数据加载成功")
            
            # 检查数据结构
            team_info = ai_data.get("team_info", {})
            ai_extracted_info = team_info.get("ai_extracted_info", {})
            basic_info = ai_extracted_info.get("basic_info", {})
            additional_info = ai_extracted_info.get("additional_info", {})
            
            print(f"📄 加载的数据结构:")
            print(f"   team_info存在: {'✅' if team_info else '❌'}")
            print(f"   ai_extracted_info存在: {'✅' if ai_extracted_info else '❌'}")
            print(f"   basic_info存在: {'✅' if basic_info else '❌'}")
            print(f"   additional_info存在: {'✅' if additional_info else '❌'}")
            
            print(f"\n📄 联系人信息:")
            print(f"   contact_person: '{basic_info.get('contact_person', 'MISSING')}'")
            print(f"   contact_phone: '{basic_info.get('contact_phone', 'MISSING')}'")
            print(f"   leader_name: '{basic_info.get('leader_name', 'MISSING')}'")
            print(f"   coach_name: '{additional_info.get('coach_name', 'MISSING')}'")
            
            # 模拟fashion_workflow.py中的team_data构建
            print(f"\n📄 步骤2: 模拟team_data构建")
            
            team_data = {
                "name": "新建项目201",
                "leader": basic_info.get("leader_name", ""),
                "coach": additional_info.get("coach_name", ""),
                "doctor": basic_info.get("team_doctor", ""),
                "contact_person": basic_info.get("contact_person", ""),
                "contact_phone": basic_info.get("contact_phone", "")
            }
            
            print(f"   构建的team_data:")
            for key, value in team_data.items():
                print(f"   {key}: '{value}'")
            
            # 检查关键字段
            contact_person_ok = bool(team_data.get('contact_person'))
            contact_phone_ok = bool(team_data.get('contact_phone'))
            
            print(f"\n📊 关键字段检查:")
            print(f"   contact_person有值: {'✅' if contact_person_ok else '❌'}")
            print(f"   contact_phone有值: {'✅' if contact_phone_ok else '❌'}")
            
            if not contact_person_ok or not contact_phone_ok:
                print(f"\n🔍 问题分析:")
                print(f"   原始AI数据中的联系人信息:")
                
                # 直接检查原始AI数据
                ai_file = "data/user_e358df6703a9/enhanced_ai_data/新建项目201_ai_data.json"
                with open(ai_file, 'r', encoding='utf-8') as f:
                    raw_ai_data = json.load(f)
                
                raw_basic_info = raw_ai_data.get('extracted_info', {}).get('basic_info', {})
                print(f"   原始contact_person: '{raw_basic_info.get('contact_person', 'MISSING')}'")
                print(f"   原始contact_phone: '{raw_basic_info.get('contact_phone', 'MISSING')}'")
                
                # 检查转换过程
                print(f"\n🔍 检查转换过程:")
                print(f"   ai_extracted_info类型: {type(ai_extracted_info)}")
                print(f"   ai_extracted_info内容: {ai_extracted_info}")
                
                if isinstance(ai_extracted_info, dict):
                    print(f"   ai_extracted_info键: {list(ai_extracted_info.keys())}")
                    
                    if 'basic_info' in ai_extracted_info:
                        converted_basic_info = ai_extracted_info['basic_info']
                        print(f"   转换后basic_info: {converted_basic_info}")
                        print(f"   转换后contact_person: '{converted_basic_info.get('contact_person', 'MISSING')}'")
                        print(f"   转换后contact_phone: '{converted_basic_info.get('contact_phone', 'MISSING')}'")
            
            # 测试实际的Word生成调用
            print(f"\n📄 步骤3: 测试实际Word生成")
            
            # 模拟球员数据
            players_data = [
                {
                    'name': '天依',
                    'jersey_number': '1',
                    'photo': 'data/user_e358df6703a9/photos/新建项目201/test.jpg'
                }
            ]
            
            # 调用实际的Word生成方法
            word_result = workflow_service._auto_generate_word_document(
                "新建项目201", {}, None
            )
            
            print(f"   Word生成结果: {word_result}")
            
            return ai_data
            
        else:
            print("❌ AI数据加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_auto_generate_word_method():
    """检查_auto_generate_word_document方法"""
    print(f"\n🔍 检查_auto_generate_word_document方法")
    print("=" * 60)
    
    try:
        import streamlit as st
        
        # 设置session state
        if 'user_id' not in st.session_state:
            st.session_state.user_id = 'user_e358df6703a9'
        
        from services.fashion_workflow_service import FashionWorkflowService
        
        # 创建工作流服务
        workflow_service = FashionWorkflowService('user_e358df6703a9')
        
        # 检查_auto_generate_word_document方法的实现
        print("📄 检查Word生成方法的数据传递:")
        
        # 我们需要查看这个方法如何处理team_data
        # 让我们直接调用并观察
        
        # 模拟调用
        team_name = "新建项目201"
        player_photo_mapping = {}
        logo_path = None
        
        # 这里我们需要检查方法内部如何构建team_data
        print(f"   调用参数:")
        print(f"   team_name: {team_name}")
        print(f"   player_photo_mapping: {player_photo_mapping}")
        print(f"   logo_path: {logo_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 调试实际的工作流服务调用")
    print("=" * 70)
    
    # 调试实际工作流调用
    workflow_result = debug_actual_workflow_call()
    
    # 检查Word生成方法
    word_method_result = check_auto_generate_word_method()
    
    print(f"\n📊 调试结果")
    print("=" * 70)
    
    if workflow_result:
        print("✅ 工作流服务调用正常")
    else:
        print("❌ 工作流服务调用有问题")
    
    if word_method_result:
        print("✅ Word生成方法检查完成")
    else:
        print("❌ Word生成方法检查失败")

if __name__ == "__main__":
    main()
