/*
 * XML Type:  ST_SplitType
 * Namespace: http://schemas.openxmlformats.org/drawingml/2006/chart
 * Java type: org.openxmlformats.schemas.drawingml.x2006.chart.STSplitType
 *
 * Automatically generated - do not modify.
 */
package org.openxmlformats.schemas.drawingml.x2006.chart.impl;

import javax.xml.namespace.QName;
import org.apache.xmlbeans.QNameSet;

/**
 * An XML ST_SplitType(@http://schemas.openxmlformats.org/drawingml/2006/chart).
 *
 * This is an atomic type that is a restriction of org.openxmlformats.schemas.drawingml.x2006.chart.STSplitType.
 */
public class STSplitTypeImpl extends org.apache.xmlbeans.impl.values.JavaStringEnumerationHolderEx implements org.openxmlformats.schemas.drawingml.x2006.chart.STSplitType {
    private static final long serialVersionUID = 1L;

    public STSplitTypeImpl(org.apache.xmlbeans.SchemaType sType) {
        super(sType, false);
    }

    protected STSplitTypeImpl(org.apache.xmlbeans.SchemaType sType, boolean b) {
        super(sType, b);
    }
}
