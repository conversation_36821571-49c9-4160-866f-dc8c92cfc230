{"test_start_time": "2025-09-01T01:42:20.301414", "test_end_time": "2025-09-01T01:44:31.349319", "test_team_name": "集成测试队", "test_results": {"1. 端到端流程测试": {"success": false, "error": "FashionWorkflowService._auto_generate_word_document() missing 1 required positional argument: 'player_photo_mapping'"}, "2. 队徽生成验证": {"success": true, "logo_file_path": "assets/logos\\集成测试队_logo_20250901_014429.png", "logo_description": "为“集成测试队”设计的队徽将体现现代风格，融入蓝色调，以传达团队精神和专业性。以下是详细的设计描述：\n\n1. **主要图案元素**：\n   - **齿轮与足球的结合**：队徽的核心元素是一枚齿轮，象征集成与协调，内部嵌入一个足球，代表团队的足球竞技属性。齿轮的设计为现代感的几何形状，象征着精密和合作。\n   - **并列的三条波浪线**：齿轮下方延伸出三条波浪线，象征球队的前进动力和不懈进取，同时代表团队成员的紧密联系与协作。\n\n2. **颜色搭配**：\n   - **深蓝色**：作为队徽的主色调，深蓝色展现了球队的专业性和坚定性。\n   - **亮蓝色**：用于齿轮与足球的细节部分，增加现代感和视觉冲击力。\n   - **白色**：用于队名“集成测试队”的字样和波浪线的高光部分，提供良好的对比度，使得整体设计简洁且清晰。\n\n3. **整体布局**：\n   - 队徽采用圆形盾牌结构，象征保护和团结，寓意球队团结一心、共同进退。\n   - 中心放置齿轮与足球的结合图案，使得队徽在视觉上有一个明确的焦点。\n   - 队徽的上方弧形排列队名“集成测试队”，下方则是球队的成立年份，以平衡布局。\n\n4. **寓意说明**：\n   - 齿轮象征团队的各个部分和谐合作，不仅展现了“集成”的核心理念，也强调了团队精神。\n   - 圆形的设计传达出“无间断的支持和相互依靠”的概念，强调团队的坚不可摧。\n   - 颜色搭配则旨在传递一种可靠性和信赖感，蓝色系的选择让人联想到科技与创新，符合现代足球队的形象。\n\n这个队徽设计旨在通过简洁明了的图案和现代的设计手法，使“集成测试队”的团队精神和专业性一目了然，易于识别且富有意义。", "file_info": {"file_size": 3147861, "dimensions": [1024, 1024], "mode": "RGB", "format": "PNG", "accessible": true, "modified": "2025-09-01 01:44:29"}}, "3. 数据流追踪测试": {"success": true, "data_locations": {"data": {"exists": true, "files": ["default_user"], "file_count": 1}, "streamlit_team_management_modular/data": {"exists": true, "files": ["37d53472d725", "actual_template_test", "actual_test", "backup_info.json", "cache_test_ff795077", "cleanup_backup_record.json", "cleanup_log.txt", "clean_test", "color_fix_test", "color_fix_test_user", "color_test", "color_verification", "complete_fix_test", "complete_workflow_test", "comprehensive_test", "debug_test", "debug_user", "default_user", "file_manager.py", "final_debug", "final_fix_test", "final_test", "final_verification", "fixed_example_ai_export.json", "fix_test", "flow_test", "intelligent_matching_fix_test", "java_debug_test", "latest_test", "manual_test", "manual_tianyi909", "migration_log.txt", "new_test", "placeholder_debug", "player_repository.py", "real_user_test", "spaced_test", "team_1.json", "team_121311.json", "team_2.json", "team_33333.json", "team_588999.json", "team_589556.json", "team_66544.json", "team_995541.json", "team_default.json", "team_name_test", "team_repository.py", "team_测试AI感知.json", "template_15players.docx", "test", "test_real_logo", "test_user", "tianyi369_debug", "tianyi909_debug", "unified_storage_test_report.json", "user_f38e6d57530a", "user_test", "verbose_debug", "__init__.py", "__pycache__"], "file_count": 61}, "cache": {"exists": true, "files": [], "file_count": 0}, "streamlit_team_management_modular/cache": {"exists": true, "files": [], "file_count": 0}}, "workflow_data_files": [".\\streamlit_team_management_modular\\data\\user_f38e6d57530a\\fashion_workflow\\workflow_天依700001_20250831_190945.json"]}, "4. Word模板分析": {"success": true, "template_files": ["word_zc\\template_15players.docx", "word_zc\\template_15players_backup.docx", "word_zc\\template_15players_backup_before_color_fix.docx", "word_zc\\template_15players_clean.docx", "word_zc\\template_15players_fixed.docx", "word_zc\\~$mplate_15players.docx", "word_zc\\ai-football-generator\\realistic_template.docx", "word_zc\\ai-football-generator\\simple_template.docx", "word_zc\\ai-football-generator\\simple_test_template.docx", "word_zc\\ai-football-generator\\template.docx", "word_zc\\ai-football-generator\\template_15players_copy.docx", "word_zc\\ai-football-generator\\template_original_backup.docx", "word_zc\\ai-football-generator\\~$mplate.docx", "word_zc\\ai-football-generator\\output\\ai_football_registration_1755753028386.docx", "word_zc\\ai-football-generator\\output\\ai_football_registration_1755753110150.docx", "word_zc\\ai-football-generator\\output\\ai_football_registration_1755753110448.docx", "word_zc\\ai-football-generator\\output\\ai_football_registration_1755753171407.docx", "word_zc\\ai-football-generator\\output\\ai_football_registration_1755753171694.docx", "word_zc\\ai-football-generator\\output\\ai_football_registration_1755753444650.docx", "word_zc\\ai-football-generator\\output\\ai_football_registration_1755753445137.docx", "word_zc\\ai-football-generator\\output\\ai_football_registration_1755753790870.docx", "word_zc\\ai-football-generator\\output\\ai_football_registration_1755753875983.docx", "word_zc\\ai-football-generator\\output\\ai_football_registration_1755753876424.docx", "word_zc\\ai-football-generator\\output\\Python集成测试队_registration_1755763417567.docx", "word_zc\\ai-football-generator\\output\\Python集成测试队_registration_1756531448671.docx", "word_zc\\ai-football-generator\\output\\test_output_1755414238270.docx", "word_zc\\ai-football-generator\\output\\test_output_1755414305917.docx", "word_zc\\ai-football-generator\\output\\test_output_1755415340017.docx", "word_zc\\ai-football-generator\\output\\太河镇人民政府_registration_1755761757879.docx", "word_zc\\ai-football-generator\\output\\太河镇人民政府_registration_1755761760474.docx", "word_zc\\ai-football-generator\\output\\太河镇人民政府_registration_1755761969104.docx", "word_zc\\ai-football-generator\\output\\太河镇人民政府_registration_1755761971204.docx", "word_zc\\ai-football-generator\\output\\干净模板最终测试队_registration_1756537545444.docx", "word_zc\\ai-football-generator\\output\\测试15人模板队_registration_1756531869560.docx", "word_zc\\ai-football-generator\\output\\测试realistic模板队_registration_1756531491422.docx", "word_zc\\ai-football-generator\\output\\测试simple_test模板队_registration_1756531574748.docx", "word_zc\\ai-football-generator\\output\\测试simple模板队_registration_1756531531853.docx"], "template_analysis": {"word_zc\\template_15players.docx": {"size": 18167, "modified": "2025-08-30 16:00:16", "accessible": true, "analysis_attempted": true}, "word_zc\\template_15players_backup.docx": {"size": 18459, "modified": "2025-08-25 11:36:08", "accessible": true, "analysis_attempted": true}, "word_zc\\template_15players_backup_before_color_fix.docx": {"size": 18167, "modified": "2025-08-30 16:00:16", "accessible": true, "analysis_attempted": true}, "word_zc\\template_15players_clean.docx": {"size": 12647, "modified": "2025-08-30 15:04:13", "accessible": true, "analysis_attempted": true}, "word_zc\\template_15players_fixed.docx": {"size": 15598, "modified": "2025-08-30 18:15:01", "accessible": true, "analysis_attempted": true}, "word_zc\\~$mplate_15players.docx": {"size": 162, "modified": "2025-08-31 17:10:30", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\realistic_template.docx": {"size": 37152, "modified": "2025-08-21 13:17:24", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\simple_template.docx": {"size": 17508, "modified": "2025-08-17 13:55:16", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\simple_test_template.docx": {"size": 37101, "modified": "2025-08-21 13:09:42", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\template.docx": {"size": 17882, "modified": "2025-08-22 14:11:51", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\template_15players_copy.docx": {"size": 18459, "modified": "2025-08-25 11:36:08", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\template_original_backup.docx": {"size": 17882, "modified": "2025-08-22 14:11:51", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\~$mplate.docx": {"size": 162, "modified": "2025-08-21 13:09:39", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\ai_football_registration_1755753028386.docx": {"size": 37170, "modified": "2025-08-21 13:10:28", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\ai_football_registration_1755753110150.docx": {"size": 37170, "modified": "2025-08-21 13:11:50", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\ai_football_registration_1755753110448.docx": {"size": 521863, "modified": "2025-08-21 13:11:50", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\ai_football_registration_1755753171407.docx": {"size": 37199, "modified": "2025-08-21 13:12:51", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\ai_football_registration_1755753171694.docx": {"size": 820737, "modified": "2025-08-21 13:12:51", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\ai_football_registration_1755753444650.docx": {"size": 37220, "modified": "2025-08-21 13:17:24", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\ai_football_registration_1755753445137.docx": {"size": 820796, "modified": "2025-08-21 13:17:25", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\ai_football_registration_1755753790870.docx": {"size": 37246, "modified": "2025-08-21 13:23:10", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\ai_football_registration_1755753875983.docx": {"size": 37246, "modified": "2025-08-21 13:24:36", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\ai_football_registration_1755753876424.docx": {"size": 820836, "modified": "2025-08-21 13:24:36", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\Python集成测试队_registration_1755763417567.docx": {"size": 347826, "modified": "2025-08-21 16:03:39", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\Python集成测试队_registration_1756531448671.docx": {"size": 15298, "modified": "2025-08-30 13:24:10", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\test_output_1755414238270.docx": {"size": 67, "modified": "2025-08-17 15:03:59", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\test_output_1755414305917.docx": {"size": 445978, "modified": "2025-08-17 15:05:07", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\test_output_1755415340017.docx": {"size": 467150, "modified": "2025-08-17 15:22:21", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\太河镇人民政府_registration_1755761757879.docx": {"size": 347836, "modified": "2025-08-21 15:36:00", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\太河镇人民政府_registration_1755761760474.docx": {"size": 347836, "modified": "2025-08-21 15:36:00", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\太河镇人民政府_registration_1755761969104.docx": {"size": 347836, "modified": "2025-08-21 15:39:30", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\太河镇人民政府_registration_1755761971204.docx": {"size": 347836, "modified": "2025-08-21 15:39:31", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\干净模板最终测试队_registration_1756537545444.docx": {"size": 12798, "modified": "2025-08-30 15:05:46", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\测试15人模板队_registration_1756531869560.docx": {"size": 280903, "modified": "2025-08-30 13:31:12", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\测试realistic模板队_registration_1756531491422.docx": {"size": 37348, "modified": "2025-08-30 13:24:52", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\测试simple_test模板队_registration_1756531574748.docx": {"size": 37245, "modified": "2025-08-30 13:26:16", "accessible": true, "analysis_attempted": true}, "word_zc\\ai-football-generator\\output\\测试simple模板队_registration_1756531531853.docx": {"size": 280373, "modified": "2025-08-30 13:25:33", "accessible": true, "analysis_attempted": true}}}, "5. Word处理服务测试": {"success": true, "word_services": [], "service_count": 0}, "6. 文件系统集成测试": {"success": true, "directory_status": {"assets/logos": {"exists": true, "readable": true, "writable": true, "files": 6}, "temp_files": {"exists": true, "readable": true, "writable": true, "files": 2}, "word_zc": {"exists": true, "readable": true, "writable": true, "files": 8}, "streamlit_team_management_modular/data": {"exists": true, "readable": true, "writable": true, "files": 61}}}, "7. 配置和依赖检查": {"success": true, "package_status": {"python-docx": {"installed": false}, "docx": {"installed": true}, "openpyxl": {"installed": true}, "PIL": {"installed": true}, "Pillow": {"installed": false}}}, "8. 错误处理验证": {"success": true, "log_files": [".\\logo_white_bg_blend_1756660976.png", ".\\logo_white_bg_composite_1756660976.png", ".\\logo_white_bg_paste_1756660976.png", ".\\test_ai_image_generation_logs.py", ".\\test_ai_logo_generation_complete.py", ".\\test_ai_logo_issues_verification.py", ".\\test_all_log_sources.py", ".\\test_final_log_analysis.py", ".\\test_frontend_logs.py", ".\\test_logo_to_word_integration.py", ".\\test_log_suppression_result.py", ".\\test_log_suppression_solutions.py"], "recent_errors": [{"file": ".\\test_ai_image_generation_logs.py", "content": "elif current_method and re.search(r'st\\.(info|success|error|warning)\\(', line):"}, {"file": ".\\test_ai_image_generation_logs.py", "content": "except Exception as e:"}, {"file": ".\\test_ai_logo_generation_complete.py", "content": "except Exception as e:"}, {"file": ".\\test_ai_logo_generation_complete.py", "content": "except Exception as e:"}, {"file": ".\\test_ai_logo_generation_complete.py", "content": "except Exception as e:"}, {"file": ".\\test_ai_logo_issues_verification.py", "content": "except Exception as e:"}, {"file": ".\\test_ai_logo_issues_verification.py", "content": "except Exception as e:"}, {"file": ".\\test_ai_logo_issues_verification.py", "content": "except Exception as e:"}, {"file": ".\\test_ai_logo_issues_verification.py", "content": "except Exception as e:"}, {"file": ".\\test_ai_logo_issues_verification.py", "content": "except ImportError:"}]}}}