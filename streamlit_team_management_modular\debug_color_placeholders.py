#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试颜色占位符问题
"""

import zipfile
import xml.etree.ElementTree as ET
import os
import re

def debug_color_placeholders_in_template():
    """调试模板中的颜色占位符"""
    print("🔍 调试模板中的颜色占位符")
    print("=" * 60)
    
    template_path = "../word_zc/template_15players_fixed.docx"
    
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        return
    
    try:
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                print("📄 查找颜色相关占位符:")
                
                # 查找所有占位符
                placeholders = re.findall(r'\{\{[^}]+\}\}', content)
                unique_placeholders = list(set(placeholders))
                
                # 查找颜色相关占位符
                color_placeholders = []
                for placeholder in unique_placeholders:
                    if any(keyword in placeholder.lower() for keyword in ['color', 'colour', '颜色', 'jersey', 'shorts', 'socks', 'goalkeeper']):
                        color_placeholders.append(placeholder)
                
                print(f"   找到{len(color_placeholders)}个颜色相关占位符:")
                for placeholder in sorted(color_placeholders):
                    print(f"     • {placeholder}")
                
                # 检查分割的颜色占位符
                print(f"\n📄 检查分割的颜色占位符:")
                color_keywords = ['jerseyColor', 'shortsColor', 'socksColor', 'goalkeeperKitColor']
                
                for keyword in color_keywords:
                    if keyword in content:
                        print(f"   ✅ 找到 {keyword}")
                        
                        # 查找上下文
                        lines = content.split('\n')
                        for i, line in enumerate(lines):
                            if keyword in line:
                                print(f"     第{i+1}行: {line.strip()}")
                                break
                    else:
                        print(f"   ❌ 未找到 {keyword}")
                
                # 检查Java程序期望的占位符格式
                print(f"\n📄 检查Java程序期望的占位符格式:")
                expected_placeholders = [
                    '{{jerseyColor}}',
                    '{{shortsColor}}', 
                    '{{socksColor}}',
                    '{{goalkeeperKitColor}}'
                ]
                
                for expected in expected_placeholders:
                    if expected in content:
                        print(f"   ✅ 找到期望格式: {expected}")
                    else:
                        print(f"   ❌ 未找到期望格式: {expected}")
                        
                        # 查找可能的变体
                        keyword = expected.replace('{{', '').replace('}}', '')
                        if keyword in content:
                            print(f"     但找到关键词: {keyword}")
                
                return color_placeholders
                
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        return []

def test_java_color_processing():
    """测试Java颜色处理"""
    print(f"\n🔍 测试Java颜色处理")
    print("=" * 60)
    
    try:
        import json
        import subprocess
        
        # 创建包含颜色信息的测试数据
        test_data = {
            "teamInfo": {
                "title": "颜色测试报名表",
                "organizationName": "颜色测试队",
                "teamLeader": "测试领队",
                "coach": "测试教练",
                "teamDoctor": "测试队医",
                "contactPerson": "测试联系人",
                "contactPhone": "13800138000",
                "jerseyColor": "红色",
                "shortsColor": "蓝色",
                "socksColor": "白色",
                "goalkeeperKitColor": "黄色"
            },
            "players": [
                {
                    "number": "1",
                    "name": "测试球员",
                    "photoPath": "java_word_photos/player1.png"
                }
            ],
            "config": {
                "templatePath": "template_15players_fixed.docx",
                "outputDir": "output",
                "photosDir": "java_word_photos"
            }
        }
        
        # Java工作目录
        java_dir = "../word_zc/ai-football-generator"
        
        # 写入测试文件
        test_file = os.path.join(java_dir, "test_colors.json")
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建颜色测试文件")
        print(f"📄 测试数据:")
        team_info = test_data['teamInfo']
        print(f"   jerseyColor: '{team_info['jerseyColor']}'")
        print(f"   shortsColor: '{team_info['shortsColor']}'")
        print(f"   socksColor: '{team_info['socksColor']}'")
        print(f"   goalkeeperKitColor: '{team_info['goalkeeperKitColor']}'")
        
        # 运行Java程序
        print(f"\n🚀 运行Java程序...")
        result = subprocess.run(
            ["java", "-cp", "target/word-generator.jar", "CommandLineMain", "test_colors.json"],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=30,
            errors='ignore',
            cwd=java_dir
        )
        
        print(f"📊 返回码: {result.returncode}")
        
        if result.stdout:
            print(f"\n📝 Java标准输出:")
            for line in result.stdout.split('\n'):
                if line.strip():
                    print(f"   {line}")
        
        if result.stderr:
            print(f"\n📝 Java错误输出:")
            for line in result.stderr.split('\n'):
                if line.strip():
                    print(f"   {line}")
        
        # 检查生成的文件
        if result.returncode == 0:
            print("✅ Java程序运行成功")
            
            output_dir = os.path.join(java_dir, "output")
            if os.path.exists(output_dir):
                files = [f for f in os.listdir(output_dir) if f.endswith('.docx')]
                if files:
                    latest_file = max([os.path.join(output_dir, f) for f in files], 
                                    key=os.path.getmtime)
                    print(f"📄 生成文件: {os.path.basename(latest_file)}")
                    
                    # 检查生成的文件内容
                    return check_java_generated_colors(latest_file, test_data['teamInfo'])
        else:
            print(f"❌ Java程序运行失败")
            return False
        
    except Exception as e:
        print(f"❌ Java颜色测试失败: {e}")
        return False
    finally:
        # 清理测试文件
        test_file = os.path.join("../word_zc/ai-football-generator", "test_colors.json")
        if os.path.exists(test_file):
            os.remove(test_file)

def check_java_generated_colors(file_path, team_info):
    """检查Java生成的颜色"""
    print(f"\n🔍 检查Java生成的颜色")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    # 检查各种颜色
                    colors = {
                        'jerseyColor': team_info.get('jerseyColor', ''),
                        'shortsColor': team_info.get('shortsColor', ''),
                        'socksColor': team_info.get('socksColor', ''),
                        'goalkeeperKitColor': team_info.get('goalkeeperKitColor', '')
                    }
                    
                    print(f"📄 Java生成的颜色检查:")
                    color_results = {}
                    
                    for color_type, color_value in colors.items():
                        if color_value:
                            has_color = color_value in full_text
                            print(f"   {color_type} '{color_value}': {'✅ 找到' if has_color else '❌ 未找到'}")
                            color_results[color_type] = has_color
                        else:
                            print(f"   {color_type}: ❌ 数据中无此字段")
                            color_results[color_type] = False
                    
                    # 显示服装颜色区域的内容
                    print(f"\n📄 服装颜色区域内容:")
                    words = full_text.split()
                    for i, word in enumerate(words):
                        if any(keyword in word for keyword in ['服装', '颜色', '球衣', '球裤', '球袜']):
                            start = max(0, i-3)
                            end = min(len(words), i+15)
                            context = ' '.join(words[start:end])
                            print(f"   {context}")
                    
                    return color_results
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return {}
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return {}

def main():
    """主函数"""
    print("🎯 调试颜色占位符问题")
    print("=" * 70)
    
    # 1. 调试模板中的颜色占位符
    color_placeholders = debug_color_placeholders_in_template()
    
    # 2. 测试Java颜色处理
    java_color_result = test_java_color_processing()
    
    # 综合分析
    print(f"\n📊 颜色问题分析结果")
    print("=" * 70)
    
    if color_placeholders:
        print(f"🔍 模板占位符分析:")
        print(f"   找到{len(color_placeholders)}个颜色相关占位符")
        
        # 检查是否有标准格式的占位符
        standard_formats = ['{{jerseyColor}}', '{{shortsColor}}', '{{socksColor}}', '{{goalkeeperKitColor}}']
        found_standard = [p for p in color_placeholders if p in standard_formats]
        
        if found_standard:
            print(f"   ✅ 找到标准格式占位符: {len(found_standard)}个")
        else:
            print(f"   ❌ 未找到标准格式占位符")
            print(f"   💡 可能原因: 占位符被XML分割或格式不匹配")
    
    if java_color_result:
        successful_colors = sum(1 for result in java_color_result.values() if result)
        total_colors = len(java_color_result)
        
        print(f"\n🔍 Java颜色处理:")
        if successful_colors == total_colors:
            print(f"   ✅ Java能够正确处理颜色字段")
            print(f"   💡 问题可能在Python的数据传递")
        elif successful_colors > 0:
            print(f"   ⚠️ Java部分处理颜色字段 ({successful_colors}/{total_colors})")
        else:
            print(f"   ❌ Java无法处理颜色字段")
            print(f"   💡 问题可能在模板占位符格式")
    
    print(f"\n🎯 颜色问题诊断:")
    print(f"   基于前面成功修复联系人、团队名称、自动填充问题的经验")
    print(f"   颜色字段问题可能的原因:")
    print(f"   1. 模板中颜色占位符被XML分割（类似联系人问题）")
    print(f"   2. 占位符格式与Java程序期望不匹配")
    print(f"   3. WordGeneratorService数据传递有问题")
    
    print(f"\n💡 建议修复策略:")
    print(f"   1. 检查模板中颜色占位符的实际格式")
    print(f"   2. 确保Java程序能够识别颜色占位符")
    print(f"   3. 验证Python到Java的数据传递")

if __name__ == "__main__":
    main()
