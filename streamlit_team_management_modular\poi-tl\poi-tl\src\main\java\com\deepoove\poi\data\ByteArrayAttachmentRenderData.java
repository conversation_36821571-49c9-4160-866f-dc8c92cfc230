/*
 * Copyright 2014-2025 Sayi
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.deepoove.poi.data;

public class ByteArrayAttachmentRenderData extends AttachmentRenderData {

    private static final long serialVersionUID = 1L;

    private byte[] bytes;

    public ByteArrayAttachmentRenderData(byte[] bytes) {
        this(bytes, null);
    }

    public ByteArrayAttachmentRenderData(byte[] bytes, AttachmentType fileType) {
        this.bytes = bytes;
        this.setFileType(fileType);
    }

    @Override
    public byte[] readAttachmentData() {
        return bytes;
    }
}
