#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前端调试信息
Test Frontend Debug Information
"""

import os
import re
from pathlib import Path

def analyze_frontend_debug_info():
    """分析前端调试信息"""
    print("🔍 分析前端界面调试信息")
    print("=" * 80)
    
    # 从Playwright快照中发现的调试信息
    debug_info_found = [
        {
            "content": "🔍 TeamService调试 - 获取统计信息:",
            "type": "调试标题",
            "severity": "🔴 高可见性"
        },
        {
            "content": "球队名称: 65415002",
            "type": "调试数据",
            "severity": "🟡 中可见性"
        },
        {
            "content": "当前用户ID: user_825c58641ee7",
            "type": "调试数据",
            "severity": "🟡 中可见性"
        },
        {
            "content": "加载的球队对象: Team(team_info=TeamInfo(name='65415002', created_at='2025-08-29T17:47:25.136288', display_name='65415002'), players=[Player(id='5591104a-eb61-4443-a871-b3e0c198ce68', name='666', jersey_number='1', photo='d0b4af760f4f4450af39c9d2fa6af35f.jpg', created_at='2025-08-29T18:02:25.256060', updated_at='2025-08-29T18:02:25.256060')])",
            "type": "详细对象信息",
            "severity": "🔴 高可见性 - 非常冗长"
        },
        {
            "content": "✅ 球队统计: 总数=1, 有照片=1, 完成度=100.0%",
            "type": "调试结果",
            "severity": "🟡 中可见性"
        }
    ]
    
    print("📋 发现的调试信息:")
    for i, info in enumerate(debug_info_found, 1):
        print(f"\n{i}. {info['severity']}")
        print(f"   类型: {info['type']}")
        print(f"   内容: {info['content'][:100]}{'...' if len(info['content']) > 100 else ''}")
    
    return debug_info_found

def search_debug_sources():
    """搜索调试信息的源头"""
    print(f"\n🔍 搜索调试信息源头")
    print("=" * 80)
    
    # 搜索关键词
    debug_keywords = [
        "TeamService调试",
        "获取统计信息",
        "球队名称:",
        "当前用户ID:",
        "加载的球队对象:",
        "球队统计:"
    ]
    
    search_results = {}
    
    # 搜索目录
    search_dirs = [
        "streamlit_team_management_modular/services",
        "streamlit_team_management_modular/components", 
        "streamlit_team_management_modular/pages",
        "streamlit_team_management_modular"
    ]
    
    for search_dir in search_dirs:
        if not os.path.exists(search_dir):
            continue
            
        for root, dirs, files in os.walk(search_dir):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        for keyword in debug_keywords:
                            if keyword in content:
                                if file_path not in search_results:
                                    search_results[file_path] = []
                                
                                # 找到包含关键词的行
                                lines = content.split('\n')
                                for i, line in enumerate(lines, 1):
                                    if keyword in line:
                                        search_results[file_path].append({
                                            'line': i,
                                            'keyword': keyword,
                                            'content': line.strip()
                                        })
                                        
                    except Exception as e:
                        continue
    
    print("📁 找到调试信息的文件:")
    for file_path, findings in search_results.items():
        print(f"\n📄 {file_path}")
        for finding in findings:
            print(f"   第{finding['line']}行: {finding['content'][:80]}...")
    
    return search_results

def analyze_team_service_debug():
    """分析TeamService的调试信息"""
    print(f"\n🔍 分析TeamService调试信息")
    print("=" * 80)
    
    team_service_files = [
        "streamlit_team_management_modular/services/team_service.py",
        "streamlit_team_management_modular/components/sidebar.py",
        "streamlit_team_management_modular/app.py"
    ]
    
    debug_patterns = [
        r'st\.write.*调试',
        r'st\.info.*调试',
        r'st\.success.*调试',
        r'st\.error.*调试',
        r'st\.warning.*调试',
        r'print.*调试',
        r'st\.write.*TeamService',
        r'st\.info.*TeamService',
        r'st\.write.*获取统计信息',
        r'st\.write.*球队对象',
        r'st\.write.*当前用户ID'
    ]
    
    findings = {}
    
    for file_path in team_service_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                lines = content.split('\n')
                file_findings = []
                
                for i, line in enumerate(lines, 1):
                    for pattern in debug_patterns:
                        if re.search(pattern, line, re.IGNORECASE):
                            file_findings.append({
                                'line': i,
                                'pattern': pattern,
                                'content': line.strip(),
                                'is_debug': True
                            })
                
                if file_findings:
                    findings[file_path] = file_findings
                    
            except Exception as e:
                print(f"   ❌ 读取失败: {file_path} - {e}")
    
    print("📋 TeamService调试信息分析:")
    for file_path, file_findings in findings.items():
        print(f"\n📄 {file_path}")
        for finding in file_findings:
            print(f"   🔍 第{finding['line']}行: {finding['content'][:80]}...")
    
    return findings

def search_specific_debug_strings():
    """搜索特定的调试字符串"""
    print(f"\n🎯 搜索特定调试字符串")
    print("=" * 80)
    
    # 从前端看到的具体调试信息
    specific_strings = [
        "🔍 TeamService调试 - 获取统计信息:",
        "球队名称:",
        "当前用户ID:",
        "加载的球队对象:",
        "✅ 球队统计:"
    ]
    
    results = {}
    
    # 搜索整个streamlit目录
    for root, dirs, files in os.walk("streamlit_team_management_modular"):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    for debug_string in specific_strings:
                        if debug_string in content:
                            if file_path not in results:
                                results[file_path] = []
                            
                            # 找到具体行号
                            lines = content.split('\n')
                            for i, line in enumerate(lines, 1):
                                if debug_string in line:
                                    results[file_path].append({
                                        'line': i,
                                        'debug_string': debug_string,
                                        'full_line': line.strip()
                                    })
                                    
                except Exception as e:
                    continue
    
    print("🎯 找到具体调试字符串:")
    for file_path, findings in results.items():
        print(f"\n📄 {file_path}")
        for finding in findings:
            print(f"   🔍 第{finding['line']}行: {finding['debug_string']}")
            print(f"      完整行: {finding['full_line']}")
    
    return results

def generate_debug_removal_plan():
    """生成调试信息移除计划"""
    print(f"\n📋 生成调试信息移除计划")
    print("=" * 80)
    
    removal_plan = {
        "高优先级移除": [
            {
                "target": "🔍 TeamService调试 - 获取统计信息:",
                "reason": "明显的调试标题，用户不应该看到",
                "action": "删除或注释掉相关st.write/st.info语句"
            },
            {
                "target": "加载的球队对象: Team(...)",
                "reason": "详细的对象信息，非常冗长，纯调试用途",
                "action": "删除或用条件判断包装"
            }
        ],
        
        "中优先级移除": [
            {
                "target": "球队名称: xxx",
                "reason": "调试数据，用户界面不需要",
                "action": "删除或移到日志"
            },
            {
                "target": "当前用户ID: xxx",
                "reason": "内部ID信息，用户不需要看到",
                "action": "删除或移到日志"
            },
            {
                "target": "✅ 球队统计: 总数=x, 有照片=x, 完成度=x%",
                "reason": "调试格式的统计信息，可以用更友好的方式显示",
                "action": "重构为用户友好的显示方式"
            }
        ],
        
        "检查项": [
            {
                "target": "所有包含'调试'关键词的输出",
                "reason": "确保没有遗漏的调试信息",
                "action": "全局搜索并清理"
            }
        ]
    }
    
    for category, items in removal_plan.items():
        print(f"\n🎯 {category}:")
        for item in items:
            print(f"   📌 目标: {item['target']}")
            print(f"      原因: {item['reason']}")
            print(f"      行动: {item['action']}")
    
    return removal_plan

def main():
    """主函数"""
    print("🔍 前端调试信息分析")
    print("=" * 80)
    
    print("🎯 分析目标:")
    print("   通过Playwright发现前端界面显示了调试信息")
    print("   需要找到这些调试信息的源头并制定清理计划")
    
    # 1. 分析发现的调试信息
    debug_info = analyze_frontend_debug_info()
    
    # 2. 搜索调试信息源头
    search_results = search_debug_sources()
    
    # 3. 分析TeamService调试
    team_service_debug = analyze_team_service_debug()
    
    # 4. 搜索特定调试字符串
    specific_results = search_specific_debug_strings()
    
    # 5. 生成移除计划
    removal_plan = generate_debug_removal_plan()
    
    # 总结
    print(f"\n🎯 分析总结")
    print("=" * 80)
    
    print("✅ 主要发现:")
    print(f"   🔍 发现 {len(debug_info)} 个调试信息显示在前端")
    print(f"   📁 涉及 {len(search_results)} 个源文件")
    print(f"   🎯 主要问题：TeamService调试信息泄露到用户界面")
    
    print(f"\n⚠️ 问题严重性:")
    print("   🔴 高：显示详细对象信息和调试标题")
    print("   🟡 中：显示内部ID和调试数据")
    print("   🟢 低：部分统计信息格式不够用户友好")
    
    print(f"\n💡 解决建议:")
    print("   1. 立即移除所有包含'调试'关键词的输出")
    print("   2. 删除详细对象信息的显示")
    print("   3. 将内部ID信息移到日志而不是界面")
    print("   4. 重构统计信息为用户友好格式")
    
    print(f"\n🎉 结论:")
    print("   ✅ 成功定位了前端调试信息的源头")
    print("   📋 制定了详细的清理计划")
    print("   🎯 可以开始清理工作，提升用户体验")

if __name__ == "__main__":
    main()
