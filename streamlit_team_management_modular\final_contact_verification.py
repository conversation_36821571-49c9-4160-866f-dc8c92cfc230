#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证联系人信息修复效果
"""

import sys
import os
import json
import subprocess
import zipfile
import xml.etree.ElementTree as ET
import shutil
import time

def test_python_with_clean_template():
    """测试Python使用干净模板"""
    print("🧪 最终验证：Python使用干净模板")
    print("=" * 60)
    
    try:
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 准备测试数据
        team_data = {
            'name': '最终验证联系人队',
            'leader': '张三',
            'coach': '李四',
            'doctor': '王五',
            'contact_person': '赵六',
            'contact_phone': '13800138000'
        }
        
        players_data = [
            {
                'name': '验证球员',
                'jersey_number': '10',
                'photo': 'word_zc/ai-football-generator/java_word_photos/player1.png'
            }
        ]
        
        print(f"📄 测试数据:")
        print(f"   contact_person: '{team_data['contact_person']}'")
        print(f"   contact_phone: '{team_data['contact_phone']}'")
        
        # 获取配置路径
        paths = app_settings.word_generator.get_absolute_paths("final_test", app_settings.paths)
        
        # 使用干净模板路径
        clean_template_path = "../word_zc/template_15players_clean.docx"
        
        # 创建Word生成服务，使用干净模板
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=clean_template_path,  # 直接使用干净模板
            output_dir=paths['output_dir']
        )
        
        # 生成Word文档
        print(f"\n🚀 运行Python集成（使用干净模板）...")
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print(f"✅ Python集成成功: {result['file_path']}")
            
            # 检查生成文件的内容
            return check_word_content(result['file_path'])
        else:
            print(f"❌ Python集成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ Python集成测试失败: {e}")
        return False

def check_word_content(file_path):
    """检查Word文档内容"""
    print(f"\n🔍 检查Word文档内容")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    # 检查联系人信息
                    has_contact_person = "赵六" in full_text
                    has_contact_phone = "13800138000" in full_text
                    has_placeholder_person = "{{contactPerson}}" in content
                    has_placeholder_phone = "{{contactPhone}}" in content
                    
                    print(f"📄 内容检查结果:")
                    print(f"   联系人'赵六': {'✅ 找到' if has_contact_person else '❌ 未找到'}")
                    print(f"   电话'13800138000': {'✅ 找到' if has_contact_phone else '❌ 未找到'}")
                    print(f"   占位符{{{{contactPerson}}}}: {'⚠️ 仍存在' if has_placeholder_person else '✅ 已替换'}")
                    print(f"   占位符{{{{contactPhone}}}}: {'⚠️ 仍存在' if has_placeholder_phone else '✅ 已替换'}")
                    
                    # 显示联系人相关的完整上下文
                    print(f"\n📄 联系人相关内容:")
                    words = full_text.split()
                    for i, word in enumerate(words):
                        if "联系人" in word:
                            start = max(0, i-3)
                            end = min(len(words), i+10)
                            context = ' '.join(words[start:end])
                            print(f"   联系人上下文: {context}")
                            break
                    
                    # 判断结果
                    if has_contact_person and has_contact_phone:
                        print(f"\n🎉 联系人信息显示成功！")
                        return True
                    else:
                        print(f"\n❌ 联系人信息仍未显示")
                        return False
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return False
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def create_user_guide():
    """创建用户使用指南"""
    print(f"\n📋 用户使用指南")
    print("=" * 60)
    
    guide = """
🎯 联系人信息自动化流程使用指南

✅ 问题已解决：
   • Word模板中的占位符分割问题已修复
   • 联系人信息现在能够正确显示在Word文档中
   • 完整的AI聊天→数据提取→Word生成流程已实现

🚀 使用步骤：

1. 在AI聊天中输入联系人信息：
   示例："我是张三，电话是13800138000，我们球队叫测试队"

2. AI自动提取信息：
   • 系统会自动识别联系人姓名和电话
   • 提取的信息会保存到球队数据中

3. 生成Word报名表：
   • 点击"生成Word报名表"按钮
   • 系统会自动将联系人信息填入模板

4. 查看结果：
   • 生成的Word文档中会显示：
     "球队联系人: 张三 电话：13800138000"

🔧 技术修复内容：
   • 修复了Word模板中被XML分割的占位符
   • 完善了Java代码中的联系人字段处理
   • 优化了Python数据映射逻辑

💡 注意事项：
   • 确保使用修复后的模板文件
   • 联系人信息会自动从AI聊天中提取
   • 如果没有输入联系人信息，会使用球队领队作为默认联系人
"""
    
    print(guide)
    
    # 保存指南到文件
    with open("联系人信息使用指南.txt", 'w', encoding='utf-8') as f:
        f.write(guide)
    
    print("✅ 使用指南已保存到: 联系人信息使用指南.txt")

def main():
    """主函数"""
    print("🎯 联系人信息修复最终验证")
    print("=" * 70)
    
    # 最终验证测试
    success = test_python_with_clean_template()
    
    # 显示最终结果
    print(f"\n📊 最终验证结果")
    print("=" * 70)
    
    if success:
        print("🎉 联系人信息问题完全解决！")
        print("✅ 干净模板工作正常")
        print("✅ 联系人信息能够正确显示在Word文档中")
        print("✅ Python集成正常工作")
        
        print(f"\n🔄 建议操作:")
        print(f"1. 替换原模板文件:")
        print(f"   手动复制 template_15players_clean.docx 替换 template_15players_fixed.docx")
        print(f"2. 重启Streamlit应用以使用新模板")
        
        print(f"\n🎯 用户现在可以:")
        print(f"   ✅ 在AI聊天中输入联系人和电话信息")
        print(f"   ✅ 系统自动提取并保存联系人信息")
        print(f"   ✅ 生成的Word报名表包含完整的联系人信息")
        print(f"   ✅ 联系人信息自动化流程完整实现")
        
        # 创建用户指南
        create_user_guide()
        
        print(f"\n🎉 恭喜！联系人信息功能已完全修复并可正常使用！")
        
    else:
        print("⚠️ 验证未完全成功")
        print("💡 建议进一步检查配置")

if __name__ == "__main__":
    main()
