#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的API替换测试
"""

import requests
import os
import time
import json

def test_302ai_v1_directly():
    """直接测试302.ai V1 API"""
    print("🧪 直接测试302.ai V1背景移除API")
    print("=" * 60)
    
    # API配置
    api_key = "sk-Y5BDQdWlSCr2nQUYvbeKhEIwzzzg5AzGq8nTFR7KArlQuM3o"
    base_url = "https://api.302.ai"
    
    # 查找测试图片
    test_images = [
        "streamlit_team_management_modular/photos/player1_cropped.png",
        "streamlit_team_management_modular/photos/player2_cropped.jpg",
        "streamlit_team_management_modular/photos/player3_cropped.jpg",
        "streamlit_team_management_modular/data/user_a13da2c47ed7/uploads/测试AI感知/8b4bfbb0be374765a2884b8771dc1232.jpg"
    ]
    
    test_image = None
    for img_path in test_images:
        if os.path.exists(img_path):
            test_image = img_path
            break
    
    if not test_image:
        print("❌ 未找到测试图片")
        return False
    
    print(f"📸 使用测试图片: {test_image}")
    
    # 步骤1: 提交背景移除任务
    print(f"\n🎯 步骤1: 提交背景移除任务...")
    
    url = f"{base_url}/302/submit/removebg"
    headers = {"Authorization": f"Bearer {api_key}"}
    
    try:
        with open(test_image, 'rb') as image_file:
            files = {
                'image': (os.path.basename(test_image), image_file, 'image/jpeg')
            }
            
            print("📤 发送请求...")
            response = requests.post(
                url, 
                headers=headers, 
                files=files,
                timeout=60
            )
            
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('id')
            
            if not task_id:
                print("❌ 未获取到任务ID")
                return False
            
            print(f"📋 任务ID: {task_id}")
            
            # 步骤2: 等待任务完成
            print(f"\n🎯 步骤2: 等待任务完成...")
            final_result = wait_for_task(base_url, headers, task_id)
            
            if final_result and final_result.get('status') == 'succeeded':
                output_url = final_result.get('output')
                if output_url:
                    print(f"✅ 任务成功完成！")
                    print(f"📥 结果URL: {output_url}")
                    
                    # 步骤3: 下载结果
                    download_success = download_result(output_url, "api_test_result.png")
                    return download_success
                else:
                    print("❌ 未找到输出URL")
                    return False
            else:
                print(f"❌ 任务失败: {final_result}")
                return False
                
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"📄 响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def wait_for_task(base_url, headers, task_id, max_wait=300):
    """等待任务完成"""
    query_url = f"{base_url}/302/task/{task_id}"
    
    start_time = time.time()
    check_interval = 5
    
    while time.time() - start_time < max_wait:
        try:
            response = requests.get(query_url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                status = result.get('status', 'unknown')
                
                print(f"📊 任务状态: {status}")
                
                if status == 'succeeded':
                    print("✅ 任务完成！")
                    return result
                elif status == 'failed':
                    print(f"❌ 任务失败: {result.get('error', '未知错误')}")
                    return result
                elif status in ['processing', 'starting', 'running']:
                    pass  # 继续等待
                else:
                    print(f"⚠️ 未知状态: {status}")
                    
            else:
                print(f"⚠️ 查询状态失败: {response.status_code}")
                
        except Exception as e:
            print(f"⚠️ 查询异常: {e}")
        
        time.sleep(check_interval)
    
    print(f"⏰ 等待超时 ({max_wait}秒)")
    return None

def download_result(url, filename):
    """下载结果"""
    try:
        print(f"📥 下载结果: {filename}")
        
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            file_size = len(response.content)
            print(f"✅ 下载成功: {filename} ({file_size/1024:.1f}KB)")
            
            # 测试白底合成
            test_white_background(filename)
            return True
        else:
            print(f"❌ 下载失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 下载异常: {e}")
        return False

def test_white_background(image_path):
    """测试白底合成"""
    try:
        from PIL import Image
        
        print(f"\n🎯 步骤3: 测试白底合成...")
        
        # 打开透明背景图片
        img = Image.open(image_path).convert("RGBA")
        width, height = img.size
        
        print(f"📏 图像尺寸: {width}x{height}")
        
        # 方法1: alpha_composite
        white_bg = Image.new('RGBA', img.size, (255, 255, 255, 255))
        result = Image.alpha_composite(white_bg, img)
        final = result.convert('RGB')
        
        output_path = "api_test_white_background.png"
        final.save(output_path, "PNG")
        
        output_size = os.path.getsize(output_path)
        print(f"✅ 白底合成完成: {output_path} ({output_size/1024:.1f}KB)")
        
        return True
        
    except Exception as e:
        print(f"❌ 白底合成失败: {e}")
        return False

def compare_apis():
    """对比新旧API"""
    print(f"\n📊 API对比总结")
    print("=" * 60)
    
    print("🔄 API替换详情:")
    print("   旧API (Clipdrop):")
    print("     - 端点: /clipdrop/remove-background/v1")
    print("     - 认证: x-api-key")
    print("     - 处理: 同步")
    print("     - 成本: 未知")
    print("")
    print("   新API (302.ai V1):")
    print("     - 端点: /302/submit/removebg")
    print("     - 认证: Authorization: Bearer")
    print("     - 处理: 异步")
    print("     - 成本: 0.01 PTC/次")
    print("")
    print("✅ 优势:")
    print("   1. 解决认证错误问题")
    print("   2. 成本明确可控")
    print("   3. 技术来源透明")
    print("   4. 处理效果良好")

def main():
    """主函数"""
    print("🔧 API替换验证测试")
    print("=" * 60)
    
    # 测试新API
    success = test_302ai_v1_directly()
    
    if success:
        print(f"\n🎉 API替换成功！")
        print(f"✅ 302.ai V1 API工作正常")
        print(f"✅ 背景移除效果良好")
        print(f"✅ 白底合成完美")
    else:
        print(f"\n❌ API测试失败")
        print(f"💡 请检查网络连接和API密钥")
    
    # 显示对比
    compare_apis()
    
    print(f"\n🎯 替换完成！")
    print(f"现在您的系统使用302.ai V1 API进行背景移除")

if __name__ == "__main__":
    main()
