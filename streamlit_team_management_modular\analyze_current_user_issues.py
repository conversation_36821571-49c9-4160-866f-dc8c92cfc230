#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析当前用户的Word生成问题
"""

import os
import json
import zipfile
import xml.etree.ElementTree as ET

def analyze_current_user_data():
    """分析当前用户数据"""
    print("🔍 分析当前用户数据问题")
    print("=" * 60)
    
    # 检查用户数据路径
    user_id = 'user_c61aa17e3868'
    user_data_path = f"data/{user_id}"
    
    print(f"📄 分析用户: {user_id}")
    
    if not os.path.exists(user_data_path):
        print(f"❌ 用户数据路径不存在: {user_data_path}")
        return None
    
    # 1. 检查团队数据
    teams_path = os.path.join(user_data_path, "teams")
    if not os.path.exists(teams_path):
        print(f"❌ 团队数据路径不存在: {teams_path}")
        return None
    
    team_files = [f for f in os.listdir(teams_path) if f.endswith('.json')]
    if not team_files:
        print(f"❌ 未找到团队数据文件")
        return None
    
    print(f"📄 找到团队文件: {team_files}")
    
    # 分析每个团队
    team_analysis = {}
    for team_file in team_files:
        team_name = team_file.replace('.json', '')
        team_path = os.path.join(teams_path, team_file)
        
        with open(team_path, 'r', encoding='utf-8') as f:
            team_data = json.load(f)
        
        print(f"\n📋 团队: {team_name}")
        print(f"   团队数据键: {list(team_data.keys())}")
        
        # 检查球员数据
        players = team_data.get('players', [])
        print(f"   球员数量: {len(players)}")
        
        for i, player in enumerate(players[:3]):  # 只显示前3个
            print(f"     球员{i+1}: {player.get('name', 'NO_NAME')} (号码: {player.get('jersey_number', 'NO_NUMBER')})")
        
        # 检查基本信息
        basic_fields = ['name', 'leader', 'coach', 'doctor', 'contact_person', 'contact_phone']
        print(f"   基本信息:")
        for field in basic_fields:
            value = team_data.get(field, 'MISSING')
            print(f"     {field}: '{value}'")
        
        team_analysis[team_name] = {
            'team_data': team_data,
            'player_count': len(players),
            'has_basic_info': any(team_data.get(field) for field in basic_fields)
        }
    
    # 2. 检查AI数据
    ai_data_path = os.path.join(user_data_path, "enhanced_ai_data")
    if os.path.exists(ai_data_path):
        ai_files = [f for f in os.listdir(ai_data_path) if f.endswith('_ai_data.json')]
        print(f"\n📄 AI数据文件: {ai_files}")
        
        for ai_file in ai_files:
            ai_file_path = os.path.join(ai_data_path, ai_file)
            with open(ai_file_path, 'r', encoding='utf-8') as f:
                ai_data = json.load(f)
            
            team_name = ai_file.replace('_ai_data.json', '')
            print(f"\n📋 {team_name} 的AI数据:")
            
            extracted_info = ai_data.get('extracted_info', {})
            basic_info = extracted_info.get('basic_info', {})
            additional_info = extracted_info.get('additional_info', {})
            
            print(f"   basic_info:")
            for key, value in basic_info.items():
                print(f"     {key}: '{value}'")
            
            print(f"   additional_info:")
            for key, value in additional_info.items():
                print(f"     {key}: '{value}'")
            
            if team_name in team_analysis:
                team_analysis[team_name]['ai_data'] = ai_data
    else:
        print(f"\n⚠️ 未找到AI数据路径: {ai_data_path}")
    
    return team_analysis

def analyze_latest_word_document():
    """分析最新生成的Word文档"""
    print(f"\n🔍 分析最新生成的Word文档")
    print("=" * 60)
    
    user_id = 'user_c61aa17e3868'
    word_output_path = f"data/{user_id}/word_output"
    
    if not os.path.exists(word_output_path):
        print(f"❌ Word输出路径不存在: {word_output_path}")
        return None
    
    # 找到最新的Word文件
    word_files = [f for f in os.listdir(word_output_path) if f.endswith('.docx')]
    if not word_files:
        print(f"❌ 未找到Word文件")
        return None
    
    latest_word_file = max([os.path.join(word_output_path, f) for f in word_files], 
                          key=os.path.getmtime)
    
    print(f"📄 分析文件: {os.path.basename(latest_word_file)}")
    
    try:
        with zipfile.ZipFile(latest_word_file, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    print(f"📄 Word文档内容:")
                    print(f"   {full_text}")
                    
                    # 分析内容
                    analysis = {
                        'has_team_name': '天依' in full_text,
                        'has_leader': any(name in full_text for name in ['领队', '队长'] if name != '领队'),
                        'has_coach': any(name in full_text for name in ['教练'] if name != '教练'),
                        'has_doctor': any(name in full_text for name in ['队医'] if name != '队医'),
                        'has_contact': '赵六' in full_text,
                        'has_phone': '18454432036' in full_text,
                        'player_count': full_text.count('号') - full_text.count('号码'),
                        'has_colors': any(color in full_text for color in ['红', '蓝', '白', '黄', '绿', '黑'])
                    }
                    
                    print(f"\n📊 内容分析:")
                    for key, value in analysis.items():
                        status = "✅" if value else "❌"
                        print(f"   {key}: {status} {value}")
                    
                    return analysis
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return None
                
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def test_word_generation_with_current_user():
    """使用当前用户测试Word生成"""
    print(f"\n🔍 使用当前用户测试Word生成")
    print("=" * 60)
    
    try:
        import streamlit as st
        
        # 设置session state
        if 'user_id' not in st.session_state:
            st.session_state.user_id = 'user_c61aa17e3868'
        
        from services.fashion_workflow_service import FashionWorkflowService
        
        # 创建工作流服务
        workflow_service = FashionWorkflowService('user_c61aa17e3868')
        
        # 获取用户的团队列表
        user_data_path = "data/user_c61aa17e3868/teams"
        if not os.path.exists(user_data_path):
            print(f"❌ 用户团队数据不存在")
            return None
        
        team_files = [f for f in os.listdir(user_data_path) if f.endswith('.json')]
        if not team_files:
            print(f"❌ 未找到团队文件")
            return None
        
        # 使用第一个团队
        team_name = team_files[0].replace('.json', '')
        print(f"📄 测试团队: {team_name}")
        
        # 测试Word生成
        word_result = workflow_service._auto_generate_word_document(
            team_name, {}, None
        )
        
        print(f"   Word生成结果: {word_result}")
        
        if word_result.get('success'):
            print("✅ Word生成成功")
            return word_result
        else:
            print(f"❌ Word生成失败: {word_result.get('error', 'Unknown error')}")
            return None
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def diagnose_data_quality_issues(team_analysis):
    """诊断数据质量问题"""
    print(f"\n🔍 诊断数据质量问题")
    print("=" * 60)
    
    issues = []
    
    for team_name, analysis in team_analysis.items():
        print(f"\n📋 团队: {team_name}")
        
        team_data = analysis['team_data']
        player_count = analysis['player_count']
        
        # 检查球员数量
        if player_count < 5:
            issue = f"球员数量不足: 只有{player_count}个球员，建议至少5个"
            print(f"   ⚠️ {issue}")
            issues.append(issue)
        elif player_count == 1:
            issue = f"球员数量严重不足: 只有1个球员"
            print(f"   ❌ {issue}")
            issues.append(issue)
        else:
            print(f"   ✅ 球员数量正常: {player_count}个")
        
        # 检查基本信息
        basic_fields = ['leader', 'coach', 'doctor']
        missing_fields = []
        
        for field in basic_fields:
            value = team_data.get(field, '')
            if not value or value in ['', '自动填充', '待定', '未知']:
                missing_fields.append(field)
        
        if missing_fields:
            issue = f"缺少人员信息: {', '.join(missing_fields)}"
            print(f"   ❌ {issue}")
            issues.append(issue)
        else:
            print(f"   ✅ 人员信息完整")
        
        # 检查AI数据
        if 'ai_data' in analysis:
            ai_data = analysis['ai_data']
            extracted_info = ai_data.get('extracted_info', {})
            basic_info = extracted_info.get('basic_info', {})
            
            ai_issues = []
            for field, value in basic_info.items():
                if value in ['自动填充', '待定', '未知', '']:
                    ai_issues.append(field)
            
            if ai_issues:
                issue = f"AI数据质量问题: {', '.join(ai_issues)} 包含无效值"
                print(f"   ⚠️ {issue}")
                issues.append(issue)
            else:
                print(f"   ✅ AI数据质量良好")
        else:
            issue = f"缺少AI数据"
            print(f"   ❌ {issue}")
            issues.append(issue)
    
    return issues

def generate_fix_recommendations(issues):
    """生成修复建议"""
    print(f"\n💡 修复建议")
    print("=" * 60)
    
    if not issues:
        print("🎉 未发现数据质量问题！")
        return
    
    print(f"发现 {len(issues)} 个问题:")
    for i, issue in enumerate(issues, 1):
        print(f"   {i}. {issue}")
    
    print(f"\n🔧 建议的修复方案:")
    
    # 球员数量问题
    if any('球员数量' in issue for issue in issues):
        print(f"   1. 球员数量问题:")
        print(f"      - 在团队管理中添加更多球员")
        print(f"      - 确保每个球员都有姓名和号码")
        print(f"      - 建议至少添加5个球员")
    
    # 人员信息问题
    if any('人员信息' in issue for issue in issues):
        print(f"   2. 人员信息问题:")
        print(f"      - 在团队设置中补充领队、教练、队医信息")
        print(f"      - 避免使用'自动填充'等占位符")
        print(f"      - 输入真实的人员姓名")
    
    # AI数据问题
    if any('AI数据' in issue for issue in issues):
        print(f"   3. AI数据质量问题:")
        print(f"      - 重新运行AI数据提取")
        print(f"      - 检查原始输入数据的质量")
        print(f"      - 手动补充缺失的信息")

def main():
    """主函数"""
    print("🎯 分析当前用户的Word生成问题")
    print("=" * 70)
    print("基于用户提供的Word文档截图进行深入分析")
    print("=" * 70)
    
    # 1. 分析用户数据
    team_analysis = analyze_current_user_data()
    
    if not team_analysis:
        print("❌ 无法获取用户数据")
        return
    
    # 2. 分析最新Word文档
    word_analysis = analyze_latest_word_document()
    
    # 3. 测试Word生成
    generation_result = test_word_generation_with_current_user()
    
    # 4. 诊断数据质量问题
    issues = diagnose_data_quality_issues(team_analysis)
    
    # 5. 生成修复建议
    generate_fix_recommendations(issues)
    
    # 综合分析
    print(f"\n📊 综合分析结果")
    print("=" * 70)
    
    print(f"🔍 问题根因分析:")
    print(f"   从Word文档截图可以看出:")
    print(f"   ✅ 团队名称正常显示 (天依 369)")
    print(f"   ✅ 联系人信息正常显示 (赵六, 18454432036)")
    print(f"   ❌ 领队、教练、队医信息缺失")
    print(f"   ❌ 只有1个球员信息，其他位置空白")
    print(f"   ❌ 颜色信息全部缺失")
    
    print(f"\n🎯 主要问题:")
    if issues:
        print(f"   发现 {len(issues)} 个数据质量问题")
        print(f"   这些问题导致Word文档信息不完整")
    else:
        print(f"   数据质量良好，问题可能在其他地方")
    
    print(f"\n💡 下一步行动:")
    print(f"   1. 根据修复建议补充缺失的数据")
    print(f"   2. 确保团队中有足够的球员信息")
    print(f"   3. 补充领队、教练、队医的真实姓名")
    print(f"   4. 重新生成Word文档验证修复效果")

if __name__ == "__main__":
    main()
