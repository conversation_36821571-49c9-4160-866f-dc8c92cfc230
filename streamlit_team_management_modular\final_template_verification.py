#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终模板验证 - 确保所有配置都使用正确的模板
"""

import os
import sys
import json
import zipfile
import re
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_all_template_references():
    """验证所有模板引用"""
    print("=" * 60)
    print("🔍 验证所有模板引用")
    print("=" * 60)
    
    # 检查的文件和期望的模板路径
    files_to_check = [
        {
            "file": "config/settings.py",
            "pattern": r'TEMPLATE_PATH.*?=.*?"([^"]+)"',
            "expected": "template_15players_fixed.docx"
        },
        {
            "file": "config/settings_test_15players.py", 
            "pattern": r'TEMPLATE_PATH.*?=.*?"([^"]+)"',
            "expected": "template_15players_fixed.docx"
        },
        {
            "file": "../word_zc/ai-football-generator/test_15players_template.json",
            "pattern": r'"templatePath":\s*"([^"]+)"',
            "expected": "template_15players_fixed.docx"
        }
    ]
    
    all_correct = True
    
    for file_info in files_to_check:
        file_path = file_info["file"]
        pattern = file_info["pattern"]
        expected = file_info["expected"]
        
        print(f"\n📄 检查文件: {file_path}")
        
        if not os.path.exists(file_path):
            print(f"   ❌ 文件不存在")
            all_correct = False
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            matches = re.findall(pattern, content)
            
            if matches:
                for match in matches:
                    if expected in match:
                        print(f"   ✅ 正确: {match}")
                    else:
                        print(f"   ❌ 错误: {match} (期望包含: {expected})")
                        all_correct = False
            else:
                print(f"   ❌ 未找到模板路径配置")
                all_correct = False
                
        except Exception as e:
            print(f"   ❌ 读取文件失败: {e}")
            all_correct = False
    
    return all_correct

def verify_template_files():
    """验证模板文件状态"""
    print(f"\n" + "=" * 60)
    print("🔍 验证模板文件状态")
    print("=" * 60)
    
    template_dir = "../word_zc"
    templates = [
        "template_15players_fixed.docx",
        "template_15players_fixed.docx"
    ]
    
    for template_name in templates:
        template_path = os.path.join(template_dir, template_name)
        print(f"\n📄 检查模板: {template_name}")
        
        if not os.path.exists(template_path):
            print(f"   ❌ 文件不存在")
            continue
        
        try:
            # 检查占位符
            with zipfile.ZipFile(template_path, 'r') as zip_file:
                with zip_file.open('word/document.xml') as xml_file:
                    content = xml_file.read().decode('utf-8')
            
            # 查找占位符
            placeholder_pattern = r'\{([^}]+)\}'
            placeholders = re.findall(placeholder_pattern, content)
            
            # 检查损坏的占位符
            broken_placeholders = []
            clean_placeholders = []
            
            for placeholder in placeholders:
                if '<' in placeholder or '>' in placeholder or 'w:' in placeholder:
                    broken_placeholders.append(placeholder)
                else:
                    clean_placeholders.append(placeholder)
            
            print(f"   📊 总占位符: {len(placeholders)}")
            print(f"   ✅ 正常占位符: {len(clean_placeholders)}")
            print(f"   ❌ 损坏占位符: {len(broken_placeholders)}")
            
            if broken_placeholders:
                print(f"   损坏的占位符示例:")
                for placeholder in broken_placeholders[:3]:
                    print(f"      {placeholder[:50]}...")
            
        except Exception as e:
            print(f"   ❌ 检查失败: {e}")

def test_final_word_generation():
    """最终Word生成测试"""
    print(f"\n" + "=" * 60)
    print("🧪 最终Word生成测试")
    print("=" * 60)
    
    try:
        # 准备测试数据
        team_data = {
            "name": "最终验证测试队",
            "contact_person": "测试联系人",
            "contact_phone": "13800000000",
            "leader": "测试领队",
            "coach": "测试教练",
            "doctor": "测试队医",
            "jersey_color": "红色",
            "shorts_color": "黑色",
            "socks_color": "红色",
            "goalkeeper_kit_color": "绿色"
        }
        
        players_data = [
            {"name": "测试球员1", "jersey_number": "1", "photo": ""},
            {"name": "测试球员2", "jersey_number": "2", "photo": ""}
        ]
        
        print(f"📄 测试数据准备完成")
        
        # 使用当前配置生成Word
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        paths = app_settings.word_generator.get_absolute_paths("final_verification", app_settings.paths)
        
        print(f"📄 使用的配置:")
        print(f"   模板路径: {paths['template_path']}")
        print(f"   JAR路径: {paths['jar_path']}")
        print(f"   输出目录: {paths['output_dir']}")
        
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        # 生成Word文档
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            output_file = result['file_path']
            print(f"\n✅ Word生成成功: {os.path.basename(output_file)}")
            
            # 验证生成的文档
            return verify_generated_document(output_file, team_data)
        else:
            print(f"\n❌ Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_generated_document(docx_path, team_data):
    """验证生成的文档"""
    print(f"\n📄 验证生成的文档:")
    
    try:
        with zipfile.ZipFile(docx_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        # 检查占位符是否都被替换
        placeholder_pattern = r'\{([^}]+)\}'
        remaining_placeholders = re.findall(placeholder_pattern, content)
        
        if remaining_placeholders:
            print(f"   ❌ 仍有 {len(remaining_placeholders)} 个未替换的占位符")
            return False
        else:
            print(f"   ✅ 所有占位符都已替换")
        
        # 检查关键数据是否出现
        key_data = [
            team_data["name"],
            team_data["contact_person"],
            team_data["contact_phone"],
            team_data["leader"],
            team_data["coach"],
            team_data["jersey_color"]
        ]
        
        missing_data = []
        for data in key_data:
            if data not in content:
                missing_data.append(data)
        
        if missing_data:
            print(f"   ❌ 缺少数据: {missing_data}")
            return False
        else:
            print(f"   ✅ 所有关键数据都已出现")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 验证失败: {e}")
        return False

def test_tianyi909_final():
    """最终测试天依909数据"""
    print(f"\n" + "=" * 60)
    print("🧪 最终测试天依909数据")
    print("=" * 60)
    
    try:
        # 读取天依909的数据
        test_file = "data/user_f33368cb41dd/enhanced_ai_data/天依909_ai_data.json"
        
        if not os.path.exists(test_file):
            print(f"❌ 天依909数据文件不存在: {test_file}")
            return False
        
        with open(test_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        extracted_info = data.get("extracted_info", {})
        basic_info = extracted_info.get("basic_info", {})
        kit_colors = extracted_info.get("kit_colors", {})
        additional_info = extracted_info.get("additional_info", {})
        
        # 准备Word生成数据
        team_data = {
            "name": basic_info.get("team_name", "天依909"),
            "contact_person": basic_info.get("contact_person"),
            "contact_phone": basic_info.get("contact_phone"),
            "leader": basic_info.get("leader_name"),
            "coach": additional_info.get("coach_name", basic_info.get("leader_name")),
            "doctor": basic_info.get("team_doctor"),
            "jersey_color": kit_colors.get("jersey_color"),
            "shorts_color": kit_colors.get("shorts_color"),
            "socks_color": kit_colors.get("socks_color"),
            "goalkeeper_kit_color": kit_colors.get("goalkeeper_kit_color")
        }
        
        players_data = [
            {"name": "张三", "jersey_number": "1", "photo": ""}
        ]
        
        print(f"📄 天依909数据:")
        for key, value in team_data.items():
            print(f"   {key}: '{value}'")
        
        # 生成Word文档
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        paths = app_settings.word_generator.get_absolute_paths("tianyi909_final", app_settings.paths)
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            output_file = result['file_path']
            print(f"\n✅ 天依909 Word生成成功: {os.path.basename(output_file)}")
            
            # 验证关键信息
            with zipfile.ZipFile(output_file, 'r') as zip_file:
                with zip_file.open('word/document.xml') as xml_file:
                    content = xml_file.read().decode('utf-8')
            
            keywords = ["天依909", "赵六", "粉色", "18454432036"]
            found_count = 0
            
            for keyword in keywords:
                if keyword in content:
                    found_count += 1
                    print(f"   ✅ 找到: '{keyword}'")
                else:
                    print(f"   ❌ 未找到: '{keyword}'")
            
            return found_count == len(keywords)
        else:
            print(f"\n❌ 天依909 Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 天依909测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 最终模板验证")
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 验证所有模板引用
        references_ok = verify_all_template_references()
        
        # 2. 验证模板文件状态
        verify_template_files()
        
        # 3. 最终Word生成测试
        word_generation_ok = test_final_word_generation()
        
        # 4. 天依909最终测试
        tianyi909_ok = test_tianyi909_final()
        
        print("\n" + "=" * 60)
        print("📋 最终验证总结")
        print("=" * 60)
        
        print(f"📊 验证结果:")
        print(f"   模板引用配置: {'✅ 正确' if references_ok else '❌ 错误'}")
        print(f"   Word生成测试: {'✅ 成功' if word_generation_ok else '❌ 失败'}")
        print(f"   天依909测试: {'✅ 成功' if tianyi909_ok else '❌ 失败'}")
        
        if references_ok and word_generation_ok and tianyi909_ok:
            print(f"\n🎉 所有验证都通过！")
            print(f"   ✅ 模板配置正确")
            print(f"   ✅ Word生成正常")
            print(f"   ✅ 天依909数据处理正常")
            print(f"\n💡 用户现在应该能看到正确填充的Word文档了！")
        else:
            print(f"\n❌ 仍有问题需要解决")
            if not references_ok:
                print(f"   - 检查并修复模板引用配置")
            if not word_generation_ok:
                print(f"   - 检查Word生成流程")
            if not tianyi909_ok:
                print(f"   - 检查天依909数据处理")
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
