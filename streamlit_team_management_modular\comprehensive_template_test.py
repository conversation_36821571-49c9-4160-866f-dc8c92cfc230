#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面测试真正使用的模板和数据流
"""

import os
import sys
import json
import zipfile
import re
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_actual_template():
    """分析用户实际使用的模板"""
    print("=" * 60)
    print("🔍 分析用户实际使用的模板")
    print("=" * 60)
    
    template_path = "../word_zc/template_15players_fixed.docx"
    
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        return None
    
    try:
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        # 查找所有占位符
        placeholder_pattern = r'\{([^}]+)\}'
        placeholders = re.findall(placeholder_pattern, content)
        
        print(f"📄 实际模板文件: {os.path.basename(template_path)}")
        print(f"📄 找到 {len(placeholders)} 个占位符:")
        
        unique_placeholders = list(set(placeholders))
        unique_placeholders.sort()
        
        for placeholder in unique_placeholders:
            print(f"   {placeholder}")
        
        # 检查是否有损坏的占位符（跨XML标签的）
        broken_placeholders = []
        for placeholder in unique_placeholders:
            if '<' in placeholder or '>' in placeholder or 'w:' in placeholder:
                broken_placeholders.append(placeholder)
        
        if broken_placeholders:
            print(f"\n❌ 发现损坏的占位符（跨XML标签）:")
            for placeholder in broken_placeholders:
                print(f"   {placeholder}")
        
        return unique_placeholders, broken_placeholders
        
    except Exception as e:
        print(f"❌ 分析模板失败: {e}")
        return None, None

def check_settings_template_path():
    """检查设置中的模板路径"""
    print(f"\n" + "=" * 60)
    print("🔍 检查设置中的模板路径")
    print("=" * 60)
    
    try:
        from config.settings import app_settings
        
        # 获取模板路径配置
        paths = app_settings.word_generator.get_absolute_paths("test", app_settings.paths)
        
        print(f"📄 配置的模板路径:")
        print(f"   template_path: {paths['template_path']}")
        print(f"   jar_path: {paths['jar_path']}")
        print(f"   output_dir: {paths['output_dir']}")
        
        # 检查模板文件是否存在
        if os.path.exists(paths['template_path']):
            print(f"   ✅ 模板文件存在")
            return paths['template_path']
        else:
            print(f"   ❌ 模板文件不存在")
            return None
            
    except Exception as e:
        print(f"❌ 检查设置失败: {e}")
        return None

def test_with_actual_template():
    """使用实际模板测试"""
    print(f"\n" + "=" * 60)
    print("🧪 使用实际模板测试")
    print("=" * 60)
    
    try:
        # 准备测试数据
        team_data = {
            "name": "实际模板测试队",
            "contact_person": "测试联系人",
            "contact_phone": "13800000000",
            "leader": "测试领队",
            "coach": "测试教练",
            "doctor": "测试队医",
            "jersey_color": "红色",
            "shorts_color": "黑色",
            "socks_color": "红色",
            "goalkeeper_kit_color": "绿色"
        }
        
        players_data = [
            {"name": "测试球员1", "jersey_number": "1", "photo": ""},
            {"name": "测试球员2", "jersey_number": "2", "photo": ""}
        ]
        
        print(f"📄 测试数据:")
        for key, value in team_data.items():
            print(f"   {key}: '{value}'")
        
        # 使用WordGeneratorService生成
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        paths = app_settings.word_generator.get_absolute_paths("actual_template_test", app_settings.paths)
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        print(f"\n📄 使用的模板: {os.path.basename(paths['template_path'])}")
        
        # 检查准备的JSON数据
        json_data = word_service._prepare_json_data(team_data, players_data)
        
        print(f"\n📄 准备的JSON数据:")
        team_info = json_data.get("teamInfo", {})
        for key, value in team_info.items():
            print(f"   {key}: '{value}'")
        
        # 生成Word文档
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            output_file = result['file_path']
            print(f"\n✅ Word生成成功: {os.path.basename(output_file)}")
            
            # 分析生成的文档
            return analyze_generated_document_detailed(output_file, team_info)
        else:
            print(f"\n❌ Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_generated_document_detailed(docx_path, expected_data):
    """详细分析生成的Word文档"""
    print(f"\n📄 详细分析生成的Word文档:")
    
    try:
        with zipfile.ZipFile(docx_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        # 查找未替换的占位符
        placeholder_pattern = r'\{([^}]+)\}'
        remaining_placeholders = re.findall(placeholder_pattern, content)
        
        print(f"📊 占位符替换情况:")
        if remaining_placeholders:
            print(f"❌ 发现 {len(remaining_placeholders)} 个未替换的占位符:")
            unique_remaining = list(set(remaining_placeholders))
            for placeholder in unique_remaining:
                print(f"   {placeholder}")
        else:
            print(f"✅ 所有占位符都已替换")
        
        # 检查期望的数据是否出现在文档中
        print(f"\n📊 期望数据检查:")
        missing_data = []
        for key, value in expected_data.items():
            if value and str(value) in content:
                print(f"   ✅ {key}: '{value}' 已出现")
            else:
                print(f"   ❌ {key}: '{value}' 未出现")
                missing_data.append((key, value))
        
        # 提取所有文本内容
        text_pattern = r'<w:t[^>]*>([^<]*)</w:t>'
        texts = re.findall(text_pattern, content)
        
        # 过滤有意义的文本
        meaningful_texts = []
        for text in texts:
            clean_text = text.strip()
            if clean_text and len(clean_text) > 0 and not clean_text.startswith('<'):
                meaningful_texts.append(clean_text)
        
        print(f"\n📊 文档中的所有文本内容:")
        for i, text in enumerate(meaningful_texts):
            print(f"   {i+1}. '{text}'")
        
        # 检查是否有空的表格单元格模式
        print(f"\n📊 表格单元格分析:")
        
        # 查找表格单元格
        cell_pattern = r'<w:tc[^>]*>(.*?)</w:tc>'
        cells = re.findall(cell_pattern, content, re.DOTALL)
        
        empty_cells = 0
        filled_cells = 0
        
        for cell in cells:
            cell_texts = re.findall(text_pattern, cell)
            cell_content = ''.join(cell_texts).strip()
            
            if not cell_content:
                empty_cells += 1
            else:
                filled_cells += 1
        
        print(f"   总单元格数: {len(cells)}")
        print(f"   空单元格数: {empty_cells}")
        print(f"   有内容单元格数: {filled_cells}")
        
        # 检查特定的问题模式
        print(f"\n📊 问题模式检查:")
        
        # 检查是否有连续的空单元格
        consecutive_empty = 0
        max_consecutive_empty = 0
        
        for cell in cells:
            cell_texts = re.findall(text_pattern, cell)
            cell_content = ''.join(cell_texts).strip()
            
            if not cell_content:
                consecutive_empty += 1
                max_consecutive_empty = max(max_consecutive_empty, consecutive_empty)
            else:
                consecutive_empty = 0
        
        if max_consecutive_empty > 5:
            print(f"   ⚠️ 发现连续 {max_consecutive_empty} 个空单元格")
        else:
            print(f"   ✅ 没有异常的连续空单元格")
        
        return len(remaining_placeholders) == 0 and len(missing_data) == 0
        
    except Exception as e:
        print(f"❌ 分析文档失败: {e}")
        return False

def test_tianyi909_with_actual_template():
    """使用实际模板测试天依909数据"""
    print(f"\n" + "=" * 60)
    print("🧪 使用实际模板测试天依909数据")
    print("=" * 60)
    
    try:
        # 读取天依909的数据
        test_file = "data/user_f33368cb41dd/enhanced_ai_data/天依909_ai_data.json"
        
        with open(test_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        extracted_info = data.get("extracted_info", {})
        basic_info = extracted_info.get("basic_info", {})
        kit_colors = extracted_info.get("kit_colors", {})
        additional_info = extracted_info.get("additional_info", {})
        
        # 准备Word生成数据
        team_data = {
            "name": basic_info.get("team_name", "天依909"),
            "contact_person": basic_info.get("contact_person"),
            "contact_phone": basic_info.get("contact_phone"),
            "leader": basic_info.get("leader_name"),
            "coach": additional_info.get("coach_name", basic_info.get("leader_name")),
            "doctor": basic_info.get("team_doctor"),
            "jersey_color": kit_colors.get("jersey_color"),
            "shorts_color": kit_colors.get("shorts_color"),
            "socks_color": kit_colors.get("socks_color"),
            "goalkeeper_kit_color": kit_colors.get("goalkeeper_kit_color")
        }
        
        players_data = [
            {"name": "张三", "jersey_number": "1", "photo": ""}
        ]
        
        print(f"📄 天依909数据:")
        for key, value in team_data.items():
            print(f"   {key}: '{value}'")
        
        # 使用WordGeneratorService生成
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        paths = app_settings.word_generator.get_absolute_paths("tianyi909_actual_template", app_settings.paths)
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        print(f"\n📄 使用的模板: {os.path.basename(paths['template_path'])}")
        
        # 生成Word文档
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            output_file = result['file_path']
            print(f"\n✅ Word生成成功: {os.path.basename(output_file)}")
            
            # 检查生成的文档
            return check_tianyi909_document(output_file)
        else:
            print(f"\n❌ Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_tianyi909_document(docx_path):
    """检查天依909生成的文档"""
    print(f"\n📄 检查天依909生成的文档:")
    
    try:
        with zipfile.ZipFile(docx_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        # 检查关键信息
        keywords = ["天依909", "赵六", "粉色", "黑色", "绿色", "18454432036"]
        found_keywords = []
        
        for keyword in keywords:
            if keyword in content:
                found_keywords.append(keyword)
                print(f"   ✅ 找到: '{keyword}'")
            else:
                print(f"   ❌ 未找到: '{keyword}'")
        
        # 检查占位符
        placeholder_pattern = r'\{([^}]+)\}'
        remaining_placeholders = re.findall(placeholder_pattern, content)
        
        if remaining_placeholders:
            print(f"\n❌ 仍有未替换的占位符:")
            unique_remaining = list(set(remaining_placeholders))
            for placeholder in unique_remaining[:10]:  # 只显示前10个
                print(f"   {placeholder}")
        else:
            print(f"\n✅ 所有占位符都已替换")
        
        return len(found_keywords) >= 4 and len(remaining_placeholders) == 0
        
    except Exception as e:
        print(f"❌ 检查文档失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 全面测试真正使用的模板和数据流")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 分析实际使用的模板
        placeholders, broken_placeholders = analyze_actual_template()
        
        # 2. 检查设置中的模板路径
        template_path = check_settings_template_path()
        
        # 3. 使用实际模板测试
        actual_template_ok = test_with_actual_template()
        
        # 4. 使用实际模板测试天依909数据
        tianyi909_ok = test_tianyi909_with_actual_template()
        
        print("\n" + "=" * 60)
        print("📋 全面测试总结")
        print("=" * 60)
        
        print(f"📊 测试结果:")
        print(f"   模板占位符数量: {len(placeholders) if placeholders else 0}")
        print(f"   损坏的占位符: {len(broken_placeholders) if broken_placeholders else 0}")
        print(f"   实际模板测试: {'✅ 成功' if actual_template_ok else '❌ 失败'}")
        print(f"   天依909测试: {'✅ 成功' if tianyi909_ok else '❌ 失败'}")
        
        if broken_placeholders:
            print(f"\n🎯 发现的问题:")
            print(f"   模板中有损坏的占位符（跨XML标签）")
            print(f"   这会导致占位符无法被正确识别和替换")
            
            print(f"\n💡 解决方案:")
            print(f"   1. 修复模板文件中的损坏占位符")
            print(f"   2. 确保占位符完整且不跨XML标签")
            print(f"   3. 使用正确的模板文件")
            
        elif not actual_template_ok or not tianyi909_ok:
            print(f"\n🎯 其他问题:")
            print(f"   模板占位符正常，但生成过程有问题")
            print(f"   需要检查Java程序和数据传递")
            
        else:
            print(f"\n✅ 所有测试都成功!")
            print(f"   模板和数据流都正常工作")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
