#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Word模板中的队徽占位符
Check Logo Placeholder in Word Template

专门检查Word模板是否包含队徽相关的占位符
"""

import os
import zipfile
import re
from datetime import datetime

def check_template_logo_placeholder():
    """检查模板中的队徽占位符"""
    
    print("🔍 检查Word模板中的队徽占位符")
    print("=" * 80)
    print(f"⏰ 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 查找模板文件
    template_locations = [
        'word_zc/template_15players.docx',
        'word_zc/template_15players_fixed.docx',
        'word_zc/ai-football-generator/template.docx',
        'word_zc/ai-football-generator/realistic_template.docx'
    ]
    
    for template_path in template_locations:
        if os.path.exists(template_path):
            print(f"📄 检查模板: {os.path.basename(template_path)}")
            print("-" * 60)
            
            try:
                with zipfile.ZipFile(template_path, 'r') as zip_file:
                    with zip_file.open('word/document.xml') as xml_file:
                        content = xml_file.read().decode('utf-8')
                
                # 检查队徽相关占位符
                logo_placeholders = [
                    '{{logo}}',
                    '{{logoPath}}',
                    '{{teamLogo}}',
                    '{{logo_path}}',
                    '{{LOGO}}',
                    '{{Logo}}'
                ]
                
                print(f"🔍 队徽占位符检查:")
                logo_found = False
                
                for placeholder in logo_placeholders:
                    if placeholder in content:
                        print(f"   ✅ 找到: {placeholder}")
                        logo_found = True
                    else:
                        # 检查是否字段名存在但被分割
                        field_name = placeholder.replace("{{", "").replace("}}", "")
                        if field_name in content:
                            print(f"   ⚠️ 字段名存在但可能被XML分割: {field_name}")
                            logo_found = True
                
                if not logo_found:
                    print(f"   ❌ 未找到任何队徽占位符")
                
                # 检查所有占位符
                placeholder_pattern = r'\{\{[^}]+\}\}'
                all_placeholders = re.findall(placeholder_pattern, content)
                
                print(f"\n📋 所有占位符 ({len(all_placeholders)} 个):")
                for placeholder in sorted(set(all_placeholders)):
                    print(f"   - {placeholder}")
                
                # 检查是否有图片相关的XML标签
                print(f"\n🖼️ 图片相关标签检查:")
                image_tags = [
                    'w:drawing',
                    'pic:pic',
                    'a:blip',
                    'wp:inline',
                    'wp:anchor'
                ]
                
                for tag in image_tags:
                    if tag in content:
                        print(f"   ✅ 找到图片标签: {tag}")
                    else:
                        print(f"   ❌ 未找到图片标签: {tag}")
                
                print()
                
            except Exception as e:
                print(f"   ❌ 读取模板失败: {e}")
                print()
        else:
            print(f"❌ 模板文件不存在: {template_path}")

def add_logo_placeholder_to_template():
    """向模板添加队徽占位符"""
    
    print("🔧 向模板添加队徽占位符")
    print("=" * 80)
    
    # 选择主要模板
    template_path = 'word_zc/template_15players_fixed.docx'
    backup_path = 'word_zc/template_15players_fixed_backup_before_logo.docx'
    
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        return False
    
    try:
        # 备份原模板
        import shutil
        shutil.copy2(template_path, backup_path)
        print(f"✅ 备份原模板: {os.path.basename(backup_path)}")
        
        # 读取模板内容
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            file_list = zip_file.namelist()
            files_content = {}
            
            for file_name in file_list:
                files_content[file_name] = zip_file.read(file_name)
        
        # 修改document.xml
        document_xml = files_content['word/document.xml'].decode('utf-8')
        
        # 在标题附近添加队徽占位符
        # 查找标题位置
        title_pattern = r'(<w:t[^>]*>.*?足球.*?比赛.*?报名表.*?</w:t>)'
        title_match = re.search(title_pattern, document_xml)
        
        if title_match:
            print("✅ 找到标题位置")
            
            # 在标题前添加队徽占位符
            logo_placeholder_xml = '''
            <w:p>
                <w:pPr>
                    <w:jc w:val="center"/>
                </w:pPr>
                <w:r>
                    <w:t>{{logoPath}}</w:t>
                </w:r>
            </w:p>
            '''
            
            # 插入队徽占位符
            title_start = title_match.start()
            
            # 找到标题段落的开始
            p_start = document_xml.rfind('<w:p', 0, title_start)
            if p_start != -1:
                new_content = (
                    document_xml[:p_start] + 
                    logo_placeholder_xml + 
                    document_xml[p_start:]
                )
                
                files_content['word/document.xml'] = new_content.encode('utf-8')
                print("✅ 添加队徽占位符成功")
            else:
                print("❌ 无法找到标题段落位置")
                return False
        else:
            print("❌ 无法找到标题位置")
            return False
        
        # 重新打包ZIP文件
        with zipfile.ZipFile(template_path, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            for file_name, content in files_content.items():
                zip_file.writestr(file_name, content)
        
        print(f"✅ 模板修改完成: {os.path.basename(template_path)}")
        
        # 验证修改
        print(f"\n🔍 验证修改结果:")
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                if '{{logoPath}}' in content:
                    print("   ✅ 队徽占位符已成功添加")
                    return True
                else:
                    print("   ❌ 队徽占位符添加失败")
                    return False
        
    except Exception as e:
        print(f"❌ 修改模板失败: {e}")
        
        # 恢复备份
        if os.path.exists(backup_path):
            try:
                shutil.copy2(backup_path, template_path)
                print(f"✅ 已恢复原模板")
            except:
                pass
        
        return False

def test_logo_placeholder_processing():
    """测试队徽占位符处理"""
    
    print("🧪 测试队徽占位符处理")
    print("=" * 80)
    
    # 创建测试JSON数据
    import json
    import tempfile
    import subprocess
    
    test_data = {
        "teamInfo": {
            "title": "队徽占位符测试队报名表",
            "organizationName": "队徽占位符测试队",
            "teamLeader": "测试领队",
            "coach": "测试教练",
            "teamDoctor": "测试队医",
            "contactPerson": "测试联系人",
            "contactPhone": "13800138000",
            "logoPath": "assets/logos/test_logo.png",  # 测试队徽路径
            "jerseyColor": "红色",
            "shortsColor": "蓝色",
            "socksColor": "白色",
            "goalkeeperKitColor": "绿色"
        },
        "players": [
            {
                "name": "测试球员1",
                "jerseyNumber": "1",
                "jerseyColor": "红色",
                "shortsColor": "蓝色",
                "socksColor": "白色",
                "photoPath": "test_photo_1.jpg"
            }
        ]
    }
    
    # 写入临时JSON文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
        test_json_file = f.name
    
    print(f"✅ 创建测试JSON文件: {test_json_file}")
    print(f"   logoPath: {test_data['teamInfo']['logoPath']}")
    
    # 调用Java程序
    jar_path = "word_zc/ai-football-generator/target/word-generator.jar"
    cmd = ['java', '-cp', jar_path, 'CommandLineMain', test_json_file]
    
    print(f"\n🚀 执行Java程序:")
    print(f"   命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=60,
            encoding='utf-8',
            errors='ignore'
        )
        
        print(f"\n📊 执行结果:")
        print(f"   返回码: {result.returncode}")
        
        output_text = result.stdout + result.stderr
        
        # 检查队徽处理
        if 'logoPath' in output_text:
            print("   ✅ Java程序处理了logoPath字段")
        else:
            print("   ❌ Java程序未处理logoPath字段")
        
        if '队徽路径=' in output_text:
            print("   ✅ 队徽路径出现在调试输出中")
            for line in output_text.split('\n'):
                if '队徽路径=' in line:
                    print(f"      {line.strip()}")
        else:
            print("   ❌ 队徽路径未出现在调试输出中")
        
        # 检查Word生成结果
        if 'SUCCESS:' in output_text:
            print("   ✅ Word生成成功")
        elif 'ERROR:' in output_text:
            print("   ❌ Word生成失败")
            for line in output_text.split('\n'):
                if 'ERROR:' in line:
                    print(f"      {line.strip()}")
        
        # 清理临时文件
        try:
            os.unlink(test_json_file)
        except:
            pass
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    # 步骤1: 检查现有模板
    check_template_logo_placeholder()
    
    print("\n" + "=" * 80)
    
    # 步骤2: 添加队徽占位符
    if add_logo_placeholder_to_template():
        print("\n" + "=" * 80)
        
        # 步骤3: 重新检查模板
        print("🔍 重新检查修改后的模板:")
        check_template_logo_placeholder()
        
        print("\n" + "=" * 80)
        
        # 步骤4: 测试队徽处理
        test_logo_placeholder_processing()
    
    print(f"\n⏰ 检查完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
