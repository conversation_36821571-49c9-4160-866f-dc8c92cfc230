#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI信息收集与显示断层问题
Test AI Information Collection and Display Disconnect Issue
"""

import os

def analyze_ai_info_flow():
    """分析AI信息流程"""
    print("🔍 分析AI信息收集与显示流程")
    print("=" * 80)
    
    flow_analysis = {
        "用户观察到的问题": {
            "AI聊天记录": [
                "联系人: 赵六",
                "联系电话: 18544432036", 
                "球衣主色调: 粉色",
                "AI已经收集了完整信息"
            ],
            "显示区域": [
                "球队名称: 未设置",
                "联系人: 未设置",
                "联系电话: 未设置",
                "队长: 未设置"
            ],
            "问题": "信息收集成功但显示失败"
        },
        
        "可能的原因": [
            "信息提取后未正确保存到session_state",
            "显示函数从错误的数据源读取",
            "数据格式转换问题",
            "session_state键名不匹配",
            "信息提取触发机制问题"
        ],
        
        "需要检查的环节": [
            "AI信息提取函数",
            "session_state保存逻辑",
            "显示函数数据读取",
            "数据格式和键名映射",
            "提取按钮触发流程"
        ]
    }
    
    for category, details in flow_analysis.items():
        print(f"\n🎯 {category}")
        if isinstance(details, dict):
            for key, value in details.items():
                print(f"   {key}:")
                for item in value:
                    print(f"      • {item}")
        else:
            for item in details:
                print(f"   • {item}")
    
    return flow_analysis

def check_extraction_function():
    """检查信息提取函数"""
    print(f"\n🔍 检查AI信息提取函数")
    print("=" * 80)
    
    ai_chat_file = "streamlit_team_management_modular/components/ai_chat.py"
    
    if os.path.exists(ai_chat_file):
        try:
            with open(ai_chat_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找提取信息相关的函数
            extraction_patterns = [
                "_extract_team_info",
                "extract_team_info",
                "extracted_info",
                "session_state.*team_info",
                "session_state.*players"
            ]
            
            extraction_code = {}
            lines = content.split('\n')
            
            for i, line in enumerate(lines, 1):
                for pattern in extraction_patterns:
                    import re
                    if re.search(pattern, line, re.IGNORECASE):
                        if pattern not in extraction_code:
                            extraction_code[pattern] = []
                        extraction_code[pattern].append({
                            'line': i,
                            'content': line.strip()
                        })
            
            print("📋 找到的信息提取相关代码:")
            for pattern, findings in extraction_code.items():
                print(f"\n🔍 {pattern}:")
                for finding in findings[:3]:  # 只显示前3个
                    print(f"   第{finding['line']}行: {finding['content'][:80]}...")
            
            return extraction_code
            
        except Exception as e:
            print(f"❌ 检查失败: {e}")
            return {}
    else:
        print(f"❌ 文件不存在: {ai_chat_file}")
        return {}

def check_display_function_data_source():
    """检查显示函数的数据源"""
    print(f"\n🔍 检查显示函数的数据源")
    print("=" * 80)
    
    ai_chat_file = "streamlit_team_management_modular/components/ai_chat.py"
    
    if os.path.exists(ai_chat_file):
        try:
            with open(ai_chat_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找我们新添加的显示函数
            display_function_start = content.find("def _display_team_info_friendly(self, team_info):")
            
            if display_function_start != -1:
                # 提取显示函数的代码
                lines = content[display_function_start:].split('\n')
                function_lines = []
                indent_level = None
                
                for line in lines:
                    if line.strip() == "":
                        function_lines.append(line)
                        continue
                    
                    current_indent = len(line) - len(line.lstrip())
                    
                    if indent_level is None and line.strip().startswith("def "):
                        indent_level = current_indent
                        function_lines.append(line)
                    elif indent_level is not None:
                        if current_indent > indent_level or line.strip() == "":
                            function_lines.append(line)
                        else:
                            break
                
                print("📋 显示函数的数据读取逻辑:")
                for i, line in enumerate(function_lines[:20], 1):  # 只显示前20行
                    if 'get(' in line or 'team_info' in line or 'basic_info' in line:
                        print(f"   第{i}行: {line}")
                
                # 分析数据读取路径
                data_paths = []
                for line in function_lines:
                    if '.get(' in line:
                        data_paths.append(line.strip())
                
                print(f"\n📊 数据读取路径:")
                for path in data_paths[:10]:  # 只显示前10个
                    print(f"   {path}")
                
                return function_lines
            else:
                print("❌ 未找到显示函数")
                return []
                
        except Exception as e:
            print(f"❌ 检查失败: {e}")
            return []
    else:
        print(f"❌ 文件不存在: {ai_chat_file}")
        return []

def check_session_state_keys():
    """检查session_state键名"""
    print(f"\n🔍 检查session_state键名")
    print("=" * 80)
    
    ai_chat_file = "streamlit_team_management_modular/components/ai_chat.py"
    
    if os.path.exists(ai_chat_file):
        try:
            with open(ai_chat_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找session_state相关的键名
            session_patterns = [
                r'st\.session_state\[[\'"](.*?)[\'"]\]',
                r'session_state\[[\'"](.*?)[\'"]\]',
                r'team_info_key\s*=',
                r'players_key\s*='
            ]
            
            session_keys = {}
            lines = content.split('\n')
            
            import re
            for i, line in enumerate(lines, 1):
                for pattern in session_patterns:
                    matches = re.findall(pattern, line)
                    if matches:
                        if pattern not in session_keys:
                            session_keys[pattern] = []
                        for match in matches:
                            session_keys[pattern].append({
                                'line': i,
                                'key': match,
                                'content': line.strip()
                            })
            
            print("📋 找到的session_state键名:")
            for pattern, findings in session_keys.items():
                print(f"\n🔑 {pattern}:")
                unique_keys = set()
                for finding in findings:
                    if finding['key'] not in unique_keys:
                        unique_keys.add(finding['key'])
                        print(f"   {finding['key']} (第{finding['line']}行)")
            
            return session_keys
            
        except Exception as e:
            print(f"❌ 检查失败: {e}")
            return {}
    else:
        print(f"❌ 文件不存在: {ai_chat_file}")
        return {}

def analyze_data_structure_mismatch():
    """分析数据结构不匹配问题"""
    print(f"\n🔍 分析数据结构不匹配问题")
    print("=" * 80)
    
    mismatch_analysis = {
        "显示函数期望的数据结构": {
            "路径": "team_info.get('ai_extracted_info', {}).get('basic_info', {})",
            "期望格式": {
                "ai_extracted_info": {
                    "basic_info": {
                        "team_name": "球队名称",
                        "contact_person": "联系人",
                        "contact_phone": "联系电话",
                        "leader_name": "队长"
                    }
                }
            }
        },
        
        "可能的实际数据结构": [
            {
                "情况1": "数据直接保存在team_info根级别",
                "结构": "team_info = {'team_name': '...', 'contact_person': '...'}"
            },
            {
                "情况2": "数据保存在extracted_info中",
                "结构": "team_info = {'extracted_info': {'basic_info': {...}}}"
            },
            {
                "情况3": "键名不匹配",
                "结构": "使用了不同的键名如'name'而不是'team_name'"
            },
            {
                "情况4": "数据未保存",
                "结构": "提取成功但未写入session_state"
            }
        ],
        
        "诊断方法": [
            "在显示函数中添加调试输出",
            "检查session_state的实际内容",
            "对比提取函数的输出格式",
            "验证键名映射关系"
        ]
    }
    
    for category, details in mismatch_analysis.items():
        print(f"\n🎯 {category}")
        if isinstance(details, dict):
            if "路径" in details:
                print(f"   路径: {details['路径']}")
            if "期望格式" in details:
                print(f"   期望格式: {details['期望格式']}")
        elif isinstance(details, list):
            for item in details:
                if isinstance(item, dict):
                    for key, value in item.items():
                        print(f"   {key}: {value}")
                else:
                    print(f"   • {item}")
    
    return mismatch_analysis

def generate_debugging_suggestions():
    """生成调试建议"""
    print(f"\n💡 生成调试建议")
    print("=" * 80)
    
    debugging_suggestions = {
        "立即检查": [
            {
                "问题": "数据结构不匹配",
                "方法": "在显示函数开头添加 st.write('DEBUG:', team_info)",
                "目的": "查看实际的数据结构"
            },
            {
                "问题": "session_state键名",
                "方法": "检查team_info_key的实际值",
                "目的": "确认键名是否正确"
            },
            {
                "问题": "提取函数输出",
                "方法": "在提取函数中添加调试输出",
                "目的": "确认提取是否成功"
            }
        ],
        
        "可能的修复方案": [
            {
                "方案1": "修改显示函数的数据读取路径",
                "适用": "如果数据结构与期望不符"
            },
            {
                "方案2": "修改提取函数的数据保存格式",
                "适用": "如果提取函数输出格式有问题"
            },
            {
                "方案3": "添加数据格式转换",
                "适用": "如果需要兼容多种数据格式"
            },
            {
                "方案4": "修复session_state保存逻辑",
                "适用": "如果数据未正确保存"
            }
        ],
        
        "测试步骤": [
            "1. 添加调试输出查看实际数据",
            "2. 确认session_state中的数据",
            "3. 对比期望与实际的数据结构",
            "4. 根据发现调整显示逻辑",
            "5. 测试修复效果"
        ]
    }
    
    for category, items in debugging_suggestions.items():
        print(f"\n💡 {category}")
        for item in items:
            if isinstance(item, dict):
                if "问题" in item:
                    print(f"   🔍 {item['问题']}")
                    print(f"      方法: {item['方法']}")
                    print(f"      目的: {item['目的']}")
                elif "方案1" in item or "方案2" in item or "方案3" in item or "方案4" in item:
                    for key, value in item.items():
                        print(f"   📋 {key}: {value}")
            else:
                print(f"   • {item}")
    
    return debugging_suggestions

def main():
    """主函数"""
    print("🔍 AI信息收集与显示断层问题分析")
    print("=" * 80)
    
    print("🎯 问题描述:")
    print("   AI已经收集了信息（聊天记录显示）")
    print("   但显示区域都是'未设置'")
    print("   说明信息收集和显示之间有断层")
    
    # 1. 分析AI信息流程
    flow_analysis = analyze_ai_info_flow()
    
    # 2. 检查提取函数
    extraction_code = check_extraction_function()
    
    # 3. 检查显示函数数据源
    display_function = check_display_function_data_source()
    
    # 4. 检查session_state键名
    session_keys = check_session_state_keys()
    
    # 5. 分析数据结构不匹配
    mismatch_analysis = analyze_data_structure_mismatch()
    
    # 6. 生成调试建议
    debugging_suggestions = generate_debugging_suggestions()
    
    # 总结
    print(f"\n🎯 问题分析总结")
    print("=" * 80)
    
    print("✅ 主要发现:")
    print("   🔍 AI确实收集了信息（聊天记录证实）")
    print("   📊 显示函数期望特定的数据结构")
    print("   🔗 可能存在数据结构不匹配问题")
    print("   🔑 session_state键名可能不一致")
    
    print(f"\n⚠️ 可能的原因:")
    print("   1. 显示函数读取路径与实际数据结构不符")
    print("   2. 提取的数据未正确保存到session_state")
    print("   3. session_state键名不匹配")
    print("   4. 数据格式转换问题")
    
    print(f"\n💡 建议调试步骤:")
    print("   1. 在显示函数中添加调试输出查看实际数据")
    print("   2. 检查session_state的实际内容")
    print("   3. 对比期望与实际的数据结构")
    print("   4. 根据发现调整数据读取逻辑")

if __name__ == "__main__":
    main()
