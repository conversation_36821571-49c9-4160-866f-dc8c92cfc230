#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析模板占位符问题
"""

import os
import sys
import json
import zipfile
import re
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_template_placeholders():
    """分析模板中的占位符"""
    print("=" * 60)
    print("🔍 分析模板中的占位符")
    print("=" * 60)
    
    template_path = "../word_zc/template_15players_fixed.docx"
    
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        return None
    
    try:
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        # 查找所有占位符
        placeholder_pattern = r'\{([^}]+)\}'
        placeholders = re.findall(placeholder_pattern, content)
        
        print(f"📄 模板文件: {os.path.basename(template_path)}")
        print(f"📄 找到 {len(placeholders)} 个占位符:")
        
        unique_placeholders = list(set(placeholders))
        unique_placeholders.sort()
        
        for placeholder in unique_placeholders:
            print(f"   {placeholder}")
        
        return unique_placeholders
        
    except Exception as e:
        print(f"❌ 分析模板失败: {e}")
        return None

def analyze_java_mapping():
    """分析Java程序的字段映射"""
    print(f"\n" + "=" * 60)
    print("🔍 分析Java程序的字段映射")
    print("=" * 60)
    
    java_dir = "../word_zc/ai-football-generator/src"
    
    if not os.path.exists(java_dir):
        print(f"❌ Java源代码目录不存在: {java_dir}")
        return None
    
    # 查找Java文件
    java_files = []
    for root, dirs, files in os.walk(java_dir):
        for file in files:
            if file.endswith('.java'):
                java_files.append(os.path.join(root, file))
    
    print(f"📄 检查 {len(java_files)} 个Java文件")
    
    field_mappings = {}
    
    for java_file in java_files:
        try:
            with open(java_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找字段映射
            # 查找类似 put("fieldName", value) 的模式
            put_pattern = r'put\s*\(\s*"([^"]+)"\s*,\s*([^)]+)\)'
            matches = re.findall(put_pattern, content)
            
            if matches:
                print(f"\n📄 {os.path.basename(java_file)}:")
                for field_name, value_expr in matches:
                    field_mappings[field_name] = value_expr.strip()
                    print(f"   {field_name} → {value_expr.strip()}")
        
        except Exception as e:
            pass
    
    return field_mappings

def test_word_generation_with_debug():
    """测试Word生成并调试占位符替换"""
    print(f"\n" + "=" * 60)
    print("🧪 测试Word生成并调试占位符替换")
    print("=" * 60)
    
    try:
        # 准备测试数据
        team_data = {
            "name": "占位符测试队",
            "contact_person": "测试联系人",
            "contact_phone": "13800000000",
            "leader": "测试领队",
            "coach": "测试教练",
            "doctor": "测试队医",
            "jersey_color": "红色",
            "shorts_color": "黑色",
            "socks_color": "红色",
            "goalkeeper_kit_color": "绿色"
        }
        
        players_data = [
            {"name": "测试球员1", "jersey_number": "1", "photo": ""},
            {"name": "测试球员2", "jersey_number": "2", "photo": ""}
        ]
        
        print(f"📄 测试数据:")
        for key, value in team_data.items():
            print(f"   {key}: '{value}'")
        
        # 生成Word文档
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        paths = app_settings.word_generator.get_absolute_paths("placeholder_debug", app_settings.paths)
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        # 检查准备的JSON数据
        json_data = word_service._prepare_json_data(team_data, players_data)
        
        print(f"\n📄 准备的JSON数据:")
        team_info = json_data.get("teamInfo", {})
        for key, value in team_info.items():
            print(f"   {key}: '{value}'")
        
        # 生成Word文档
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            output_file = result['file_path']
            print(f"\n✅ Word生成成功: {os.path.basename(output_file)}")
            
            # 分析生成的Word文档
            return analyze_generated_document(output_file, team_info)
        else:
            print(f"\n❌ Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_generated_document(docx_path, expected_data):
    """分析生成的Word文档"""
    print(f"\n📄 分析生成的Word文档:")
    
    try:
        with zipfile.ZipFile(docx_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        # 查找未替换的占位符
        placeholder_pattern = r'\{([^}]+)\}'
        remaining_placeholders = re.findall(placeholder_pattern, content)
        
        if remaining_placeholders:
            print(f"❌ 发现 {len(remaining_placeholders)} 个未替换的占位符:")
            unique_remaining = list(set(remaining_placeholders))
            for placeholder in unique_remaining:
                print(f"   {placeholder}")
        else:
            print(f"✅ 所有占位符都已替换")
        
        # 检查期望的数据是否出现在文档中
        print(f"\n📄 检查期望数据是否出现:")
        for key, value in expected_data.items():
            if str(value) in content:
                print(f"   ✅ {key}: '{value}' 已出现")
            else:
                print(f"   ❌ {key}: '{value}' 未出现")
        
        # 显示文档中的实际文本内容（清理后）
        text_pattern = r'<w:t[^>]*>([^<]*)</w:t>'
        texts = re.findall(text_pattern, content)
        
        # 过滤空文本和XML内容
        meaningful_texts = [text.strip() for text in texts if text.strip() and not text.startswith('<')]
        
        print(f"\n📄 文档中的实际文本内容（前20个）:")
        for i, text in enumerate(meaningful_texts[:20]):
            print(f"   {i+1}. '{text}'")
        
        return len(remaining_placeholders) == 0
        
    except Exception as e:
        print(f"❌ 分析文档失败: {e}")
        return False

def compare_placeholders_and_mappings(template_placeholders, java_mappings):
    """比较模板占位符和Java映射"""
    print(f"\n" + "=" * 60)
    print("🔍 比较模板占位符和Java映射")
    print("=" * 60)
    
    if not template_placeholders or not java_mappings:
        print(f"❌ 缺少数据进行比较")
        return
    
    print(f"📄 模板占位符 vs Java映射:")
    
    # 检查模板中的占位符是否在Java中有对应的映射
    missing_mappings = []
    for placeholder in template_placeholders:
        if placeholder in java_mappings:
            print(f"   ✅ {placeholder} → {java_mappings[placeholder]}")
        else:
            print(f"   ❌ {placeholder} → 缺少映射")
            missing_mappings.append(placeholder)
    
    # 检查Java中的映射是否在模板中有对应的占位符
    extra_mappings = []
    for field_name in java_mappings:
        if field_name not in template_placeholders:
            extra_mappings.append(field_name)
    
    if missing_mappings:
        print(f"\n❌ 模板中缺少映射的占位符:")
        for placeholder in missing_mappings:
            print(f"   {placeholder}")
    
    if extra_mappings:
        print(f"\n⚠️ Java中多余的映射:")
        for field_name in extra_mappings:
            print(f"   {field_name}")
    
    if not missing_mappings and not extra_mappings:
        print(f"\n✅ 模板占位符和Java映射完全匹配")

def main():
    """主函数"""
    print("🚀 分析模板占位符问题")
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 分析模板占位符
        template_placeholders = analyze_template_placeholders()
        
        # 2. 分析Java映射
        java_mappings = analyze_java_mapping()
        
        # 3. 比较占位符和映射
        compare_placeholders_and_mappings(template_placeholders, java_mappings)
        
        # 4. 测试Word生成
        word_success = test_word_generation_with_debug()
        
        print("\n" + "=" * 60)
        print("📋 分析总结")
        print("=" * 60)
        
        print(f"📊 分析结果:")
        print(f"   模板占位符: {len(template_placeholders) if template_placeholders else 0}")
        print(f"   Java映射: {len(java_mappings) if java_mappings else 0}")
        print(f"   Word生成测试: {'✅ 成功' if word_success else '❌ 失败'}")
        
        if not word_success:
            print(f"\n🎯 可能的问题:")
            print(f"   1. 模板占位符与Java映射不匹配")
            print(f"   2. JSON数据字段名与Java期望的不一致")
            print(f"   3. Java程序处理逻辑有问题")
            print(f"   4. 模板文件损坏或格式问题")
            
            print(f"\n💡 解决建议:")
            print(f"   1. 检查并修复占位符映射")
            print(f"   2. 确保JSON数据字段名正确")
            print(f"   3. 调试Java程序的处理逻辑")
            print(f"   4. 验证模板文件的完整性")
        else:
            print(f"\n✅ 占位符替换正常工作")
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
