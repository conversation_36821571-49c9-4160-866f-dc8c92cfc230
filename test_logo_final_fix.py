#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
队徽最终修复测试
Logo Final Fix Test

使用正确的字段名进行最终测试
"""

import os
import sys
import json
import tempfile
import subprocess
from datetime import datetime

# 添加项目路径
sys.path.append('streamlit_team_management_modular')

def test_logo_final_fix():
    """队徽最终修复测试"""
    
    print("🎯 队徽最终修复测试")
    print("=" * 80)
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # 步骤1: 生成队徽
        print("步骤1: 生成队徽")
        print("-" * 40)
        
        from services.fashion_workflow_service import FashionWorkflowService
        workflow_service = FashionWorkflowService()
        
        test_team_name = "队徽最终修复测试队"
        logo_path = workflow_service._auto_generate_team_logo(test_team_name)
        abs_logo_path = os.path.abspath(logo_path)
        
        print(f"✅ 队徽生成: {logo_path}")
        print(f"   绝对路径: {abs_logo_path}")
        print(f"   文件存在: {os.path.exists(abs_logo_path)}")
        print(f"   文件大小: {os.path.getsize(abs_logo_path)/1024:.1f}KB")
        
        # 步骤2: 创建正确格式的测试数据
        print(f"\n步骤2: 创建正确格式的测试数据")
        print("-" * 40)
        
        # 使用Java程序期望的字段名
        test_data = {
            "teamInfo": {
                "title": f"{test_team_name}报名表",
                "organizationName": test_team_name,
                "teamLeader": "测试领队",
                "coach": "测试教练",
                "teamDoctor": "测试队医",
                "contactPerson": "测试联系人",
                "contactPhone": "13800138000",
                "logoPath": abs_logo_path,  # 队徽路径
                "jerseyColor": "红色",
                "shortsColor": "蓝色", 
                "socksColor": "白色",
                "goalkeeperKitColor": "绿色"
            },
            "players": [
                {
                    "number": "1",  # 使用 "number" 而不是 "jerseyNumber"
                    "name": "测试球员1",
                    "photoPath": ""
                },
                {
                    "number": "2",
                    "name": "测试球员2", 
                    "photoPath": ""
                }
            ],
            "config": {
                "templatePath": os.path.abspath("word_zc/template_15players_fixed.docx"),
                "outputDir": os.path.abspath("word_zc/ai-football-generator/output"),
                "photosDir": os.path.abspath("photos")
            }
        }
        
        print(f"✅ 测试数据准备完成")
        print(f"   logoPath: {test_data['teamInfo']['logoPath']}")
        print(f"   球员数量: {len(test_data['players'])}")
        print(f"   球员字段: {list(test_data['players'][0].keys())}")
        
        # 确保输出目录存在
        os.makedirs(test_data['config']['outputDir'], exist_ok=True)
        
        # 步骤3: 写入JSON文件
        print(f"\n步骤3: 写入JSON文件")
        print("-" * 40)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
            test_json_file = f.name
        
        print(f"✅ JSON文件: {test_json_file}")
        
        # 步骤4: 调用Java程序
        print(f"\n步骤4: 调用Java程序")
        print("-" * 40)
        
        jar_path = "word_zc/ai-football-generator/target/word-generator.jar"
        cmd = ['java', '-cp', jar_path, 'CommandLineMain', test_json_file]
        
        print(f"🚀 执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=120,
            encoding='utf-8',
            errors='ignore'
        )
        
        print(f"\n📊 Java程序执行结果:")
        print(f"   返回码: {result.returncode}")
        
        # 步骤5: 分析输出
        print(f"\n步骤5: 分析输出")
        print("-" * 40)
        
        output_text = result.stdout + result.stderr
        
        # 检查关键信息
        logo_parsed = '队徽路径=' in output_text
        players_parsed = 'Player parsed:' in output_text
        word_success = 'SUCCESS:' in output_text
        has_error = 'ERROR:' in output_text
        
        print(f"🔍 关键检查结果:")
        print(f"   队徽路径解析: {'✅' if logo_parsed else '❌'}")
        print(f"   球员数据解析: {'✅' if players_parsed else '❌'}")
        print(f"   Word生成成功: {'✅' if word_success else '❌'}")
        print(f"   有错误信息: {'❌' if has_error else '✅'}")
        
        # 显示关键输出行
        print(f"\n📄 关键输出:")
        for line in output_text.split('\n'):
            if any(keyword in line for keyword in ['INFO:', 'WARN:', 'ERROR:', 'SUCCESS:', '队徽路径=', 'Player parsed:']):
                print(f"   {line}")
        
        # 步骤6: 检查生成的Word文件
        print(f"\n步骤6: 检查生成的Word文件")
        print("-" * 40)
        
        output_dir = test_data['config']['outputDir']
        if os.path.exists(output_dir):
            files = [f for f in os.listdir(output_dir) if f.endswith('.docx')]
            files.sort(key=lambda x: os.path.getmtime(os.path.join(output_dir, x)), reverse=True)
            
            if files:
                latest_file = files[0]
                latest_path = os.path.join(output_dir, latest_file)
                file_size_kb = os.path.getsize(latest_path) / 1024
                file_age = datetime.now().timestamp() - os.path.getmtime(latest_path)
                
                print(f"✅ 最新Word文件: {latest_file}")
                print(f"   文件大小: {file_size_kb:.1f}KB")
                print(f"   修改时间: {datetime.fromtimestamp(os.path.getmtime(latest_path)).strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"   文件年龄: {file_age:.0f}秒")
                
                # 判断是否是新生成的文件
                if file_age < 60:  # 1分钟内
                    print(f"   ✅ 文件是新生成的")
                    
                    # 检查文件大小是否合理
                    if file_size_kb > 50:  # 如果文件大于50KB，可能包含了队徽
                        print(f"   ✅ 文件大小合理，可能包含队徽")
                    else:
                        print(f"   ⚠️ 文件大小较小，可能未包含队徽")
                else:
                    print(f"   ⚠️ 文件较旧，可能不是本次生成的")
            else:
                print("❌ 输出目录中没有Word文件")
        else:
            print(f"❌ 输出目录不存在: {output_dir}")
        
        # 步骤7: 最终评估
        print(f"\n步骤7: 最终评估")
        print("-" * 40)
        
        success_criteria = {
            '队徽路径解析': logo_parsed,
            '球员数据解析': players_parsed,
            'Java程序成功': result.returncode == 0,
            'Word生成成功': word_success,
            '无错误信息': not has_error
        }
        
        print(f"📊 成功标准检查:")
        success_count = 0
        for criterion, passed in success_criteria.items():
            status = "✅" if passed else "❌"
            print(f"   {status} {criterion}")
            if passed:
                success_count += 1
        
        total_criteria = len(success_criteria)
        success_rate = success_count / total_criteria * 100
        
        print(f"\n🎯 最终结果:")
        print(f"   成功项: {success_count}/{total_criteria}")
        print(f"   成功率: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("🎉 队徽修复测试成功！队徽应该已经插入到Word文档中！")
        elif success_rate >= 60:
            print("⚠️ 队徽修复部分成功，还有一些小问题需要解决")
        else:
            print("❌ 队徽修复测试失败，需要进一步调试")
        
        # 清理临时文件
        try:
            os.unlink(test_json_file)
        except:
            pass
        
        print(f"\n⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return {
            'success_rate': success_rate,
            'logo_parsed': logo_parsed,
            'players_parsed': players_parsed,
            'word_success': word_success,
            'java_return_code': result.returncode
        }
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_logo_final_fix()
