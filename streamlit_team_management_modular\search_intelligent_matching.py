#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索"智能搭配"相关的所有内容
"""

import os
import sys
import json
import zipfile
import re
from datetime import datetime

def search_in_text_files():
    """搜索所有文本文件中的"智能搭配"""
    print("=" * 60)
    print("🔍 搜索所有文本文件中的'智能搭配'")
    print("=" * 60)
    
    search_terms = ["智能搭配", "智能填充", "AI搭配", "AI填充", "自动搭配"]
    found_files = []
    
    # 搜索Python文件
    for root, dirs, files in os.walk("."):
        # 跳过一些目录
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules']]
        
        for file in files:
            if file.endswith(('.py', '.txt', '.md', '.json', '.yml', '.yaml')):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    for term in search_terms:
                        if term in content:
                            count = content.count(term)
                            found_files.append((file_path, term, count))
                            print(f"📄 {file_path}: '{term}' 出现 {count} 次")
                            
                            # 显示上下文
                            lines = content.split('\n')
                            for i, line in enumerate(lines):
                                if term in line:
                                    print(f"   第{i+1}行: {line.strip()}")
                                    break
                            
                except Exception as e:
                    pass
    
    return found_files

def search_in_word_templates():
    """搜索Word模板文件中的"智能搭配"""
    print(f"\n" + "=" * 60)
    print("🔍 搜索Word模板文件中的'智能搭配'")
    print("=" * 60)
    
    template_dir = "../word_zc"
    template_files = []
    
    # 查找所有docx文件
    for root, dirs, files in os.walk(template_dir):
        for file in files:
            if file.endswith('.docx') and not file.startswith('~$'):
                template_files.append(os.path.join(root, file))
    
    print(f"📄 找到 {len(template_files)} 个模板文件")
    
    search_terms = ["智能搭配", "智能填充", "AI搭配", "AI填充", "自动搭配"]
    problematic_templates = []
    
    for template_path in template_files:
        print(f"\n📄 检查: {os.path.basename(template_path)}")
        
        try:
            with zipfile.ZipFile(template_path, 'r') as zip_file:
                with zip_file.open('word/document.xml') as xml_file:
                    content = xml_file.read().decode('utf-8')
            
            found_terms = []
            for term in search_terms:
                count = content.count(term)
                if count > 0:
                    found_terms.append((term, count))
            
            if found_terms:
                print(f"   ❌ 发现问题:")
                for term, count in found_terms:
                    print(f"      '{term}': {count} 次")
                
                problematic_templates.append((template_path, found_terms))
                
                # 显示上下文
                for term, _ in found_terms:
                    pattern = f'.{{0,50}}{re.escape(term)}.{{0,50}}'
                    matches = re.findall(pattern, content)
                    print(f"   上下文 '{term}':")
                    for i, match in enumerate(matches[:2]):
                        clean_match = match.replace('<', '&lt;').replace('>', '&gt;')
                        print(f"      {i+1}. {clean_match}")
            else:
                print(f"   ✅ 正常")
                
        except Exception as e:
            print(f"   ❌ 检查失败: {e}")
    
    return problematic_templates

def search_in_recent_word_files():
    """搜索最近生成的Word文件"""
    print(f"\n" + "=" * 60)
    print("🔍 搜索最近生成的Word文件中的'智能搭配'")
    print("=" * 60)
    
    # 查找所有word_output目录下的docx文件
    word_files = []
    for root, dirs, files in os.walk("data"):
        if "word_output" in root:
            for file in files:
                if file.endswith('.docx'):
                    file_path = os.path.join(root, file)
                    word_files.append(file_path)
    
    # 按修改时间排序，检查最近的10个
    word_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    
    print(f"📄 找到 {len(word_files)} 个Word文件，检查最近的10个:")
    
    search_terms = ["智能搭配", "智能填充", "AI搭配", "AI填充", "自动搭配"]
    problematic_files = []
    
    for word_file in word_files[:10]:
        print(f"\n📄 检查: {os.path.basename(word_file)}")
        print(f"   路径: {word_file}")
        print(f"   修改时间: {datetime.fromtimestamp(os.path.getmtime(word_file))}")
        
        try:
            with zipfile.ZipFile(word_file, 'r') as zip_file:
                with zip_file.open('word/document.xml') as xml_file:
                    content = xml_file.read().decode('utf-8')
            
            found_terms = []
            for term in search_terms:
                count = content.count(term)
                if count > 0:
                    found_terms.append((term, count))
            
            if found_terms:
                print(f"   ❌ 发现问题:")
                for term, count in found_terms:
                    print(f"      '{term}': {count} 次")
                
                problematic_files.append((word_file, found_terms))
                
                # 显示上下文
                for term, _ in found_terms:
                    pattern = f'.{{0,30}}{re.escape(term)}.{{0,30}}'
                    matches = re.findall(pattern, content)
                    print(f"   上下文 '{term}':")
                    for i, match in enumerate(matches[:2]):
                        clean_match = match.replace('<', '&lt;').replace('>', '&gt;')
                        print(f"      {i+1}. {clean_match}")
            else:
                print(f"   ✅ 正常")
                
        except Exception as e:
            print(f"   ❌ 检查失败: {e}")
    
    return problematic_files

def search_in_user_data():
    """搜索用户数据中的"智能搭配"""
    print(f"\n" + "=" * 60)
    print("🔍 搜索用户数据中的'智能搭配'")
    print("=" * 60)
    
    data_dir = "data"
    search_terms = ["智能搭配", "智能填充", "AI搭配", "AI填充", "自动搭配"]
    
    # 查找所有JSON文件
    json_files = []
    for root, dirs, files in os.walk(data_dir):
        for file in files:
            if file.endswith('.json'):
                json_files.append(os.path.join(root, file))
    
    print(f"📄 检查 {len(json_files)} 个JSON文件")
    
    problematic_data = []
    
    for json_file in json_files:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            found_terms = []
            for term in search_terms:
                count = content.count(term)
                if count > 0:
                    found_terms.append((term, count))
            
            if found_terms:
                print(f"\n❌ 发现问题文件: {json_file}")
                for term, count in found_terms:
                    print(f"   '{term}': {count} 次")
                
                problematic_data.append((json_file, found_terms))
                
        except Exception as e:
            pass
    
    if not problematic_data:
        print(f"✅ 所有用户数据正常")
    
    return problematic_data

def check_java_source_code():
    """检查Java源代码"""
    print(f"\n" + "=" * 60)
    print("🔍 检查Java源代码中的'智能搭配'")
    print("=" * 60)
    
    java_dir = "../word_zc/ai-football-generator/src"
    search_terms = ["智能搭配", "智能填充", "AI搭配", "AI填充", "自动搭配"]
    
    if not os.path.exists(java_dir):
        print(f"❌ Java源代码目录不存在: {java_dir}")
        return []
    
    java_files = []
    for root, dirs, files in os.walk(java_dir):
        for file in files:
            if file.endswith('.java'):
                java_files.append(os.path.join(root, file))
    
    print(f"📄 检查 {len(java_files)} 个Java文件")
    
    problematic_java = []
    
    for java_file in java_files:
        try:
            with open(java_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            found_terms = []
            for term in search_terms:
                count = content.count(term)
                if count > 0:
                    found_terms.append((term, count))
            
            if found_terms:
                print(f"\n❌ 发现问题文件: {java_file}")
                for term, count in found_terms:
                    print(f"   '{term}': {count} 次")
                
                problematic_java.append((java_file, found_terms))
                
                # 显示上下文
                lines = content.split('\n')
                for term, _ in found_terms:
                    for i, line in enumerate(lines):
                        if term in line:
                            print(f"   第{i+1}行: {line.strip()}")
                            break
                
        except Exception as e:
            pass
    
    if not problematic_java:
        print(f"✅ 所有Java代码正常")
    
    return problematic_java

def main():
    """主函数"""
    print("🚀 搜索'智能搭配'相关的所有内容")
    print(f"搜索时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 搜索文本文件
        text_files = search_in_text_files()
        
        # 2. 搜索Word模板
        template_files = search_in_word_templates()
        
        # 3. 搜索最近的Word文件
        word_files = search_in_recent_word_files()
        
        # 4. 搜索用户数据
        user_data = search_in_user_data()
        
        # 5. 检查Java源代码
        java_files = check_java_source_code()
        
        print("\n" + "=" * 60)
        print("📋 搜索总结")
        print("=" * 60)
        
        print(f"📊 搜索结果:")
        print(f"   文本文件问题: {len(text_files)}")
        print(f"   模板文件问题: {len(template_files)}")
        print(f"   Word文件问题: {len(word_files)}")
        print(f"   用户数据问题: {len(user_data)}")
        print(f"   Java代码问题: {len(java_files)}")
        
        total_issues = len(text_files) + len(template_files) + len(word_files) + len(user_data) + len(java_files)
        
        if total_issues > 0:
            print(f"\n🎯 发现 {total_issues} 个问题源:")
            
            if template_files:
                print(f"\n❌ 模板文件问题 (最可能的原因):")
                for template_path, terms in template_files:
                    print(f"   {os.path.basename(template_path)}: {terms}")
                    
            if word_files:
                print(f"\n❌ Word文件问题:")
                for word_path, terms in word_files:
                    print(f"   {os.path.basename(word_path)}: {terms}")
                    
            if java_files:
                print(f"\n❌ Java代码问题:")
                for java_path, terms in java_files:
                    print(f"   {os.path.basename(java_path)}: {terms}")
            
            print(f"\n💡 解决建议:")
            if template_files:
                print(f"   1. 修复模板文件中的'智能搭配'文本")
                print(f"   2. 将'智能搭配'替换为对应的占位符")
            if java_files:
                print(f"   3. 修复Java代码中的硬编码文本")
            print(f"   4. 确保用户使用正确的模板和代码版本")
            
        else:
            print(f"\n✅ 未发现'智能搭配'相关问题")
            print(f"   问题可能是:")
            print(f"   1. 用户使用了不同版本的模板或代码")
            print(f"   2. 问题出现在特定的用户操作流程中")
            print(f"   3. 缓存或临时文件问题")
        
    except Exception as e:
        print(f"❌ 搜索过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
