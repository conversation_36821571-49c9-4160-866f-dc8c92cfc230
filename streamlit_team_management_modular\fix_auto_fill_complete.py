#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整修复自动填充问题
"""

import os
import sys
import json
import shutil
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_specific_user_data():
    """修复特定用户数据"""
    print("=" * 60)
    print("🔧 修复天依909用户数据")
    print("=" * 60)
    
    # 直接修复天依909的数据
    test_file = "data/user_f33368cb41dd/enhanced_ai_data/天依909_ai_data.json"
    
    if not os.path.exists(test_file):
        print(f"❌ 文件不存在: {test_file}")
        return False
    
    try:
        # 备份原文件
        backup_path = f"{test_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(test_file, backup_path)
        print(f"✅ 已备份到: {os.path.basename(backup_path)}")
        
        # 读取原数据
        with open(test_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📄 原始数据:")
        extracted_info = data.get("extracted_info", {})
        basic_info = extracted_info.get("basic_info", {})
        kit_colors = extracted_info.get("kit_colors", {})
        
        for key, value in basic_info.items():
            print(f"   {key}: '{value}'")
        for key, value in kit_colors.items():
            print(f"   {key}: '{value}'")
        
        # 手动应用自动填充逻辑
        contact_person = basic_info.get("contact_person", "")
        jersey_color = kit_colors.get("jersey_color", "")
        
        # 修复人员信息
        if contact_person and contact_person not in ["自动填充", "", "待定", "未知", "暂无"]:
            if basic_info.get("leader_name") == "自动填充" or not basic_info.get("leader_name"):
                basic_info["leader_name"] = contact_person
                print(f"✅ 修复领队: '{contact_person}'")
            
            if basic_info.get("team_doctor") == "自动填充" or not basic_info.get("team_doctor"):
                basic_info["team_doctor"] = contact_person
                print(f"✅ 修复队医: '{contact_person}'")
        
        # 修复颜色信息
        if jersey_color and jersey_color not in ["自动填充", "", "待定", "未知", "暂无"]:
            jersey_color_lower = jersey_color.lower()
            
            # 球裤颜色
            if not kit_colors.get("shorts_color"):
                if jersey_color_lower in ["红", "蓝", "绿", "黄", "红色", "蓝色", "绿色", "黄色", "粉色", "紫色", "橙色"]:
                    kit_colors["shorts_color"] = "黑色"
                elif jersey_color_lower in ["白", "白色"]:
                    kit_colors["shorts_color"] = "白色"
                else:
                    kit_colors["shorts_color"] = "黑色"
                print(f"✅ 修复球裤颜色: '{kit_colors['shorts_color']}'")
            
            # 球袜颜色（与球衣同色）
            if not kit_colors.get("socks_color"):
                kit_colors["socks_color"] = jersey_color
                print(f"✅ 修复球袜颜色: '{kit_colors['socks_color']}'")
            
            # 守门员服装颜色
            if not kit_colors.get("goalkeeper_kit_color"):
                if jersey_color_lower in ["绿", "绿色"]:
                    kit_colors["goalkeeper_kit_color"] = "橙色"
                elif jersey_color_lower in ["白", "白色"]:
                    kit_colors["goalkeeper_kit_color"] = "黄色"
                else:
                    kit_colors["goalkeeper_kit_color"] = "绿色"
                print(f"✅ 修复守门员服装颜色: '{kit_colors['goalkeeper_kit_color']}'")
        
        # 确保additional_info存在
        if "additional_info" not in extracted_info:
            extracted_info["additional_info"] = {}
        
        # 设置教练信息
        if contact_person and not extracted_info["additional_info"].get("coach_name"):
            extracted_info["additional_info"]["coach_name"] = contact_person
            print(f"✅ 设置教练: '{contact_person}'")
        
        # 保存修复后的数据
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 修复后数据:")
        for key, value in basic_info.items():
            print(f"   {key}: '{value}'")
        for key, value in kit_colors.items():
            print(f"   {key}: '{value}'")
        for key, value in extracted_info.get("additional_info", {}).items():
            print(f"   {key}: '{value}'")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_workflow_data():
    """修复工作流数据"""
    print(f"\n" + "=" * 60)
    print("🔧 修复工作流数据")
    print("=" * 60)
    
    workflow_file = "data/user_f33368cb41dd/fashion_workflow/workflow_天依909_20250831_163906.json"
    
    if not os.path.exists(workflow_file):
        print(f"❌ 工作流文件不存在: {workflow_file}")
        return False
    
    try:
        # 备份原文件
        backup_path = f"{workflow_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(workflow_file, backup_path)
        print(f"✅ 已备份到: {os.path.basename(backup_path)}")
        
        # 读取原数据
        with open(workflow_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 修复工作流中的AI导出数据
        if "readiness_check" in data and "ai_export_data" in data["readiness_check"]:
            ai_export_data = data["readiness_check"]["ai_export_data"]
            if "team_info" in ai_export_data and "ai_extracted_info" in ai_export_data["team_info"]:
                extracted_info = ai_export_data["team_info"]["ai_extracted_info"]
                basic_info = extracted_info.get("basic_info", {})
                kit_colors = extracted_info.get("kit_colors", {})
                
                contact_person = basic_info.get("contact_person", "")
                jersey_color = kit_colors.get("jersey_color", "")
                
                # 应用相同的修复逻辑
                if contact_person and contact_person not in ["自动填充", "", "待定", "未知", "暂无"]:
                    if basic_info.get("leader_name") == "自动填充" or not basic_info.get("leader_name"):
                        basic_info["leader_name"] = contact_person
                        print(f"✅ 修复工作流领队: '{contact_person}'")
                    
                    if basic_info.get("team_doctor") == "自动填充" or not basic_info.get("team_doctor"):
                        basic_info["team_doctor"] = contact_person
                        print(f"✅ 修复工作流队医: '{contact_person}'")
                
                if jersey_color and jersey_color not in ["自动填充", "", "待定", "未知", "暂无"]:
                    jersey_color_lower = jersey_color.lower()
                    
                    if not kit_colors.get("shorts_color"):
                        if jersey_color_lower in ["红", "蓝", "绿", "黄", "红色", "蓝色", "绿色", "黄色", "粉色", "紫色", "橙色"]:
                            kit_colors["shorts_color"] = "黑色"
                        else:
                            kit_colors["shorts_color"] = "黑色"
                        print(f"✅ 修复工作流球裤颜色: '{kit_colors['shorts_color']}'")
                    
                    if not kit_colors.get("socks_color"):
                        kit_colors["socks_color"] = jersey_color
                        print(f"✅ 修复工作流球袜颜色: '{kit_colors['socks_color']}'")
                    
                    if not kit_colors.get("goalkeeper_kit_color"):
                        if jersey_color_lower in ["绿", "绿色"]:
                            kit_colors["goalkeeper_kit_color"] = "橙色"
                        elif jersey_color_lower in ["白", "白色"]:
                            kit_colors["goalkeeper_kit_color"] = "黄色"
                        else:
                            kit_colors["goalkeeper_kit_color"] = "绿色"
                        print(f"✅ 修复工作流守门员服装颜色: '{kit_colors['goalkeeper_kit_color']}'")
                
                # 确保additional_info存在
                if "additional_info" not in extracted_info:
                    extracted_info["additional_info"] = {}
                
                if contact_person and not extracted_info["additional_info"].get("coach_name"):
                    extracted_info["additional_info"]["coach_name"] = contact_person
                    print(f"✅ 设置工作流教练: '{contact_person}'")
        
        # 保存修复后的数据
        with open(workflow_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        return True
        
    except Exception as e:
        print(f"❌ 修复工作流失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_word_generation():
    """测试Word生成"""
    print(f"\n" + "=" * 60)
    print("📄 测试Word生成")
    print("=" * 60)
    
    try:
        # 读取修复后的数据
        test_file = "data/user_f33368cb41dd/enhanced_ai_data/天依909_ai_data.json"
        
        with open(test_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        extracted_info = data.get("extracted_info", {})
        basic_info = extracted_info.get("basic_info", {})
        kit_colors = extracted_info.get("kit_colors", {})
        additional_info = extracted_info.get("additional_info", {})
        
        # 准备Word生成数据
        team_data = {
            "name": basic_info.get("team_name", "天依909"),
            "contact_person": basic_info.get("contact_person"),
            "contact_phone": basic_info.get("contact_phone"),
            "leader": basic_info.get("leader_name"),
            "coach": additional_info.get("coach_name", basic_info.get("leader_name")),
            "doctor": basic_info.get("team_doctor"),
            "jersey_color": kit_colors.get("jersey_color"),
            "shorts_color": kit_colors.get("shorts_color"),
            "socks_color": kit_colors.get("socks_color"),
            "goalkeeper_kit_color": kit_colors.get("goalkeeper_kit_color")
        }
        
        players_data = [
            {"name": "张三", "jersey_number": "1", "photo": ""}
        ]
        
        print(f"📄 Word生成数据:")
        for key, value in team_data.items():
            print(f"   {key}: '{value}'")
        
        # 检查数据完整性
        missing_fields = []
        for key, value in team_data.items():
            if not value or value in ["自动填充", "", "待定", "未知", "暂无"]:
                missing_fields.append(key)
        
        if missing_fields:
            print(f"❌ 缺少字段: {missing_fields}")
            return False
        
        # 生成Word文档
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        paths = app_settings.word_generator.get_absolute_paths("final_fix_test", app_settings.paths)
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            output_file = result['file_path']
            print(f"\n✅ Word生成成功: {os.path.basename(output_file)}")
            return True
        else:
            print(f"\n❌ Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ Word生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 完整修复自动填充问题")
    print(f"修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 修复用户数据
        user_data_ok = fix_specific_user_data()
        
        # 2. 修复工作流数据
        workflow_ok = fix_workflow_data()
        
        # 3. 测试Word生成
        word_ok = test_word_generation()
        
        print("\n" + "=" * 60)
        print("📋 修复总结")
        print("=" * 60)
        
        print(f"📊 修复结果:")
        print(f"   用户数据修复: {'✅ 成功' if user_data_ok else '❌ 失败'}")
        print(f"   工作流数据修复: {'✅ 成功' if workflow_ok else '❌ 失败'}")
        print(f"   Word生成测试: {'✅ 成功' if word_ok else '❌ 失败'}")
        
        if user_data_ok and workflow_ok and word_ok:
            print(f"\n🎉 修复完全成功!")
            print(f"   ✅ 清理了'自动填充'占位符")
            print(f"   ✅ 重新应用了自动填充逻辑")
            print(f"   ✅ Word生成功能正常")
            print(f"   ✅ 所有字段都有正确的值")
            
            print(f"\n💡 修复内容:")
            print(f"   1. 领队、队医 → 联系人'赵六'")
            print(f"   2. 球裤颜色 → '黑色'（根据粉色球衣）")
            print(f"   3. 球袜颜色 → '粉色'（与球衣同色）")
            print(f"   4. 守门员服装 → '绿色'（默认颜色）")
            print(f"   5. 教练 → '赵六'")
            
        else:
            print(f"\n⚠️ 修复部分成功")
            if not user_data_ok:
                print(f"   ❌ 用户数据修复失败")
            if not workflow_ok:
                print(f"   ❌ 工作流数据修复失败")
            if not word_ok:
                print(f"   ❌ Word生成测试失败")
        
        print(f"\n💡 用户使用建议:")
        print(f"   1. 现在可以正常生成Word报名表")
        print(f"   2. 所有字段都会正确显示")
        print(f"   3. 不会再出现空白字段")
        
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
