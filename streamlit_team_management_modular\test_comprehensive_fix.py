#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试全面修复效果
"""

import zipfile
import xml.etree.ElementTree as ET
import os
import json

def test_comprehensive_fix():
    """测试全面修复效果"""
    print("🔍 测试全面修复效果")
    print("=" * 60)
    
    try:
        import streamlit as st
        
        # 设置session state
        if 'user_id' not in st.session_state:
            st.session_state.user_id = 'user_44ecbeed9db2'
        
        from services.fashion_workflow_service import FashionWorkflowService
        
        # 创建工作流服务
        workflow_service = FashionWorkflowService('user_44ecbeed9db2')
        
        # 测试修复后的_auto_generate_word_document方法
        print("📄 测试修复后的Word生成")
        
        # 调用修复后的方法
        word_result = workflow_service._auto_generate_word_document(
            "天依003", {}, None
        )
        
        print(f"   Word生成结果: {word_result}")
        
        if word_result.get('success'):
            print("✅ Word生成成功")
            
            # 检查生成的文件内容
            file_path = word_result.get('file_path')
            if file_path and os.path.exists(file_path):
                return analyze_fixed_word_content(file_path)
            else:
                print("❌ 生成的文件不存在")
                return False
        else:
            print(f"❌ Word生成失败: {word_result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_fixed_word_content(file_path):
    """分析修复后的Word内容"""
    print(f"\n🔍 分析修复后的Word内容")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    print(f"📄 修复后Word文档完整内容:")
                    print(f"   {full_text}")
                    
                    # 分析修复效果
                    fix_results = {}
                    
                    # 1. 检查团队名称修复
                    print(f"\n📋 1. 团队名称修复检查:")
                    has_correct_team_name = "天依003" in full_text
                    has_default_team_name = "足球队" in full_text and "天依003" not in full_text
                    
                    if has_correct_team_name:
                        print(f"   ✅ 团队名称修复成功: 显示'天依003'")
                        fix_results['team_name'] = 'fixed'
                    elif has_default_team_name:
                        print(f"   ❌ 团队名称仍有问题: 显示'足球队'")
                        fix_results['team_name'] = 'not_fixed'
                    else:
                        print(f"   ⚠️ 团队名称状态不明")
                        fix_results['team_name'] = 'unknown'
                    
                    # 2. 检查"自动填充"问题修复
                    print(f"\n📋 2. '自动填充'问题修复检查:")
                    auto_fill_count = full_text.count("自动填充")
                    
                    if auto_fill_count == 0:
                        print(f"   ✅ '自动填充'问题修复成功: 无'自动填充'字样")
                        fix_results['auto_fill'] = 'fixed'
                    else:
                        print(f"   ❌ '自动填充'问题仍存在: 发现{auto_fill_count}个")
                        fix_results['auto_fill'] = 'not_fixed'
                        
                        # 显示仍存在的"自动填充"位置
                        words = full_text.split()
                        for i, word in enumerate(words):
                            if "自动填充" in word:
                                start = max(0, i-3)
                                end = min(len(words), i+3)
                                context = ' '.join(words[start:end])
                                print(f"     仍存在: {context}")
                    
                    # 3. 检查联系人信息（应该仍然正常）
                    print(f"\n📋 3. 联系人信息检查:")
                    has_contact_person = "赵六" in full_text
                    has_contact_phone = "18454432036" in full_text
                    
                    if has_contact_person and has_contact_phone:
                        print(f"   ✅ 联系人信息正常: 赵六 18454432036")
                        fix_results['contact_info'] = 'working'
                    else:
                        print(f"   ❌ 联系人信息有问题")
                        fix_results['contact_info'] = 'broken'
                    
                    # 4. 检查颜色字段（需要有测试数据才能验证）
                    print(f"\n📋 4. 颜色字段检查:")
                    # 由于当前测试数据可能没有颜色信息，我们检查是否有颜色相关的空白区域
                    color_areas = []
                    words = full_text.split()
                    for i, word in enumerate(words):
                        if any(keyword in word for keyword in ['球衣', '球裤', '球袜', '守门员']):
                            start = max(0, i-2)
                            end = min(len(words), i+8)
                            context = ' '.join(words[start:end])
                            color_areas.append(context)
                    
                    print(f"   颜色相关区域:")
                    for area in color_areas:
                        print(f"     {area}")
                    
                    fix_results['color_areas'] = color_areas
                    
                    # 5. 检查球员信息
                    print(f"\n📋 5. 球员信息检查:")
                    player_names_found = []
                    common_names = ['张三', '李四', '王五', '天依']
                    for name in common_names:
                        if name in full_text:
                            player_names_found.append(name)
                    
                    print(f"   找到球员: {player_names_found}")
                    fix_results['players'] = player_names_found
                    
                    return fix_results
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return None
                
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def test_with_color_data():
    """使用包含颜色数据的测试"""
    print(f"\n🔍 测试颜色字段修复")
    print("=" * 60)
    
    try:
        import streamlit as st
        
        # 设置session state
        if 'user_id' not in st.session_state:
            st.session_state.user_id = 'user_44ecbeed9db2'
        
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 模拟包含完整信息的团队数据
        team_data = {
            'name': '颜色修复测试队',
            'leader': '测试领队',
            'coach': '测试教练',
            'doctor': '测试队医',
            'contact_person': '测试联系人',
            'contact_phone': '13800138000',
            # 颜色字段
            'jersey_color': '红色',
            'shorts_color': '蓝色',
            'socks_color': '白色',
            'goalkeeper_kit_color': '黄色'
        }
        
        players_data = [
            {
                'name': '测试球员1',
                'jersey_number': '1',
                'photo': 'test1.jpg'
            },
            {
                'name': '测试球员2',
                'jersey_number': '2',
                'photo': 'test2.jpg'
            }
        ]
        
        print(f"📄 测试数据:")
        print(f"   团队名称: '{team_data['name']}'")
        print(f"   领队: '{team_data['leader']}'")
        print(f"   球衣颜色: '{team_data['jersey_color']}'")
        print(f"   球裤颜色: '{team_data['shorts_color']}'")
        
        # 获取配置
        paths = app_settings.word_generator.get_absolute_paths("color_fix_test", app_settings.paths)
        
        # 创建服务
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        # 检查数据准备
        json_data = word_service._prepare_json_data(team_data, players_data)
        team_info = json_data['teamInfo']
        
        print(f"\n📄 修复后的数据准备:")
        print(f"   organizationName: '{team_info.get('organizationName', 'MISSING')}'")
        print(f"   teamLeader: '{team_info.get('teamLeader', 'MISSING')}'")
        print(f"   jerseyColor: '{team_info.get('jerseyColor', 'MISSING')}'")
        print(f"   shortsColor: '{team_info.get('shortsColor', 'MISSING')}'")
        print(f"   socksColor: '{team_info.get('socksColor', 'MISSING')}'")
        print(f"   goalkeeperKitColor: '{team_info.get('goalkeeperKitColor', 'MISSING')}'")
        
        # 生成Word
        print(f"\n🚀 生成Word文档...")
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print(f"✅ 生成成功: {os.path.basename(result['file_path'])}")
            
            # 检查颜色字段
            return check_color_fix_in_word(result['file_path'], team_data)
        else:
            print(f"❌ 生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 颜色测试失败: {e}")
        return False

def check_color_fix_in_word(file_path, team_data):
    """检查Word中的颜色修复"""
    print(f"\n🔍 检查Word中的颜色修复")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    # 检查各种颜色
                    colors = {
                        'jersey_color': team_data.get('jersey_color', ''),
                        'shorts_color': team_data.get('shorts_color', ''),
                        'socks_color': team_data.get('socks_color', ''),
                        'goalkeeper_kit_color': team_data.get('goalkeeper_kit_color', '')
                    }
                    
                    print(f"📄 颜色修复检查:")
                    color_results = {}
                    
                    for color_type, color_value in colors.items():
                        if color_value:
                            has_color = color_value in full_text
                            print(f"   {color_type} '{color_value}': {'✅ 找到' if has_color else '❌ 未找到'}")
                            color_results[color_type] = has_color
                        else:
                            print(f"   {color_type}: ❌ 数据中无此字段")
                            color_results[color_type] = False
                    
                    # 计算颜色修复成功率
                    successful_colors = sum(1 for result in color_results.values() if result)
                    total_colors = len(color_results)
                    success_rate = (successful_colors / total_colors * 100) if total_colors > 0 else 0
                    
                    print(f"\n📊 颜色字段修复成功率: {successful_colors}/{total_colors} ({success_rate:.1f}%)")
                    
                    return color_results
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return {}
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return {}

def main():
    """主函数"""
    print("🎯 测试全面修复效果")
    print("=" * 70)
    print("验证所有修复是否生效")
    print("=" * 70)
    
    # 1. 测试主要修复效果
    main_fix_result = test_comprehensive_fix()
    
    # 2. 测试颜色字段修复
    color_fix_result = test_with_color_data()
    
    # 综合分析
    print(f"\n📊 全面修复测试结果")
    print("=" * 70)
    
    if main_fix_result:
        print(f"🔍 主要修复效果:")
        
        if main_fix_result.get('team_name') == 'fixed':
            print(f"   ✅ 团队名称问题已修复")
        else:
            print(f"   ❌ 团队名称问题仍存在")
        
        if main_fix_result.get('auto_fill') == 'fixed':
            print(f"   ✅ '自动填充'问题已修复")
        else:
            print(f"   ❌ '自动填充'问题仍存在")
        
        if main_fix_result.get('contact_info') == 'working':
            print(f"   ✅ 联系人信息仍正常工作")
        else:
            print(f"   ❌ 联系人信息被破坏")
    
    if color_fix_result:
        successful_colors = sum(1 for result in color_fix_result.values() if result)
        total_colors = len(color_fix_result)
        
        if successful_colors == total_colors:
            print(f"   ✅ 颜色字段修复完全成功")
        elif successful_colors > 0:
            print(f"   ⚠️ 颜色字段部分修复成功 ({successful_colors}/{total_colors})")
        else:
            print(f"   ❌ 颜色字段修复失败")
    
    # 最终结论
    print(f"\n🎯 最终结论:")
    
    if (main_fix_result and 
        main_fix_result.get('team_name') == 'fixed' and 
        main_fix_result.get('auto_fill') == 'fixed' and
        main_fix_result.get('contact_info') == 'working' and
        color_fix_result and
        sum(1 for result in color_fix_result.values() if result) >= 3):
        
        print("🎉 全面修复完全成功！")
        print("✅ 团队名称问题已解决")
        print("✅ '自动填充'问题已解决")
        print("✅ 颜色字段映射已添加")
        print("✅ 联系人信息继续正常工作")
        
        print(f"\n🎯 用户现在可以:")
        print(f"   1. 生成包含正确团队名称的Word报名表")
        print(f"   2. 不再看到'自动填充'占位符")
        print(f"   3. 如果提供颜色数据，可以正确显示颜色信息")
        print(f"   4. 联系人信息继续正常工作")
        
    else:
        print("⚠️ 修复部分成功，仍有问题需要解决")
        print("💡 建议检查具体的失败项目")

if __name__ == "__main__":
    main()
