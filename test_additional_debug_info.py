#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试额外发现的调试信息
Test Additional Debug Information Found
"""

import os
import re

def analyze_additional_debug_info():
    """分析额外发现的调试信息"""
    print("🔍 分析额外发现的调试信息")
    print("=" * 80)
    
    # 从用户截图中发现的新调试信息
    additional_debug_info = [
        {
            "content": "🤖 AI信息收集助手",
            "type": "组件标题",
            "severity": "🟢 正常 - 这是功能标题"
        },
        {
            "content": "🔧 增强AI功能已启用 (文档自动处理和结构化输出)",
            "type": "功能状态提示",
            "severity": "🟡 中等 - 可能是调试信息"
        },
        {
            "content": "🤖 AI球队信息收集助手",
            "type": "组件标题",
            "severity": "🟢 正常 - 这是功能标题"
        },
        {
            "content": "🔍 AI助手当前调试信息:",
            "type": "调试标题",
            "severity": "🔴 高 - 明显的调试信息"
        },
        {
            "content": "当前球队: 003222",
            "type": "调试数据",
            "severity": "🟡 中等 - 调试格式"
        },
        {
            "content": "当前统计: {'total_players': 2, 'players_with_photos': 2, 'completion_rate': 100.0, 'is_complete': True}",
            "type": "详细调试数据",
            "severity": "🔴 高 - 详细内部数据"
        },
        {
            "content": "上次统计: 未设置",
            "type": "调试数据",
            "severity": "🟡 中等 - 调试信息"
        },
        {
            "content": "是否操作完成",
            "type": "调试状态",
            "severity": "🟡 中等 - 调试信息"
        },
        {
            "content": "AI助手存在: False",
            "type": "调试状态",
            "severity": "🟡 中等 - 调试信息"
        },
        {
            "content": "🔍 查看球队队员调试",
            "type": "调试标题",
            "severity": "🔴 高 - 明显的调试信息"
        }
    ]
    
    print("📋 发现的额外调试信息:")
    for i, info in enumerate(additional_debug_info, 1):
        print(f"\n{i}. {info['severity']}")
        print(f"   类型: {info['type']}")
        print(f"   内容: {info['content']}")
    
    return additional_debug_info

def search_ai_component_debug():
    """搜索AI组件的调试信息"""
    print(f"\n🔍 搜索AI组件调试信息")
    print("=" * 80)
    
    # 搜索关键词
    ai_debug_keywords = [
        "AI助手当前调试信息",
        "当前球队:",
        "当前统计:",
        "上次统计:",
        "是否操作完成",
        "AI助手存在:",
        "查看球队队员调试",
        "增强AI功能已启用"
    ]
    
    search_results = {}
    
    # 重点搜索AI相关文件
    ai_files = [
        "streamlit_team_management_modular/components/ai_chat.py",
        "streamlit_team_management_modular/services/enhanced_ai_service.py",
        "streamlit_team_management_modular/app.py",
        "streamlit_team_management_modular/pages/ai_extraction.py"
    ]
    
    # 也搜索整个目录
    for root, dirs, files in os.walk("streamlit_team_management_modular"):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    for keyword in ai_debug_keywords:
                        if keyword in content:
                            if file_path not in search_results:
                                search_results[file_path] = []
                            
                            # 找到包含关键词的行
                            lines = content.split('\n')
                            for i, line in enumerate(lines, 1):
                                if keyword in line:
                                    search_results[file_path].append({
                                        'line': i,
                                        'keyword': keyword,
                                        'content': line.strip()
                                    })
                                    
                except Exception as e:
                    continue
    
    print("📁 找到AI组件调试信息的文件:")
    for file_path, findings in search_results.items():
        print(f"\n📄 {file_path}")
        for finding in findings:
            print(f"   第{finding['line']}行: {finding['content'][:80]}...")
    
    return search_results

def search_specific_ai_debug_patterns():
    """搜索特定的AI调试模式"""
    print(f"\n🎯 搜索特定AI调试模式")
    print("=" * 80)
    
    # 搜索模式
    debug_patterns = [
        r'st\.write.*调试信息',
        r'st\.write.*当前球队',
        r'st\.write.*当前统计',
        r'st\.write.*AI助手',
        r'st\.info.*调试',
        r'st\.success.*已启用',
        r'st\.write.*存在.*False',
        r'st\.write.*完成'
    ]
    
    pattern_results = {}
    
    for root, dirs, files in os.walk("streamlit_team_management_modular"):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    lines = content.split('\n')
                    for i, line in enumerate(lines, 1):
                        for pattern in debug_patterns:
                            if re.search(pattern, line, re.IGNORECASE):
                                if file_path not in pattern_results:
                                    pattern_results[file_path] = []
                                pattern_results[file_path].append({
                                    'line': i,
                                    'pattern': pattern,
                                    'content': line.strip()
                                })
                                
                except Exception as e:
                    continue
    
    print("🎯 找到匹配调试模式的文件:")
    for file_path, findings in pattern_results.items():
        print(f"\n📄 {file_path}")
        for finding in findings:
            print(f"   🔍 第{finding['line']}行: {finding['content'][:80]}...")
    
    return pattern_results

def analyze_ai_chat_component():
    """分析AI聊天组件"""
    print(f"\n🔍 分析AI聊天组件")
    print("=" * 80)
    
    ai_chat_file = "streamlit_team_management_modular/components/ai_chat.py"
    
    if os.path.exists(ai_chat_file):
        try:
            with open(ai_chat_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找调试相关的输出
            lines = content.split('\n')
            debug_lines = []
            
            for i, line in enumerate(lines, 1):
                if any(keyword in line.lower() for keyword in ['调试', 'debug', 'st.write', 'st.info']):
                    debug_lines.append({
                        'line': i,
                        'content': line.strip()
                    })
            
            print(f"📄 AI聊天组件调试信息:")
            if debug_lines:
                for debug_line in debug_lines:
                    print(f"   第{debug_line['line']}行: {debug_line['content'][:80]}...")
            else:
                print(f"   ✅ 未发现明显的调试输出")
            
            return debug_lines
            
        except Exception as e:
            print(f"   ❌ 读取失败: {e}")
            return []
    else:
        print(f"   ❌ 文件不存在: {ai_chat_file}")
        return []

def generate_ai_debug_removal_plan():
    """生成AI调试信息移除计划"""
    print(f"\n📋 生成AI调试信息移除计划")
    print("=" * 80)
    
    removal_plan = {
        "高优先级移除": [
            {
                "target": "🔍 AI助手当前调试信息:",
                "reason": "明显的调试标题，用户不应该看到",
                "action": "删除或注释掉相关st.write语句"
            },
            {
                "target": "当前统计: {'total_players': 2, ...}",
                "reason": "详细的内部数据结构，纯调试用途",
                "action": "删除或用条件判断包装"
            },
            {
                "target": "🔍 查看球队队员调试",
                "reason": "明显的调试标题",
                "action": "删除或注释掉"
            }
        ],
        
        "中优先级移除": [
            {
                "target": "当前球队: xxx",
                "reason": "调试格式的数据显示",
                "action": "删除或改为用户友好格式"
            },
            {
                "target": "上次统计: 未设置",
                "reason": "内部状态信息，用户不需要",
                "action": "删除或移到日志"
            },
            {
                "target": "AI助手存在: False",
                "reason": "内部状态调试信息",
                "action": "删除或移到日志"
            },
            {
                "target": "是否操作完成",
                "reason": "内部状态信息",
                "action": "删除或移到日志"
            }
        ],
        
        "低优先级检查": [
            {
                "target": "🔧 增强AI功能已启用",
                "reason": "可能是功能提示，需要确认是否必要",
                "action": "检查是否为用户需要的功能提示"
            }
        ]
    }
    
    for category, items in removal_plan.items():
        print(f"\n🎯 {category}:")
        for item in items:
            print(f"   📌 目标: {item['target']}")
            print(f"      原因: {item['reason']}")
            print(f"      行动: {item['action']}")
    
    return removal_plan

def main():
    """主函数"""
    print("🔍 额外调试信息分析")
    print("=" * 80)
    
    print("🎯 分析目标:")
    print("   用户发现了更多前端调试信息")
    print("   需要找到这些AI组件相关调试信息的源头")
    
    # 1. 分析额外发现的调试信息
    additional_debug = analyze_additional_debug_info()
    
    # 2. 搜索AI组件调试信息
    ai_search_results = search_ai_component_debug()
    
    # 3. 搜索特定调试模式
    pattern_results = search_specific_ai_debug_patterns()
    
    # 4. 分析AI聊天组件
    ai_chat_debug = analyze_ai_chat_component()
    
    # 5. 生成移除计划
    removal_plan = generate_ai_debug_removal_plan()
    
    # 总结
    print(f"\n🎯 分析总结")
    print("=" * 80)
    
    print("✅ 主要发现:")
    print(f"   🔍 发现 {len(additional_debug)} 个额外调试信息")
    print(f"   📁 涉及 {len(ai_search_results)} 个AI相关文件")
    print(f"   🎯 主要问题：AI组件调试信息泄露到用户界面")
    
    print(f"\n⚠️ 问题严重性:")
    print("   🔴 高：AI助手调试信息和详细统计数据")
    print("   🟡 中：内部状态和调试格式数据")
    print("   🟢 低：部分功能提示可能是正常的")
    
    print(f"\n💡 解决建议:")
    print("   1. 立即移除所有AI助手调试信息")
    print("   2. 删除详细统计数据的调试显示")
    print("   3. 将内部状态信息移到日志")
    print("   4. 检查功能提示是否必要")
    
    print(f"\n🎉 结论:")
    print("   ✅ 成功定位了AI组件调试信息的源头")
    print("   📋 制定了详细的清理计划")
    print("   🎯 需要继续清理AI相关的调试输出")

if __name__ == "__main__":
    main()
