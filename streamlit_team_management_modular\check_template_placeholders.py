#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查模板文件中的占位符
"""

import os
import zipfile
import re
from datetime import datetime

def check_template_placeholders():
    """检查模板文件中的占位符"""
    template_path = "../word_zc/template_15players_fixed.docx"
    
    print(f"🔍 检查模板文件: {template_path}")
    
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        return
    
    try:
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        print(f"📄 模板文件分析:")
        
        # 查找所有占位符
        placeholder_pattern = r'\{\{[^}]+\}\}'
        placeholders = re.findall(placeholder_pattern, content)
        
        print(f"   找到占位符数量: {len(placeholders)}")
        
        # 检查颜色相关占位符
        color_placeholders = [
            "{{jerseyColor}}", "{{shortsColor}}", 
            "{{socksColor}}", "{{goalkeeperKitColor}}"
        ]
        
        print(f"\n🎨 颜色占位符检查:")
        found_color_placeholders = []
        for placeholder in color_placeholders:
            if placeholder in content:
                print(f"   ✅ 找到: {placeholder}")
                found_color_placeholders.append(placeholder)
            else:
                print(f"   ❌ 缺失: {placeholder}")
                # 检查是否被分割
                field_name = placeholder.replace("{{", "").replace("}}", "")
                if field_name in content:
                    print(f"      ⚠️ 字段名存在但可能被XML分割: {field_name}")
                    # 查找分割的模式
                    split_patterns = [
                        f"{{{{.*{field_name}.*}}}}",
                        f"{{.*{field_name}.*}}",
                        field_name
                    ]
                    for pattern in split_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        if matches:
                            print(f"         发现相关内容: {matches[:3]}")  # 只显示前3个
        
        # 检查其他重要占位符
        other_placeholders = [
            "{{contactPerson}}", "{{contactPhone}}", 
            "{{teamLeader}}", "{{coach}}", "{{teamDoctor}}"
        ]
        
        print(f"\n👥 其他占位符检查:")
        for placeholder in other_placeholders:
            if placeholder in content:
                print(f"   ✅ 找到: {placeholder}")
            else:
                print(f"   ❌ 缺失: {placeholder}")
        
        print(f"\n📋 所有找到的占位符:")
        for placeholder in sorted(set(placeholders)):
            print(f"   {placeholder}")
        
        # 检查是否有颜色相关的文本
        color_related_text = ["颜色", "球衣", "球裤", "球袜", "守门员"]
        print(f"\n🔍 颜色相关文本检查:")
        for text in color_related_text:
            if text in content:
                print(f"   ✅ 找到相关文本: '{text}'")
                # 查找包含该文本的上下文
                pattern = f".{{0,50}}{text}.{{0,50}}"
                matches = re.findall(pattern, content)
                if matches:
                    print(f"      上下文: {matches[0][:100]}...")
            else:
                print(f"   ❌ 未找到相关文本: '{text}'")
        
        return {
            "total_placeholders": len(placeholders),
            "color_placeholders_found": len(found_color_placeholders),
            "all_placeholders": placeholders
        }
        
    except Exception as e:
        print(f"❌ 检查模板失败: {e}")
        return None

def compare_templates():
    """比较原始模板和修复后的模板"""
    print(f"\n" + "=" * 60)
    print("🔍 比较原始模板和修复后的模板")
    print("=" * 60)
    
    original_template = "../word_zc/template_15players_fixed.docx"
    fixed_template = "../word_zc/template_15players_fixed.docx"
    
    templates = {
        "原始模板": original_template,
        "修复后模板": fixed_template
    }
    
    for name, path in templates.items():
        print(f"\n📄 {name}: {os.path.basename(path)}")
        
        if not os.path.exists(path):
            print(f"   ❌ 文件不存在")
            continue
        
        try:
            with zipfile.ZipFile(path, 'r') as zip_file:
                with zip_file.open('word/document.xml') as xml_file:
                    content = xml_file.read().decode('utf-8')
            
            # 查找颜色占位符
            color_placeholders = [
                "jerseyColor", "shortsColor", "socksColor", "goalkeeperKitColor"
            ]
            
            for placeholder in color_placeholders:
                full_placeholder = f"{{{{{placeholder}}}}}"
                if full_placeholder in content:
                    print(f"   ✅ {placeholder}: 完整占位符存在")
                elif placeholder in content:
                    print(f"   ⚠️ {placeholder}: 字段名存在但可能被分割")
                else:
                    print(f"   ❌ {placeholder}: 完全不存在")
            
        except Exception as e:
            print(f"   ❌ 读取失败: {e}")

def main():
    """主函数"""
    print("🚀 检查模板文件占位符")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 检查修复后的模板
        result = check_template_placeholders()
        
        # 比较两个模板
        compare_templates()
        
        print("\n" + "=" * 60)
        print("📋 模板检查总结")
        print("=" * 60)
        
        if result:
            print(f"✅ 模板文件检查完成")
            print(f"   总占位符数量: {result['total_placeholders']}")
            print(f"   颜色占位符数量: {result['color_placeholders_found']}/4")
            
            if result['color_placeholders_found'] < 4:
                print(f"\n⚠️ 颜色占位符不完整，这可能是颜色信息无法显示的原因")
                print(f"   建议检查模板文件中的颜色占位符是否被正确修复")
            else:
                print(f"\n✅ 所有颜色占位符都存在")
        else:
            print("❌ 模板文件检查失败")
        
        print(f"\n🎯 下一步建议:")
        print(f"   1. 如果颜色占位符不完整，需要手动修复模板文件")
        print(f"   2. 确保模板中的占位符格式为 {{{{fieldName}}}}")
        print(f"   3. 重新测试Word生成功能")
        
    except Exception as e:
        print(f"❌ 检查过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
