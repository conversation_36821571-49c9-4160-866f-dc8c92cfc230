{"diagnosis_time": "2025-09-01T02:42:40.980085", "test_results": {"1. Java环境检查": {"success": true, "findings": ["Java可用: java", "版本信息: java version \"1.8.0_211\"", "Java classpath支持: 正常"]}, "2. JAR文件检查": {"success": true, "findings": ["找到JAR文件: 4 个", "JAR文件: word_zc/ai-football-generator/target\\ai-football-generator-1.0.0.jar", "  文件大小: 27.9KB", "  文件可读: True", "  修改时间: 2025-08-31 14:35:57", "JAR文件: word_zc/ai-football-generator/target\\word-generator.jar", "  文件大小: 31739.8KB", "  文件可读: True", "  修改时间: 2025-08-31 14:36:03", "JAR文件: word_zc\\ai-football-generator\\target\\ai-football-generator-1.0.0.jar", "  文件大小: 27.9KB", "  文件可读: True", "  修改时间: 2025-08-31 14:35:57", "JAR文件: word_zc\\ai-football-generator\\target\\word-generator.jar", "  文件大小: 31739.8KB", "  文件可读: True", "  修改时间: 2025-08-31 14:36:03"], "jar_files": ["word_zc/ai-football-generator/target\\ai-football-generator-1.0.0.jar", "word_zc/ai-football-generator/target\\word-generator.jar", "word_zc\\ai-football-generator\\target\\ai-football-generator-1.0.0.jar", "word_zc\\ai-football-generator\\target\\word-generator.jar"]}, "3. CommandLineMain类检查": {"success": true, "findings": ["检查JAR文件: ai-football-generator-1.0.0.jar", "  ✅ 包含CommandLineMain.class", "  ✅ 包含FootballReportGenerator.class", "  ✅ 包含WordGeneratorCore.class", "  ✅ 包含JsonDataParser.class", "检查JAR文件: word-generator.jar", "  ✅ 包含CommandLineMain.class", "  ✅ 包含FootballReportGenerator.class", "  ✅ 包含WordGeneratorCore.class", "  ✅ 包含JsonDataParser.class", "检查JAR文件: ai-football-generator-1.0.0.jar", "  ✅ 包含CommandLineMain.class", "  ✅ 包含FootballReportGenerator.class", "  ✅ 包含WordGeneratorCore.class", "  ✅ 包含JsonDataParser.class", "检查JAR文件: word-generator.jar", "  ✅ 包含CommandLineMain.class", "  ✅ 包含FootballReportGenerator.class", "  ✅ 包含WordGeneratorCore.class", "  ✅ 包含JsonDataParser.class"]}, "4. JAR文件内容分析": {"success": true, "findings": ["分析主JAR文件: word-generator.jar", "直接运行JAR返回码: 1", "标准错误: ERROR:Usage: java CommandLineMain <json-file-path>\n", "CommandLineMain调用返回码: 1", "CommandLineMain标准错误: ERROR:Usage: java CommandLineMain <json-file-path>\n", "✅ CommandLineMain类正常响应"], "main_jar": "word_zc/ai-football-generator/target\\word-generator.jar"}, "5. Java命令行测试": {"success": true, "findings": ["创建测试JSON文件: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpwq30ves1.json", "执行命令: java -cp word_zc/ai-football-generator/target\\word-generator.jar CommandLineMain C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpwq30ves1.json", "命令返回码: 1", "标准输出:", "  ? Ŀ¼output", "  ? Ŀ¼photos", "  ? ʼWord...", "标准错误:", "  INFO:Starting Word generation from JSON: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpwq30ves1.json", "  INFO:Team info parsed: Ϣ[=JavaԶӱ, λ=JavaԶ, =, =Խ, ҽ=Զҽ, ϵ=ϵ, ϵ绰=13800138000, ɫ=, ɫ=, ɫ=, Աװɫ=]", "  WARN:Skipping invalid player data: number=, name=Ա1", "  INFO:Total players parsed: 0", "  INFO:Parsed team data - ЧԱ: 0", "  INFO:JSON data parsed successfully", "  INFO:Team: JavaԶ", "  INFO:Players: 0", "  INFO:No config found in JSON, using defaults", "Java执行成功: False"], "java_success": false, "return_code": 1}, "6. 简化Java调用测试": {"success": true, "findings": ["测试1: 无参数调用CommandLineMain", "  返回码: 1", "  ✅ 正确显示使用帮助", "测试2: 错误文件路径调用", "  返回码: 1", "  ✅ 正确处理文件不存在错误"]}, "7. 队徽路径Java处理测试": {"success": true, "findings": ["测试队徽路径: assets/logos/test_logo_java.png", "创建包含队徽路径的JSON文件: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpk7dkeovx.json", "Java执行返回码: 1", "❌ Java程序未处理logoPath字段", "❌ Java程序未识别队徽路径", "Java错误输出（前5行）:", "  INFO:Starting Word generation from JSON: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpk7dkeovx.json", "  INFO:Team info parsed: Ϣ[=ӻ·Զӱ, λ=ӻ·Զ, =, =Խ, ҽ=Զҽ, ϵ=ϵ, ϵ绰=13800138000, ɫ=, ɫ=, ɫ=, Աװɫ=]", "  WARN:Skipping invalid player data: number=, name=Ա1", "  INFO:Total players parsed: 0", "  INFO:Parsed team data - ЧԱ: 0"], "java_return_code": 1}, "8. Java环境问题根因分析": {"success": true, "findings": ["综合分析Java环境诊断结果...", "Java环境: ✅", "JAR文件: ✅", "CommandLineMain类: ✅", "JAR内容分析: ✅", "Java命令行测试: ✅", "简化调用测试: ✅", "队徽处理测试: ✅", "\n🔍 发现的根因:", "  1. Java程序执行失败：可能是数据格式或程序逻辑问题", "\n💡 修复建议:", "  1. 检查传递给Java程序的JSON数据格式是否正确"], "root_causes": ["Java程序执行失败：可能是数据格式或程序逻辑问题"], "suggestions": ["检查传递给Java程序的JSON数据格式是否正确"]}}, "summary": {"total_diagnoses": 8, "successful_diagnoses": 8, "root_causes": ["Java程序执行失败：可能是数据格式或程序逻辑问题"], "suggestions": ["检查传递给Java程序的JSON数据格式是否正确"]}}