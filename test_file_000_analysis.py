#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件000的用途和影响分析
Test File 000 Purpose and Impact Analysis
"""

import os
import subprocess

def analyze_file_000_purpose():
    """分析文件000的用途"""
    print("🔍 分析文件000的用途")
    print("=" * 80)
    
    file_path = "streamlit_team_management_modular/000"
    
    analysis = {
        "文件基本信息": {},
        "内容分析": {},
        "可能用途": [],
        "安全风险": [],
        "影响评估": {}
    }
    
    if os.path.exists(file_path):
        try:
            # 获取文件信息
            file_stat = os.stat(file_path)
            analysis["文件基本信息"] = {
                "文件大小": f"{file_stat.st_size} 字节",
                "创建时间": f"{file_stat.st_ctime}",
                "修改时间": f"{file_stat.st_mtime}",
                "文件权限": oct(file_stat.st_mode)[-3:]
            }
            
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            analysis["内容分析"] = {
                "完整内容": content,
                "内容长度": len(content),
                "是否为命令": content.startswith("streamlit"),
                "命令类型": "Streamlit启动命令" if "streamlit run" in content else "其他"
            }
            
            # 分析可能用途
            if content == "streamlit run app.py":
                analysis["可能用途"] = [
                    "开发时的快速启动脚本",
                    "临时保存的启动命令",
                    "测试或调试时创建的文件",
                    "意外创建的临时文件",
                    "某个脚本的输出文件"
                ]
            
            # 安全风险评估
            analysis["安全风险"] = [
                "暴露了应用的启动方式",
                "显示了技术实现细节",
                "可能被恶意用户利用",
                "降低了系统的专业性",
                "不符合生产环境标准"
            ]
            
            # 影响评估
            analysis["影响评估"] = {
                "用户体验": "用户可能看到技术性文件，感到困惑",
                "安全性": "中等风险，暴露了内部命令",
                "专业性": "降低系统的专业形象",
                "维护性": "增加了不必要的文件管理负担"
            }
            
        except Exception as e:
            analysis["错误"] = f"分析失败: {e}"
    else:
        analysis["状态"] = "文件不存在"
    
    # 输出分析结果
    for category, details in analysis.items():
        print(f"\n📋 {category}:")
        if isinstance(details, dict):
            for key, value in details.items():
                print(f"   {key}: {value}")
        elif isinstance(details, list):
            for item in details:
                print(f"   • {item}")
        else:
            print(f"   {details}")
    
    return analysis

def check_similar_problematic_files():
    """检查类似的问题文件"""
    print(f"\n🔍 检查类似的问题文件")
    print("=" * 80)
    
    base_dir = "streamlit_team_management_modular"
    problematic_patterns = {
        "数字文件名": [],
        "临时文件": [],
        "命令文件": [],
        "隐藏文件": [],
        "备份文件": []
    }
    
    if os.path.exists(base_dir):
        for root, dirs, files in os.walk(base_dir):
            for file in files:
                file_path = os.path.join(root, file)
                
                # 检查数字文件名
                if file.isdigit():
                    problematic_patterns["数字文件名"].append(file_path)
                
                # 检查临时文件
                if file.endswith(('.tmp', '.temp', '~')) or file.startswith('temp'):
                    problematic_patterns["临时文件"].append(file_path)
                
                # 检查命令文件
                if file.endswith(('.bat', '.sh', '.cmd')):
                    problematic_patterns["命令文件"].append(file_path)
                
                # 检查隐藏文件
                if file.startswith('.') and file not in ['.env.example', '.gitignore']:
                    problematic_patterns["隐藏文件"].append(file_path)
                
                # 检查备份文件
                if file.endswith(('.bak', '.backup', '.old')):
                    problematic_patterns["备份文件"].append(file_path)
    
    print("📋 发现的问题文件:")
    total_issues = 0
    for pattern_name, files in problematic_patterns.items():
        if files:
            print(f"\n🎯 {pattern_name} ({len(files)} 个):")
            for file_path in files[:5]:  # 只显示前5个
                print(f"   📄 {file_path}")
                total_issues += 1
            if len(files) > 5:
                print(f"   ... 还有 {len(files)-5} 个文件")
    
    if total_issues == 0:
        print("✅ 未发现其他明显的问题文件")
    
    return problematic_patterns

def analyze_file_creation_source():
    """分析文件000的创建来源"""
    print(f"\n🔍 分析文件000的创建来源")
    print("=" * 80)
    
    possible_sources = {
        "开发工具": [
            "IDE或编辑器的临时文件",
            "调试工具的输出文件",
            "代码生成工具的产物",
            "自动化脚本的副产品"
        ],
        
        "用户操作": [
            "手动创建的启动脚本",
            "复制粘贴操作的结果",
            "重定向输出的意外产物",
            "测试命令时的临时保存"
        ],
        
        "系统行为": [
            "某个脚本的输出重定向",
            "错误的文件命名操作",
            "自动化部署的残留",
            "版本控制的意外提交"
        ],
        
        "代码问题": [
            "程序中的文件创建bug",
            "错误的文件路径配置",
            "调试代码未清理",
            "测试代码的副作用"
        ]
    }
    
    print("📋 可能的创建来源:")
    for category, sources in possible_sources.items():
        print(f"\n🎯 {category}:")
        for source in sources:
            print(f"   • {source}")
    
    # 检查代码中是否有创建此文件的逻辑
    print(f"\n🔍 检查代码中的文件创建逻辑:")
    
    code_files = []
    for root, dirs, files in os.walk("streamlit_team_management_modular"):
        for file in files:
            if file.endswith('.py'):
                code_files.append(os.path.join(root, file))
    
    file_creation_patterns = []
    for code_file in code_files:
        try:
            with open(code_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找可能创建文件000的代码
            if '000' in content or 'open(' in content:
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if '000' in line or ('open(' in line and 'w' in line):
                        file_creation_patterns.append({
                            'file': code_file,
                            'line': i,
                            'content': line.strip()
                        })
        except Exception:
            continue
    
    if file_creation_patterns:
        print("📋 发现可能的文件创建代码:")
        for pattern in file_creation_patterns[:10]:  # 只显示前10个
            print(f"   📄 {pattern['file']} 第{pattern['line']}行:")
            print(f"      {pattern['content']}")
    else:
        print("✅ 未在代码中发现明显的文件000创建逻辑")
    
    return possible_sources, file_creation_patterns

def assess_security_impact():
    """评估安全影响"""
    print(f"\n🔒 评估安全影响")
    print("=" * 80)
    
    security_assessment = {
        "风险等级": "中等",
        "风险类型": [
            "信息泄露风险",
            "技术细节暴露",
            "专业形象损害",
            "潜在的攻击向量"
        ],
        
        "具体风险": {
            "信息泄露": [
                "暴露了应用使用Streamlit框架",
                "显示了主入口文件名app.py",
                "揭示了项目的技术栈",
                "可能暴露目录结构"
            ],
            
            "攻击风险": [
                "攻击者了解启动方式",
                "可能尝试直接访问app.py",
                "了解技术栈后针对性攻击",
                "社会工程学攻击的信息来源"
            ],
            
            "业务风险": [
                "降低用户对系统的信任",
                "影响专业形象",
                "可能违反安全规范",
                "增加维护复杂度"
            ]
        },
        
        "缓解措施": [
            "立即删除文件000",
            "检查并清理其他类似文件",
            "建立文件管理规范",
            "添加.gitignore规则防止再次出现",
            "定期进行安全审查"
        ]
    }
    
    print(f"🔴 风险等级: {security_assessment['风险等级']}")
    
    for category, details in security_assessment.items():
        if category != "风险等级":
            print(f"\n🔒 {category}:")
            if isinstance(details, dict):
                for subcat, items in details.items():
                    print(f"   {subcat}:")
                    for item in items:
                        print(f"      • {item}")
            else:
                for item in details:
                    print(f"   • {item}")
    
    return security_assessment

def generate_cleanup_recommendations():
    """生成清理建议"""
    print(f"\n💡 生成清理建议")
    print("=" * 80)
    
    recommendations = {
        "立即行动": [
            {
                "行动": "删除文件000",
                "命令": "rm streamlit_team_management_modular/000",
                "原因": "消除安全风险和专业形象问题",
                "优先级": "🔴 高"
            }
        ],
        
        "预防措施": [
            {
                "措施": "添加.gitignore规则",
                "内容": "添加 '000' 和数字文件名模式",
                "目的": "防止类似文件被提交到版本控制"
            },
            {
                "措施": "建立文件命名规范",
                "内容": "禁止使用纯数字作为文件名",
                "目的": "避免创建令人困惑的文件"
            },
            {
                "措施": "定期清理检查",
                "内容": "定期扫描和清理临时文件",
                "目的": "保持项目目录的整洁"
            }
        ],
        
        "长期改进": [
            {
                "改进": "自动化清理脚本",
                "描述": "创建脚本自动清理临时和问题文件",
                "好处": "减少手动维护工作"
            },
            {
                "改进": "CI/CD检查",
                "描述": "在持续集成中添加文件规范检查",
                "好处": "防止问题文件进入生产环境"
            }
        ]
    }
    
    for category, items in recommendations.items():
        print(f"\n💡 {category}:")
        for item in items:
            if "行动" in item:
                print(f"   {item['优先级']} {item['行动']}")
                print(f"      命令: {item['命令']}")
                print(f"      原因: {item['原因']}")
            elif "措施" in item:
                print(f"   📌 {item['措施']}")
                print(f"      内容: {item['内容']}")
                print(f"      目的: {item['目的']}")
            else:
                print(f"   🚀 {item['改进']}")
                print(f"      描述: {item['描述']}")
                print(f"      好处: {item['好处']}")
    
    return recommendations

def main():
    """主函数"""
    print("🔍 文件000用途和影响全面分析")
    print("=" * 80)
    
    print("🎯 分析目标:")
    print("   确定文件000的具体用途")
    print("   评估其安全风险和影响")
    print("   提供清理和预防建议")
    
    # 1. 分析文件000的用途
    file_analysis = analyze_file_000_purpose()
    
    # 2. 检查类似问题文件
    similar_files = check_similar_problematic_files()
    
    # 3. 分析文件创建来源
    sources, creation_patterns = analyze_file_creation_source()
    
    # 4. 评估安全影响
    security_impact = assess_security_impact()
    
    # 5. 生成清理建议
    recommendations = generate_cleanup_recommendations()
    
    # 总结
    print(f"\n🎯 分析总结")
    print("=" * 80)
    
    print("✅ 主要发现:")
    print("   📄 文件000包含'streamlit run app.py'启动命令")
    print("   🔴 这是一个不应该存在的临时文件")
    print("   ⚠️ 暴露了技术实现细节，存在安全风险")
    print("   💼 影响了系统的专业形象")
    
    print(f"\n🔴 风险评估:")
    print("   🔒 安全风险: 中等（信息泄露）")
    print("   💼 专业性: 降低用户信任")
    print("   🎯 用户体验: 可能引起困惑")
    print("   📊 维护性: 增加不必要负担")
    
    print(f"\n💡 建议行动:")
    print("   🗑️ 立即删除文件000")
    print("   📋 建立文件命名规范")
    print("   🛡️ 添加预防措施")
    print("   🔍 定期进行清理检查")
    
    print(f"\n🎊 结论:")
    print("   文件000是一个意外创建的临时文件")
    print("   包含启动命令，不应该暴露给用户")
    print("   建议立即删除并建立预防机制")

if __name__ == "__main__":
    main()
