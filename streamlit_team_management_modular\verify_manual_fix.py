#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证手动修复的模板效果
"""

import zipfile
import xml.etree.ElementTree as ET
import json
import subprocess
import os

def verify_template_placeholders():
    """验证模板中的占位符"""
    print("🔍 验证模板占位符")
    print("=" * 50)
    
    template_path = "../word_zc/template_15players_fixed.docx"
    
    try:
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 检查占位符
                has_contact_person = '{{contactPerson}}' in content
                has_contact_phone = '{{contactPhone}}' in content
                
                print(f"📄 占位符检查:")
                print(f"   {{{{contactPerson}}}}: {'✅ 找到' if has_contact_person else '❌ 未找到'}")
                print(f"   {{{{contactPhone}}}}: {'✅ 找到' if has_contact_phone else '❌ 未找到'}")
                
                # 检查是否还有分割的占位符
                has_split = 'contactPerson</w:t></w:r>' in content or 'contactPhone</w:t></w:r>' in content
                print(f"   分割的占位符: {'⚠️ 仍存在' if has_split else '✅ 已修复'}")
                
                if has_contact_person and has_contact_phone and not has_split:
                    print("🎉 模板占位符格式正确！")
                    return True
                else:
                    print("❌ 模板占位符需要修复")
                    return False
                    
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def test_template_with_contact_data():
    """测试模板生成效果"""
    print("\n🧪 测试模板生成效果")
    print("=" * 50)
    
    # 创建测试数据
    test_data = {
        "teamInfo": {
            "title": "手动修复验证报名表",
            "organizationName": "手动修复验证队",
            "teamLeader": "张三",
            "coach": "李四",
            "teamDoctor": "王五",
            "contactPerson": "赵六",
            "contactPhone": "13800138000"
        },
        "players": [
            {
                "number": "10",
                "name": "测试球员",
                "photoPath": "java_word_photos/player1.png"
            }
        ],
        "config": {
            "templatePath": "template_15players_fixed.docx",
            "outputDir": "output",
            "photosDir": "java_word_photos"
        }
    }
    
    try:
        # Java工作目录
        java_dir = "../word_zc/ai-football-generator"
        
        # 写入测试文件
        test_file = os.path.join(java_dir, "verify_manual_fix.json")
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建测试文件")
        print(f"📄 测试数据:")
        print(f"   contactPerson: '{test_data['teamInfo']['contactPerson']}'")
        print(f"   contactPhone: '{test_data['teamInfo']['contactPhone']}'")
        
        # 运行Java程序
        print(f"\n🚀 运行Java程序...")
        result = subprocess.run(
            ["java", "-cp", "target/word-generator.jar", "CommandLineMain", "verify_manual_fix.json"],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=30,
            errors='ignore',
            cwd=java_dir
        )
        
        print(f"📊 返回码: {result.returncode}")
        
        if result.stderr:
            for line in result.stderr.split('\n'):
                if 'INFO:Team info parsed:' in line:
                    print(f"🔍 团队信息: {line}")
        
        if result.returncode == 0:
            print("✅ Java程序运行成功")
            
            # 检查生成的文件
            output_dir = os.path.join(java_dir, "output")
            if os.path.exists(output_dir):
                files = [f for f in os.listdir(output_dir) if f.endswith('.docx')]
                if files:
                    latest_file = max([os.path.join(output_dir, f) for f in files], 
                                    key=os.path.getmtime)
                    print(f"📄 生成文件: {latest_file}")
                    
                    # 检查生成文件内容
                    return check_generated_content(latest_file)
        else:
            print(f"❌ Java程序运行失败")
            if result.stderr:
                print("错误信息:")
                print(result.stderr[:300])
        
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        # 清理测试文件
        test_file = os.path.join(java_dir, "verify_manual_fix.json")
        if os.path.exists(test_file):
            os.remove(test_file)

def check_generated_content(file_path):
    """检查生成文件内容"""
    print(f"\n🔍 检查生成文件内容")
    print("=" * 50)
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    # 检查联系人信息
                    has_contact_person = "赵六" in full_text
                    has_contact_phone = "13800138000" in full_text
                    has_placeholder_person = "{{contactPerson}}" in content
                    has_placeholder_phone = "{{contactPhone}}" in content
                    
                    print(f"📄 内容检查:")
                    print(f"   联系人'赵六': {'✅ 找到' if has_contact_person else '❌ 未找到'}")
                    print(f"   电话'13800138000': {'✅ 找到' if has_contact_phone else '❌ 未找到'}")
                    print(f"   占位符仍存在: {'⚠️ 是' if has_placeholder_person or has_placeholder_phone else '✅ 否'}")
                    
                    # 显示联系人相关内容
                    if "联系人" in full_text:
                        words = full_text.split()
                        for i, word in enumerate(words):
                            if "联系人" in word:
                                start = max(0, i-3)
                                end = min(len(words), i+8)
                                context = ' '.join(words[start:end])
                                print(f"   实际内容: {context}")
                                break
                    
                    if has_contact_person and has_contact_phone:
                        print("🎉 手动修复成功！联系人信息正确显示")
                        return True
                    else:
                        print("❌ 手动修复未完全成功")
                        return False
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return False
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 验证手动修复效果")
    print("=" * 60)
    
    # 1. 验证模板占位符
    template_ok = verify_template_placeholders()
    
    # 2. 测试生成效果
    if template_ok:
        generation_ok = test_template_with_contact_data()
        
        print(f"\n📊 验证结果")
        print("=" * 60)
        
        if generation_ok:
            print("🎉 手动修复完全成功！")
            print("✅ 模板占位符格式正确")
            print("✅ 联系人信息能够正确显示")
            print("✅ 可以正常使用联系人功能")
        else:
            print("⚠️ 手动修复部分成功")
            print("✅ 模板占位符格式正确")
            print("❌ 生成效果可能还有问题")
    else:
        print(f"\n📊 验证结果")
        print("=" * 60)
        print("❌ 模板占位符格式仍有问题")
        print("💡 请按照指南重新修复模板")
        
        print(f"\n💡 修复建议:")
        print(f"1. 打开 word_zc/template_15players_fixed.docx")
        print(f"2. 搜索 '联系人'")
        print(f"3. 确保格式为: 球队联系人：{{{{contactPerson}}}} 电话：{{{{contactPhone}}}}")
        print(f"4. 一次性输入完整文本，不要分段")
        print(f"5. 保存文件")

if __name__ == "__main__":
    main()
