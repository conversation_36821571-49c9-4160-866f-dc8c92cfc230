#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终修复效果
"""

import zipfile
import xml.etree.ElementTree as ET
import os

def test_final_fix():
    """测试最终修复效果"""
    print("🔍 测试最终修复效果")
    print("=" * 60)
    
    try:
        import streamlit as st
        
        # 设置session state
        if 'user_id' not in st.session_state:
            st.session_state.user_id = 'user_e358df6703a9'
        
        from services.fashion_workflow_service import FashionWorkflowService
        
        # 创建工作流服务
        workflow_service = FashionWorkflowService('user_e358df6703a9')
        
        # 测试修复后的_auto_generate_word_document方法
        print("📄 测试修复后的_auto_generate_word_document方法")
        
        # 调用修复后的方法
        word_result = workflow_service._auto_generate_word_document(
            "新建项目201", {}, None
        )
        
        print(f"   Word生成结果: {word_result}")
        
        if word_result.get('success'):
            print("✅ Word生成成功")
            
            # 检查生成的文件内容
            file_path = word_result.get('file_path')
            if file_path and os.path.exists(file_path):
                return check_word_content(file_path)
            else:
                print("❌ 生成的文件不存在")
                return False
        else:
            print(f"❌ Word生成失败: {word_result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_word_content(file_path):
    """检查Word内容"""
    print(f"\n🔍 检查Word内容: {os.path.basename(file_path)}")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    # 检查联系人信息
                    has_contact_person = "赵六" in full_text
                    has_contact_phone = "18454432036" in full_text
                    
                    print(f"📄 Word内容检查:")
                    print(f"   联系人'赵六': {'✅ 找到' if has_contact_person else '❌ 未找到'}")
                    print(f"   电话'18454432036': {'✅ 找到' if has_contact_phone else '❌ 未找到'}")
                    
                    # 显示联系人上下文
                    if has_contact_person or has_contact_phone:
                        words = full_text.split()
                        for i, word in enumerate(words):
                            if "联系人" in word or "赵六" in word:
                                start = max(0, i-3)
                                end = min(len(words), i+15)
                                context = ' '.join(words[start:end])
                                print(f"   联系人上下文: {context}")
                                break
                    else:
                        # 显示联系人区域的内容
                        words = full_text.split()
                        for i, word in enumerate(words):
                            if "联系人" in word:
                                start = max(0, i-3)
                                end = min(len(words), i+15)
                                context = ' '.join(words[start:end])
                                print(f"   联系人区域: {context}")
                                break
                    
                    # 最终结论
                    print(f"\n🎯 检查结果:")
                    
                    if has_contact_person and has_contact_phone:
                        print("🎉 联系人信息修复完全成功！")
                        print("✅ Word文档中正确显示联系人信息")
                        print("✅ _auto_generate_word_document方法修复生效")
                        print("✅ AI数据成功合并到团队数据中")
                        
                        print(f"\n📋 显示内容:")
                        print(f"   联系人: 赵六")
                        print(f"   电话: 18454432036")
                        
                        print(f"\n🎉 恭喜！联系人信息问题已完全解决！")
                        return True
                        
                    else:
                        print("❌ 联系人信息仍然缺失")
                        print("💡 需要进一步调试AI数据合并逻辑")
                        return False
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return False
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 测试最终修复效果")
    print("=" * 70)
    print("测试修复后的_auto_generate_word_document方法")
    print("=" * 70)
    
    result = test_final_fix()
    
    print(f"\n📊 最终测试结果")
    print("=" * 70)
    
    if result:
        print("🎉 最终修复完全成功！")
        print("✅ _auto_generate_word_document方法已修复")
        print("✅ AI数据成功合并到团队数据中")
        print("✅ Word文档正确显示联系人信息")
        print("✅ 联系人信息自动化功能完全正常")
        
        print(f"\n🎯 用户现在可以:")
        print(f"   1. 在AI聊天中输入联系人信息")
        print(f"   2. 系统自动提取并保存")
        print(f"   3. 在换装工作流中生成Word报名表")
        print(f"   4. Word文档正确显示联系人信息")
        
        print(f"\n💡 建议用户:")
        print(f"   1. 重新运行Streamlit应用")
        print(f"   2. 重新进行换装工作流")
        print(f"   3. 检查生成的Word报名表")
        
    else:
        print("❌ 修复仍有问题")
        print("💡 需要进一步调试")

if __name__ == "__main__":
    main()
