#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整工作流程测试 - 队徽到Word集成
Complete Workflow Test - Logo to Word Integration

专门测试队徽生成后在Word文档中的完整集成流程
"""

import os
import sys
import json
import time
from datetime import datetime
from typing import Dict, Any

# 添加项目路径
sys.path.append('streamlit_team_management_modular')

class CompleteWorkflowTester:
    """完整工作流程测试器"""
    
    def __init__(self):
        self.test_team_name = "完整测试队"
        self.test_results = {}
        
    def run_complete_workflow_test(self):
        """运行完整工作流程测试"""
        
        print("🔄 完整工作流程测试 - 队徽到Word集成")
        print("=" * 80)
        print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 测试球队: {self.test_team_name}")
        print()
        
        # 测试步骤
        test_steps = [
            ("1. 工作流程服务初始化", self.test_workflow_service_init),
            ("2. 队徽生成测试", self.test_logo_generation_step),
            ("3. 模拟球员数据准备", self.test_player_data_preparation),
            ("4. 模拟换装结果创建", self.test_fashion_result_simulation),
            ("5. 球员图片映射创建", self.test_player_photo_mapping),
            ("6. Word文档生成测试", self.test_word_document_generation),
            ("7. 队徽在Word中的验证", self.test_logo_in_word_verification),
            ("8. 完整流程集成验证", self.test_complete_integration)
        ]
        
        for step_name, test_func in test_steps:
            print(f"\n{step_name}")
            print("-" * 60)
            try:
                result = test_func()
                self.test_results[step_name] = result
                if result.get('success'):
                    print(f"✅ {step_name} - 通过")
                    if result.get('details'):
                        print(f"   📋 详情: {result['details']}")
                else:
                    print(f"❌ {step_name} - 失败: {result.get('error', '未知错误')}")
            except Exception as e:
                print(f"❌ {step_name} - 异常: {e}")
                self.test_results[step_name] = {'success': False, 'error': str(e)}
                import traceback
                traceback.print_exc()
        
        # 生成最终报告
        self.generate_final_report()

    def test_workflow_service_init(self):
        """测试工作流程服务初始化"""
        
        try:
            from services.fashion_workflow_service import FashionWorkflowService
            
            # 创建工作流程服务实例
            self.workflow_service = FashionWorkflowService()
            
            # 检查关键属性
            has_user_id = hasattr(self.workflow_service, 'user_id')
            has_team_service = hasattr(self.workflow_service, 'team_service')
            has_ai_service = hasattr(self.workflow_service, 'ai_service')
            
            return {
                'success': True,
                'details': f"用户ID: {getattr(self.workflow_service, 'user_id', 'N/A')}",
                'has_user_id': has_user_id,
                'has_team_service': has_team_service,
                'has_ai_service': has_ai_service
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_logo_generation_step(self):
        """测试队徽生成步骤"""
        
        try:
            # 调用队徽生成方法
            logo_path = self.workflow_service._auto_generate_team_logo(self.test_team_name)
            
            if logo_path and os.path.exists(logo_path):
                # 分析队徽文件
                stat = os.stat(logo_path)
                
                return {
                    'success': True,
                    'logo_path': logo_path,
                    'file_size': stat.st_size,
                    'details': f"队徽文件: {os.path.basename(logo_path)} ({stat.st_size/1024:.1f}KB)"
                }
            else:
                return {
                    'success': False,
                    'error': f"队徽生成失败或文件不存在: {logo_path}"
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_player_data_preparation(self):
        """测试球员数据准备"""
        
        try:
            # 创建模拟球员数据
            self.mock_players = [
                {
                    'id': f'player_{i}',
                    'name': f'测试球员{i}',
                    'jersey_number': str(i),
                    'photo': f'mock_photo_{i}.jpg'
                }
                for i in range(1, 6)
            ]
            
            # 模拟球队数据
            self.mock_team_data = {
                'name': self.test_team_name,
                'players': self.mock_players,
                'leader': '测试队长',
                'coach': '测试教练',
                'contact_person': '测试联系人',
                'contact_phone': '13800138000'
            }
            
            return {
                'success': True,
                'player_count': len(self.mock_players),
                'details': f"准备了 {len(self.mock_players)} 名球员的数据"
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_fashion_result_simulation(self):
        """测试换装结果模拟"""
        
        try:
            # 模拟换装成功的结果
            self.mock_fashion_result = {
                'success': True,
                'successful_count': len(self.mock_players),
                'results': [
                    {
                        'player_id': player['id'],
                        'success': True,
                        'output_path': f'temp_files/fashion_result_{player["id"]}.jpg'
                    }
                    for player in self.mock_players
                ]
            }
            
            return {
                'success': True,
                'successful_count': self.mock_fashion_result['successful_count'],
                'details': f"模拟了 {self.mock_fashion_result['successful_count']} 个成功的换装结果"
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_player_photo_mapping(self):
        """测试球员图片映射创建"""
        
        try:
            # 创建球员图片映射
            self.player_photo_mapping = {}
            
            for player in self.mock_players:
                player_id = player['id']
                # 模拟换装后的图片路径
                fashion_photo_path = f'temp_files/fashion_result_{player_id}.jpg'
                self.player_photo_mapping[player_id] = fashion_photo_path
            
            return {
                'success': True,
                'mapping_count': len(self.player_photo_mapping),
                'details': f"创建了 {len(self.player_photo_mapping)} 个球员的图片映射",
                'mapping': self.player_photo_mapping
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_word_document_generation(self):
        """测试Word文档生成"""
        
        try:
            # 获取队徽路径
            logo_path = self.test_results.get("2. 队徽生成测试", {}).get('logo_path')
            
            if not logo_path:
                return {
                    'success': False,
                    'error': '队徽路径不可用，无法测试Word生成'
                }
            
            print(f"📄 使用队徽路径: {logo_path}")
            print(f"📋 使用球员映射: {len(self.player_photo_mapping)} 个球员")
            
            # 调用Word文档生成方法
            word_result = self.workflow_service._auto_generate_word_document(
                self.test_team_name,
                self.player_photo_mapping,
                logo_path
            )
            
            if word_result.get('success'):
                word_file_path = word_result.get('file_path')
                
                # 检查Word文件是否存在
                if word_file_path and os.path.exists(word_file_path):
                    stat = os.stat(word_file_path)
                    
                    return {
                        'success': True,
                        'word_file_path': word_file_path,
                        'file_size': stat.st_size,
                        'details': f"Word文件: {os.path.basename(word_file_path)} ({stat.st_size/1024:.1f}KB)",
                        'word_result': word_result
                    }
                else:
                    return {
                        'success': False,
                        'error': f"Word文件生成成功但文件不存在: {word_file_path}",
                        'word_result': word_result
                    }
            else:
                return {
                    'success': False,
                    'error': f"Word生成失败: {word_result.get('error', '未知错误')}",
                    'word_result': word_result
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_logo_in_word_verification(self):
        """测试队徽在Word中的验证"""
        
        try:
            word_test_result = self.test_results.get("6. Word文档生成测试", {})
            
            if not word_test_result.get('success'):
                return {
                    'success': False,
                    'error': 'Word文档生成失败，无法验证队徽'
                }
            
            word_file_path = word_test_result.get('word_file_path')
            logo_path = self.test_results.get("2. 队徽生成测试", {}).get('logo_path')
            
            # 检查文件是否存在
            word_exists = os.path.exists(word_file_path) if word_file_path else False
            logo_exists = os.path.exists(logo_path) if logo_path else False
            
            # 尝试分析Word文档内容（基本检查）
            word_analysis = {}
            if word_exists:
                try:
                    # 这里可以添加更详细的Word文档分析
                    # 但为了不修改主代码，我们只做基本检查
                    stat = os.stat(word_file_path)
                    word_analysis = {
                        'file_accessible': True,
                        'file_size': stat.st_size,
                        'modified_time': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                    }
                except Exception as e:
                    word_analysis = {'analysis_error': str(e)}
            
            return {
                'success': word_exists and logo_exists,
                'word_file_exists': word_exists,
                'logo_file_exists': logo_exists,
                'word_file_path': word_file_path,
                'logo_file_path': logo_path,
                'word_analysis': word_analysis,
                'details': f"Word文件存在: {word_exists}, 队徽文件存在: {logo_exists}"
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_complete_integration(self):
        """测试完整集成"""
        
        try:
            # 统计所有测试结果
            total_tests = len(self.test_results)
            successful_tests = sum(1 for result in self.test_results.values() if result.get('success'))
            
            # 检查关键步骤
            logo_generated = self.test_results.get("2. 队徽生成测试", {}).get('success', False)
            word_generated = self.test_results.get("6. Word文档生成测试", {}).get('success', False)
            logo_verified = self.test_results.get("7. 队徽在Word中的验证", {}).get('success', False)
            
            # 分析问题
            issues = []
            if not logo_generated:
                issues.append("队徽生成失败")
            if not word_generated:
                issues.append("Word文档生成失败")
            if not logo_verified:
                issues.append("队徽在Word中的验证失败")
            
            integration_success = logo_generated and word_generated
            
            return {
                'success': integration_success,
                'total_tests': total_tests,
                'successful_tests': successful_tests,
                'success_rate': successful_tests / total_tests * 100,
                'logo_generated': logo_generated,
                'word_generated': word_generated,
                'logo_verified': logo_verified,
                'issues': issues,
                'details': f"集成测试 {'成功' if integration_success else '失败'}: {successful_tests}/{total_tests} 步骤通过"
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def generate_final_report(self):
        """生成最终报告"""
        
        print(f"\n📋 完整工作流程测试报告")
        print("=" * 80)
        
        # 获取集成测试结果
        integration_result = self.test_results.get("8. 完整流程集成验证", {})
        
        if integration_result.get('success'):
            print("🎉 完整工作流程测试成功！")
            print(f"✅ 队徽生成: {integration_result.get('logo_generated', False)}")
            print(f"✅ Word生成: {integration_result.get('word_generated', False)}")
            print(f"📊 成功率: {integration_result.get('success_rate', 0):.1f}%")
        else:
            print("❌ 完整工作流程测试失败")
            issues = integration_result.get('issues', [])
            if issues:
                print("🔍 发现的问题:")
                for issue in issues:
                    print(f"   - {issue}")
        
        # 关键发现
        print(f"\n🔍 关键发现:")
        
        # 队徽生成结果
        logo_test = self.test_results.get("2. 队徽生成测试", {})
        if logo_test.get('success'):
            logo_path = logo_test.get('logo_path')
            file_size = logo_test.get('file_size', 0)
            print(f"   🎨 队徽生成成功: {os.path.basename(logo_path)} ({file_size/1024:.1f}KB)")
        else:
            print(f"   ❌ 队徽生成失败: {logo_test.get('error')}")
        
        # Word生成结果
        word_test = self.test_results.get("6. Word文档生成测试", {})
        if word_test.get('success'):
            word_path = word_test.get('word_file_path')
            file_size = word_test.get('file_size', 0)
            print(f"   📄 Word生成成功: {os.path.basename(word_path)} ({file_size/1024:.1f}KB)")
        else:
            print(f"   ❌ Word生成失败: {word_test.get('error')}")
        
        # 队徽验证结果
        verification_test = self.test_results.get("7. 队徽在Word中的验证", {})
        if verification_test.get('success'):
            print(f"   ✅ 队徽在Word中验证成功")
        else:
            print(f"   ❌ 队徽在Word中验证失败: {verification_test.get('error')}")
        
        print(f"\n⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 保存详细报告
        self.save_detailed_report()

    def save_detailed_report(self):
        """保存详细报告"""
        
        report_data = {
            'test_time': datetime.now().isoformat(),
            'test_team_name': self.test_team_name,
            'test_results': self.test_results,
            'summary': {
                'total_tests': len(self.test_results),
                'successful_tests': sum(1 for result in self.test_results.values() if result.get('success')),
                'logo_to_word_integration': self.test_results.get("8. 完整流程集成验证", {}).get('success', False)
            }
        }
        
        report_file = f"complete_workflow_test_report_{int(time.time())}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"📄 详细报告已保存: {report_file}")

if __name__ == "__main__":
    tester = CompleteWorkflowTester()
    tester.run_complete_workflow_test()
