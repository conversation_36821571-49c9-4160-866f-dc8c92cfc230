#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实工作流程队徽测试
Real Workflow Logo Test

通过真实的工作流程测试队徽是否能正确插入到Word中
"""

import os
import sys
import time
from datetime import datetime

# 添加项目路径
sys.path.append('streamlit_team_management_modular')

def test_real_workflow_with_logo():
    """通过真实工作流程测试队徽"""
    
    print("🚀 真实工作流程队徽测试")
    print("=" * 80)
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # 步骤1: 创建工作流程服务
        print("步骤1: 创建工作流程服务")
        print("-" * 40)
        
        from services.fashion_workflow_service import FashionWorkflowService
        workflow_service = FashionWorkflowService()
        
        print(f"✅ 工作流程服务创建成功")
        print(f"   用户ID: {workflow_service.user_id}")
        
        # 步骤2: 创建测试球队
        print(f"\n步骤2: 创建测试球队")
        print("-" * 40)
        
        test_team_name = "真实工作流程测试队"
        
        # 创建球队基本信息
        team_info = {
            'name': test_team_name,
            'leader': '测试领队',
            'coach': '测试教练',
            'team_doctor': '测试队医',
            'contact_person': '测试联系人',
            'contact_phone': '13800138000'
        }
        
        # 保存球队信息
        workflow_service.team_service.save_team_data_for_user(
            workflow_service.user_id, 
            test_team_name, 
            team_info
        )
        
        print(f"✅ 球队信息保存成功: {test_team_name}")
        
        # 步骤3: 添加测试球员
        print(f"\n步骤3: 添加测试球员")
        print("-" * 40)
        
        # 添加球员
        players = [
            {
                'name': '测试球员1',
                'jersey_number': '1',
                'position': '前锋'
            },
            {
                'name': '测试球员2', 
                'jersey_number': '2',
                'position': '中场'
            }
        ]
        
        # 加载现有数据并添加球员
        team_data = workflow_service.team_service.load_team_data_for_user(
            workflow_service.user_id, test_team_name
        )
        
        if not team_data:
            team_data = team_info.copy()
        
        team_data['players'] = players
        
        # 保存更新后的数据
        workflow_service.team_service.save_team_data_for_user(
            workflow_service.user_id,
            test_team_name,
            team_data
        )
        
        print(f"✅ 球员添加成功: {len(players)} 个球员")
        
        # 步骤4: 生成队徽
        print(f"\n步骤4: 生成队徽")
        print("-" * 40)
        
        logo_path = workflow_service._auto_generate_team_logo(test_team_name)
        
        print(f"✅ 队徽生成成功: {logo_path}")
        print(f"   文件存在: {os.path.exists(logo_path)}")
        print(f"   文件大小: {os.path.getsize(logo_path)/1024:.1f}KB")
        
        # 步骤5: 生成Word文档
        print(f"\n步骤5: 生成Word文档")
        print("-" * 40)
        
        # 准备球员照片映射（使用空路径）
        player_mapping = {}
        for i, player in enumerate(players, 1):
            ai_player_key = f"ai_player_{i}"
            player_mapping[ai_player_key] = ""  # 空照片路径
        
        print(f"   球员映射: {player_mapping}")
        
        # 调用Word生成
        word_result = workflow_service._auto_generate_word_document(
            test_team_name,
            player_mapping,
            logo_path
        )
        
        print(f"📊 Word生成结果:")
        print(f"   成功: {word_result.get('success', False)}")
        
        if word_result.get('success'):
            word_file = word_result.get('file_path', '')
            print(f"   文件路径: {word_file}")
            
            if word_file and os.path.exists(word_file):
                file_size = os.path.getsize(word_file) / 1024
                print(f"   文件大小: {file_size:.1f}KB")
                print(f"   修改时间: {datetime.fromtimestamp(os.path.getmtime(word_file)).strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 检查文件大小
                if file_size > 50:
                    print(f"   ✅ 文件大小合理，可能包含队徽")
                else:
                    print(f"   ⚠️ 文件大小较小，可能未包含队徽")
            else:
                print(f"   ❌ Word文件不存在")
        else:
            error_msg = word_result.get('error', '未知错误')
            print(f"   ❌ 生成失败: {error_msg}")
        
        # 步骤6: 验证队徽是否在球队数据中
        print(f"\n步骤6: 验证队徽数据")
        print("-" * 40)
        
        # 重新加载球队数据
        final_team_data = workflow_service.team_service.load_team_data_for_user(
            workflow_service.user_id, test_team_name
        )
        
        if final_team_data:
            logo_in_data = 'logo_path' in final_team_data
            print(f"   球队数据中有logo_path: {logo_in_data}")
            
            if logo_in_data:
                stored_logo_path = final_team_data['logo_path']
                print(f"   存储的队徽路径: {stored_logo_path}")
                print(f"   队徽文件存在: {os.path.exists(stored_logo_path) if stored_logo_path else False}")
            
            # 检查team_info中的logo_path
            if 'team_info' in final_team_data:
                team_info_logo = final_team_data['team_info'].get('logo_path')
                print(f"   team_info中的logo_path: {team_info_logo}")
        
        # 步骤7: 最终评估
        print(f"\n步骤7: 最终评估")
        print("-" * 40)
        
        success_criteria = {
            '球队创建': True,
            '球员添加': len(players) > 0,
            '队徽生成': os.path.exists(logo_path),
            'Word生成': word_result.get('success', False),
            '队徽数据存储': final_team_data and 'logo_path' in final_team_data
        }
        
        print(f"📊 成功标准检查:")
        success_count = 0
        for criterion, passed in success_criteria.items():
            status = "✅" if passed else "❌"
            print(f"   {status} {criterion}")
            if passed:
                success_count += 1
        
        total_criteria = len(success_criteria)
        success_rate = success_count / total_criteria * 100
        
        print(f"\n🎯 最终结果:")
        print(f"   成功项: {success_count}/{total_criteria}")
        print(f"   成功率: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("🎉 真实工作流程测试成功！队徽功能已完全修复！")
            print("   现在用户可以正常使用队徽功能，队徽会自动插入到Word文档中。")
        elif success_rate >= 60:
            print("⚠️ 工作流程部分成功，还有一些小问题")
        else:
            print("❌ 工作流程测试失败")
        
        print(f"\n⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return {
            'success_rate': success_rate,
            'word_generated': word_result.get('success', False),
            'logo_generated': os.path.exists(logo_path),
            'word_file': word_result.get('file_path', '') if word_result.get('success') else None
        }
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = test_real_workflow_with_logo()
    
    if result and result['success_rate'] >= 80:
        print(f"\n🎊 恭喜！队徽功能修复完成！")
        print(f"📋 修复总结:")
        print(f"   1. ✅ 在Java的TeamInfo类中添加了logoPath字段")
        print(f"   2. ✅ 在JsonDataParser中添加了logoPath解析逻辑")
        print(f"   3. ✅ 在WordGeneratorCore中添加了logoPath处理")
        print(f"   4. ✅ 修复了球员数据字段名问题（number vs jerseyNumber）")
        print(f"   5. ✅ 确保了队徽路径正确传递到Word模板")
        
        if result.get('word_file'):
            print(f"\n📄 生成的Word文件: {os.path.basename(result['word_file'])}")
            print(f"   请检查该文件确认队徽是否正确显示。")
    else:
        print(f"\n⚠️ 还需要进一步调试和优化。")
