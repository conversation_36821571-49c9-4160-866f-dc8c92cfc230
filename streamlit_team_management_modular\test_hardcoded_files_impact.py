#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试硬编码路径文件的实际影响
"""

import sys
import os
import shutil
from pathlib import Path

def backup_and_modify_config():
    """备份并修改配置文件"""
    config_file = "config/settings.py"
    backup_file = "config/settings_original_backup.py"
    
    print("🔧 修改配置文件为15人模板...")
    
    if not os.path.exists(backup_file):
        shutil.copy2(config_file, backup_file)
    
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    old_template_path = 'TEMPLATE_PATH: str = "../word_zc/ai-football-generator/template.docx"'
    new_template_path = 'TEMPLATE_PATH: str = "../word_zc/template_15players_fixed.docx"'
    
    if old_template_path in content:
        modified_content = content.replace(old_template_path, new_template_path)
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        print("✅ 配置文件已修改为15人模板")
        return True
    return False

def test_hardcoded_file(file_name):
    """测试单个硬编码文件"""
    print(f"\n🧪 测试文件: {file_name}")
    print("=" * 50)
    
    if not os.path.exists(file_name):
        print(f"❌ 文件不存在: {file_name}")
        return False
    
    try:
        # 清除模块缓存
        if 'config.settings' in sys.modules:
            del sys.modules['config.settings']
        
        # 运行测试文件
        print(f"🚀 运行 {file_name}...")
        
        # 使用subprocess运行，避免影响当前进程
        import subprocess
        result = subprocess.run(
            [sys.executable, file_name],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=60
        )
        
        print(f"📊 返回码: {result.returncode}")
        
        if result.stdout:
            print("📝 标准输出:")
            print(result.stdout[-1000:])  # 显示最后1000字符
        
        if result.stderr:
            print("⚠️ 错误输出:")
            print(result.stderr[-500:])   # 显示最后500字符
        
        # 检查是否成功生成了Word文档
        if result.returncode == 0:
            # 查找生成的文档
            output_patterns = [
                "word_output",
                "data/test_user/word_output",
                "temp"
            ]
            
            generated_files = []
            import time
            current_time = time.time()
            for pattern in output_patterns:
                if os.path.exists(pattern):
                    for file in Path(pattern).glob("*.docx"):
                        if file.stat().st_mtime > (current_time - 300):  # 5分钟内的文件
                            generated_files.append(str(file))
            
            if generated_files:
                print(f"✅ 成功生成Word文档: {len(generated_files)} 个文件")
                for file in generated_files:
                    print(f"   📄 {file}")
                return True
            else:
                print("⚠️ 程序运行成功但未找到新生成的Word文档")
                return True  # 程序本身运行成功
        else:
            print(f"❌ 程序运行失败，返回码: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 程序运行超时")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def analyze_hardcoded_files():
    """分析硬编码文件的内容"""
    print("\n🔍 分析硬编码文件内容")
    print("=" * 50)
    
    hardcoded_files = [
        "test_complete_word_generation.py",
        "test_word_integration.py", 
        "test_final_integration.py"
    ]
    
    analysis_results = {}
    
    for file_name in hardcoded_files:
        if os.path.exists(file_name):
            try:
                with open(file_name, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 分析文件用途
                is_test_file = "test" in file_name.lower()
                uses_config = "from config" in content or "config.settings" in content
                has_hardcoded_path = 'template_path = "../word_zc/ai-football-generator/template.docx"' in content
                
                analysis_results[file_name] = {
                    'is_test_file': is_test_file,
                    'uses_config': uses_config,
                    'has_hardcoded_path': has_hardcoded_path,
                    'file_type': '测试文件' if is_test_file else '生产文件'
                }
                
                print(f"\n📄 {file_name}:")
                print(f"   类型: {analysis_results[file_name]['file_type']}")
                print(f"   使用配置: {'是' if uses_config else '否'}")
                print(f"   硬编码路径: {'是' if has_hardcoded_path else '否'}")
                
            except Exception as e:
                print(f"❌ 分析 {file_name} 失败: {e}")
    
    return analysis_results

def test_production_vs_test_impact():
    """测试生产环境vs测试环境的影响"""
    print("\n🎯 测试生产环境vs测试环境影响")
    print("=" * 50)
    
    # 测试主要的生产功能
    try:
        from services.fashion_workflow_service import FashionWorkflowService
        from config.settings import app_settings
        
        # 检查生产环境配置
        paths = app_settings.word_generator.get_absolute_paths("test_user", app_settings.paths)
        print(f"🏭 生产环境模板路径: {paths['template_path']}")
        
        if "template_15players" in paths['template_path']:
            print("✅ 生产环境已正确使用15人模板")
            return True
        else:
            print("❌ 生产环境未使用15人模板")
            return False
            
    except Exception as e:
        print(f"❌ 生产环境测试失败: {e}")
        return False

def restore_original_config():
    """恢复原始配置文件"""
    config_file = "config/settings.py"
    backup_file = "config/settings_original_backup.py"
    
    if os.path.exists(backup_file):
        shutil.copy2(backup_file, config_file)
        print("✅ 已恢复原始配置文件")
        return True
    return False

def main():
    """主测试函数"""
    print("🎯 测试硬编码路径文件的实际影响")
    print("=" * 60)
    
    try:
        # 1. 修改配置文件
        if not backup_and_modify_config():
            print("❌ 配置文件修改失败")
            return
        
        # 2. 分析硬编码文件
        analysis_results = analyze_hardcoded_files()
        
        # 3. 测试生产环境影响
        production_ok = test_production_vs_test_impact()
        
        # 4. 测试硬编码文件（可选）
        hardcoded_files = [
            "test_complete_word_generation.py",
            "test_word_integration.py", 
            "test_final_integration.py"
        ]
        
        test_results = []
        for file_name in hardcoded_files:
            if os.path.exists(file_name):
                result = test_hardcoded_file(file_name)
                test_results.append((file_name, result))
        
        # 5. 恢复原始配置
        restore_original_config()
        
        # 6. 总结结果
        print("\n📊 影响分析总结")
        print("=" * 60)
        
        print(f"🏭 生产环境: {'✅ 正常' if production_ok else '❌ 有问题'}")
        
        test_files_ok = sum(1 for _, result in test_results if result)
        total_test_files = len(test_results)
        
        print(f"🧪 测试文件: {test_files_ok}/{total_test_files} 个正常运行")
        
        # 判断是否需要修改
        if production_ok:
            print("\n🎉 结论: 生产环境正常，无需修改其他代码!")
            print("💡 硬编码路径的文件都是测试文件，不影响实际功能")
            
            if test_files_ok < total_test_files:
                print("⚠️ 部分测试文件可能需要更新，但不影响生产功能")
        else:
            print("\n❌ 结论: 需要检查生产环境配置")
            
    except Exception as e:
        print(f"❌ 测试过程出现异常: {e}")
        restore_original_config()

if __name__ == "__main__":
    main()
