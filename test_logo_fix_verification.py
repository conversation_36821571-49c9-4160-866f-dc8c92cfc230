#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
队徽修复验证测试
Logo Fix Verification Test

验证Java代码修复后队徽是否能正确插入到Word文档中
"""

import os
import sys
import json
import tempfile
import subprocess
from datetime import datetime

# 添加项目路径
sys.path.append('streamlit_team_management_modular')

class LogoFixVerification:
    """队徽修复验证器"""
    
    def __init__(self):
        self.test_team_name = "队徽修复验证测试队"
        
    def run_verification(self):
        """运行验证测试"""
        
        print("🔧 队徽修复验证测试")
        print("=" * 80)
        print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 目标: 验证Java代码修复后队徽能否正确插入Word")
        print()
        
        try:
            # 步骤1: 生成测试队徽和球队数据
            print("步骤1: 生成测试队徽和球队数据")
            print("-" * 40)
            
            from services.fashion_workflow_service import FashionWorkflowService
            workflow_service = FashionWorkflowService()
            
            # 生成队徽
            logo_path = workflow_service._auto_generate_team_logo(self.test_team_name)
            print(f"✅ 队徽生成成功: {logo_path}")
            print(f"   文件存在: {os.path.exists(logo_path)}")
            print(f"   文件大小: {os.path.getsize(logo_path)/1024:.1f}KB")
            
            # 获取球队数据
            team_data = workflow_service.team_service.load_team_data_for_user(
                workflow_service.user_id, self.test_team_name
            )
            print(f"✅ 球队数据加载成功")
            print(f"   队徽路径: {team_data.get('logo_path', 'None')}")
            
            # 步骤2: 准备Word生成数据
            print(f"\n步骤2: 准备Word生成数据")
            print("-" * 40)
            
            # 创建测试JSON数据
            test_data = {
                "teamInfo": {
                    "title": f"{self.test_team_name}报名表",
                    "organizationName": self.test_team_name,
                    "teamLeader": "测试领队",
                    "coach": "测试教练",
                    "teamDoctor": "测试队医",
                    "contactPerson": "测试联系人",
                    "contactPhone": "13800138000",
                    "logoPath": logo_path,  # 关键：队徽路径
                    "jerseyColor": "红色",
                    "shortsColor": "蓝色",
                    "socksColor": "白色",
                    "goalkeeperKitColor": "绿色"
                },
                "players": [
                    {
                        "name": "测试球员1",
                        "jerseyNumber": "1",
                        "jerseyColor": "红色",
                        "shortsColor": "蓝色",
                        "socksColor": "白色",
                        "photoPath": "test_photo_1.jpg"
                    }
                ]
            }
            
            print(f"✅ JSON数据准备完成")
            print(f"   logoPath: {test_data['teamInfo']['logoPath']}")
            
            # 写入临时JSON文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
                json.dump(test_data, f, ensure_ascii=False, indent=2)
                test_json_file = f.name
            
            print(f"✅ JSON文件创建: {test_json_file}")
            
            # 步骤3: 调用修复后的Java程序
            print(f"\n步骤3: 调用修复后的Java程序")
            print("-" * 40)
            
            jar_path = "word_zc/ai-football-generator/target/word-generator.jar"
            cmd = ['java', '-cp', jar_path, 'CommandLineMain', test_json_file]
            
            print(f"🚀 执行命令: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=60,
                encoding='utf-8',
                errors='ignore'
            )
            
            print(f"📊 Java程序返回码: {result.returncode}")
            
            # 步骤4: 分析Java程序输出
            print(f"\n步骤4: 分析Java程序输出")
            print("-" * 40)
            
            output_text = result.stdout + result.stderr
            
            # 检查队徽相关信息
            logo_processed = False
            logo_path_found = False
            
            if 'logoPath' in output_text:
                logo_processed = True
                print("✅ Java程序处理了logoPath字段")
            else:
                print("❌ Java程序未处理logoPath字段")
            
            if logo_path in output_text:
                logo_path_found = True
                print("✅ Java程序识别了队徽路径")
            else:
                print("❌ Java程序未识别队徽路径")
            
            # 检查队徽路径是否在toString输出中
            if "队徽路径=" in output_text:
                print("✅ 队徽路径出现在调试输出中")
                # 提取队徽路径信息
                for line in output_text.split('\n'):
                    if "队徽路径=" in line:
                        print(f"   🔍 {line.strip()}")
            else:
                print("❌ 队徽路径未出现在调试输出中")
            
            # 检查是否有SUCCESS输出
            java_success = 'SUCCESS:' in output_text
            print(f"Java程序执行成功: {'✅' if java_success else '❌'}")
            
            # 显示关键输出
            print(f"\n📄 Java程序关键输出:")
            for line in output_text.split('\n'):
                if any(keyword in line for keyword in ['INFO:', 'ERROR:', 'SUCCESS:', '队徽路径=']):
                    print(f"   {line}")
            
            # 步骤5: 检查输出文件
            print(f"\n步骤5: 检查输出文件")
            print("-" * 40)
            
            # 查找生成的Word文件
            output_dir = "word_zc/ai-football-generator/output"
            if os.path.exists(output_dir):
                word_files = [f for f in os.listdir(output_dir) if f.endswith('.docx')]
                word_files.sort(key=lambda x: os.path.getmtime(os.path.join(output_dir, x)), reverse=True)
                
                if word_files:
                    latest_word_file = os.path.join(output_dir, word_files[0])
                    print(f"✅ 找到最新Word文件: {word_files[0]}")
                    print(f"   文件大小: {os.path.getsize(latest_word_file)/1024:.1f}KB")
                    print(f"   修改时间: {datetime.fromtimestamp(os.path.getmtime(latest_word_file)).strftime('%Y-%m-%d %H:%M:%S')}")
                    
                    # 检查文件大小是否合理（包含图片的Word文件应该比较大）
                    file_size_kb = os.path.getsize(latest_word_file) / 1024
                    if file_size_kb > 100:  # 如果文件大于100KB，可能包含了图片
                        print("✅ 文件大小表明可能包含了队徽图片")
                    else:
                        print("⚠️ 文件大小较小，可能未包含队徽图片")
                else:
                    print("❌ 未找到生成的Word文件")
            else:
                print("❌ 输出目录不存在")
            
            # 步骤6: 生成验证报告
            print(f"\n步骤6: 验证结果总结")
            print("-" * 40)
            
            verification_results = {
                'logo_generated': os.path.exists(logo_path),
                'logo_processed_by_java': logo_processed,
                'logo_path_found_in_output': logo_path_found,
                'java_execution_success': java_success,
                'word_file_generated': java_success and os.path.exists(output_dir) and len([f for f in os.listdir(output_dir) if f.endswith('.docx')]) > 0
            }
            
            print("📊 验证结果:")
            for key, value in verification_results.items():
                status = "✅" if value else "❌"
                print(f"   {status} {key}: {value}")
            
            # 总体评估
            success_count = sum(verification_results.values())
            total_count = len(verification_results)
            success_rate = success_count / total_count * 100
            
            print(f"\n🎯 总体评估:")
            print(f"   成功项: {success_count}/{total_count}")
            print(f"   成功率: {success_rate:.1f}%")
            
            if success_rate >= 80:
                print("🎉 队徽修复验证成功！")
            elif success_rate >= 60:
                print("⚠️ 队徽修复部分成功，仍有改进空间")
            else:
                print("❌ 队徽修复验证失败，需要进一步调试")
            
            # 清理临时文件
            try:
                os.unlink(test_json_file)
            except:
                pass
            
            print(f"\n⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            return verification_results
            
        except Exception as e:
            print(f"❌ 验证测试异常: {e}")
            import traceback
            traceback.print_exc()
            return None

if __name__ == "__main__":
    verifier = LogoFixVerification()
    verifier.run_verification()
