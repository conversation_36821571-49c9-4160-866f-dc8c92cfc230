{"test_time": "2025-09-01T01:48:09.082925", "test_team_name": "完整测试队", "test_results": {"1. 工作流程服务初始化": {"success": true, "details": "用户ID: default_user", "has_user_id": true, "has_team_service": true, "has_ai_service": true}, "2. 队徽生成测试": {"success": true, "logo_path": "assets/logos\\完整测试队_logo_20250901_014809.png", "file_size": 3147861, "details": "队徽文件: 完整测试队_logo_20250901_014809.png (3074.1KB)"}, "3. 模拟球员数据准备": {"success": true, "player_count": 5, "details": "准备了 5 名球员的数据"}, "4. 模拟换装结果创建": {"success": true, "successful_count": 5, "details": "模拟了 5 个成功的换装结果"}, "5. 球员图片映射创建": {"success": true, "mapping_count": 5, "details": "创建了 5 个球员的图片映射", "mapping": {"player_1": "temp_files/fashion_result_player_1.jpg", "player_2": "temp_files/fashion_result_player_2.jpg", "player_3": "temp_files/fashion_result_player_3.jpg", "player_4": "temp_files/fashion_result_player_4.jpg", "player_5": "temp_files/fashion_result_player_5.jpg"}}, "6. Word文档生成测试": {"success": false, "error": "Word生成失败: 未找到球队数据: 完整测试队", "word_result": {"success": false, "error": "未找到球队数据: 完整测试队"}}, "7. 队徽在Word中的验证": {"success": false, "error": "Word文档生成失败，无法验证队徽"}, "8. 完整流程集成验证": {"success": false, "total_tests": 7, "successful_tests": 5, "success_rate": 71.42857142857143, "logo_generated": true, "word_generated": false, "logo_verified": false, "issues": ["Word文档生成失败", "队徽在Word中的验证失败"], "details": "集成测试 失败: 5/7 步骤通过"}}, "summary": {"total_tests": 8, "successful_tests": 5, "logo_to_word_integration": false}}