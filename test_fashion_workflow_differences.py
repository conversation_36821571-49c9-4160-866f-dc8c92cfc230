#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fashion Workflow文件差异对比测试
Fashion Workflow Files Difference Comparison Test

对比分析两个fashion_workflow.py文件的差异
"""

import os
import sys
import difflib
import hashlib
from datetime import datetime

def read_file_content(file_path: str) -> str:
    """读取文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"❌ 读取文件失败 {file_path}: {e}")
        return ""

def get_file_info(file_path: str) -> dict:
    """获取文件基本信息"""
    try:
        stat = os.stat(file_path)
        content = read_file_content(file_path)
        
        return {
            'path': file_path,
            'size': stat.st_size,
            'modified': datetime.fromtimestamp(stat.st_mtime),
            'lines': len(content.splitlines()),
            'chars': len(content),
            'md5': hashlib.md5(content.encode('utf-8')).hexdigest()
        }
    except Exception as e:
        print(f"❌ 获取文件信息失败 {file_path}: {e}")
        return {}

def compare_file_structure(file1_path: str, file2_path: str):
    """对比文件结构信息"""
    
    print("📊 文件基本信息对比")
    print("=" * 80)
    
    info1 = get_file_info(file1_path)
    info2 = get_file_info(file2_path)
    
    if not info1 or not info2:
        print("❌ 无法获取文件信息")
        return
    
    print(f"📁 当前版本: {info1['path']}")
    print(f"   📏 大小: {info1['size']} bytes")
    print(f"   📄 行数: {info1['lines']} lines")
    print(f"   📝 字符: {info1['chars']} chars")
    print(f"   🕒 修改时间: {info1['modified']}")
    print(f"   🔐 MD5: {info1['md5']}")
    
    print(f"\n📁 备份版本: {info2['path']}")
    print(f"   📏 大小: {info2['size']} bytes")
    print(f"   📄 行数: {info2['lines']} lines")
    print(f"   📝 字符: {info2['chars']} chars")
    print(f"   🕒 修改时间: {info2['modified']}")
    print(f"   🔐 MD5: {info2['md5']}")
    
    # 计算差异
    size_diff = info1['size'] - info2['size']
    lines_diff = info1['lines'] - info2['lines']
    chars_diff = info1['chars'] - info2['chars']
    
    print(f"\n📊 差异统计:")
    print(f"   📏 大小差异: {size_diff:+d} bytes")
    print(f"   📄 行数差异: {lines_diff:+d} lines")
    print(f"   📝 字符差异: {chars_diff:+d} chars")
    
    if info1['md5'] == info2['md5']:
        print(f"   ✅ 文件内容完全相同")
    else:
        print(f"   ❌ 文件内容存在差异")

def analyze_detailed_differences(file1_path: str, file2_path: str):
    """分析详细差异"""
    
    print(f"\n🔍 详细差异分析")
    print("=" * 80)
    
    content1 = read_file_content(file1_path)
    content2 = read_file_content(file2_path)
    
    if not content1 or not content2:
        print("❌ 无法读取文件内容")
        return
    
    lines1 = content1.splitlines()
    lines2 = content2.splitlines()
    
    # 使用difflib进行差异分析
    differ = difflib.unified_diff(
        lines2, lines1,
        fromfile='backup_before_fix/fashion_workflow.py',
        tofile='components/fashion_workflow.py',
        lineterm=''
    )
    
    diff_lines = list(differ)
    
    if not diff_lines:
        print("✅ 两个文件内容完全相同")
        return
    
    print(f"📋 发现 {len(diff_lines)} 行差异:")
    
    # 统计差异类型
    added_lines = [line for line in diff_lines if line.startswith('+') and not line.startswith('+++')]
    removed_lines = [line for line in diff_lines if line.startswith('-') and not line.startswith('---')]
    
    print(f"   ➕ 新增行数: {len(added_lines)}")
    print(f"   ➖ 删除行数: {len(removed_lines)}")
    
    # 显示差异内容
    print(f"\n📝 差异内容:")
    for line in diff_lines:
        if line.startswith('@@'):
            print(f"\n🔍 {line}")
        elif line.startswith('+') and not line.startswith('+++'):
            print(f"➕ {line[1:]}")
        elif line.startswith('-') and not line.startswith('---'):
            print(f"➖ {line[1:]}")

def analyze_function_differences(file1_path: str, file2_path: str):
    """分析函数级别的差异"""
    
    print(f"\n🔧 函数级别差异分析")
    print("=" * 80)
    
    def extract_functions(content: str) -> dict:
        """提取文件中的函数定义"""
        functions = {}
        lines = content.splitlines()
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            if stripped.startswith('def '):
                func_name = stripped.split('(')[0].replace('def ', '')
                # 找到函数的结束位置
                indent_level = len(line) - len(line.lstrip())
                func_lines = [line]
                
                for j in range(i + 1, len(lines)):
                    next_line = lines[j]
                    if next_line.strip() == '':
                        func_lines.append(next_line)
                        continue
                    
                    next_indent = len(next_line) - len(next_line.lstrip())
                    if next_indent <= indent_level and next_line.strip():
                        break
                    func_lines.append(next_line)
                
                functions[func_name] = {
                    'line_start': i + 1,
                    'line_count': len(func_lines),
                    'content': '\n'.join(func_lines)
                }
        
        return functions
    
    content1 = read_file_content(file1_path)
    content2 = read_file_content(file2_path)
    
    functions1 = extract_functions(content1)
    functions2 = extract_functions(content2)
    
    print(f"📋 当前版本函数: {len(functions1)} 个")
    print(f"📋 备份版本函数: {len(functions2)} 个")
    
    # 找出新增、删除、修改的函数
    all_functions = set(functions1.keys()) | set(functions2.keys())
    
    added_functions = set(functions1.keys()) - set(functions2.keys())
    removed_functions = set(functions2.keys()) - set(functions1.keys())
    common_functions = set(functions1.keys()) & set(functions2.keys())
    
    if added_functions:
        print(f"\n➕ 新增函数 ({len(added_functions)} 个):")
        for func in sorted(added_functions):
            print(f"   🔧 {func} (第{functions1[func]['line_start']}行, {functions1[func]['line_count']}行)")
    
    if removed_functions:
        print(f"\n➖ 删除函数 ({len(removed_functions)} 个):")
        for func in sorted(removed_functions):
            print(f"   🔧 {func} (第{functions2[func]['line_start']}行, {functions2[func]['line_count']}行)")
    
    # 检查修改的函数
    modified_functions = []
    for func in common_functions:
        if functions1[func]['content'] != functions2[func]['content']:
            modified_functions.append(func)
    
    if modified_functions:
        print(f"\n🔄 修改函数 ({len(modified_functions)} 个):")
        for func in sorted(modified_functions):
            lines1 = functions1[func]['line_count']
            lines2 = functions2[func]['line_count']
            line_diff = lines1 - lines2
            print(f"   🔧 {func}: {lines2}行 → {lines1}行 ({line_diff:+d})")

def analyze_import_differences(file1_path: str, file2_path: str):
    """分析导入语句差异"""
    
    print(f"\n📦 导入语句差异分析")
    print("=" * 80)
    
    def extract_imports(content: str) -> set:
        """提取导入语句"""
        imports = set()
        lines = content.splitlines()
        
        for line in lines:
            stripped = line.strip()
            if stripped.startswith('import ') or stripped.startswith('from '):
                imports.add(stripped)
        
        return imports
    
    content1 = read_file_content(file1_path)
    content2 = read_file_content(file2_path)
    
    imports1 = extract_imports(content1)
    imports2 = extract_imports(content2)
    
    added_imports = imports1 - imports2
    removed_imports = imports2 - imports1
    common_imports = imports1 & imports2
    
    print(f"📋 当前版本导入: {len(imports1)} 个")
    print(f"📋 备份版本导入: {len(imports2)} 个")
    print(f"📋 共同导入: {len(common_imports)} 个")
    
    if added_imports:
        print(f"\n➕ 新增导入 ({len(added_imports)} 个):")
        for imp in sorted(added_imports):
            print(f"   📦 {imp}")
    
    if removed_imports:
        print(f"\n➖ 删除导入 ({len(removed_imports)} 个):")
        for imp in sorted(removed_imports):
            print(f"   📦 {imp}")

def test_functionality_differences():
    """测试功能差异"""
    
    print(f"\n🧪 功能差异测试")
    print("=" * 80)
    
    try:
        # 添加项目路径
        sys.path.append('streamlit_team_management_modular')
        
        # 测试当前版本
        print("🔧 测试当前版本...")
        try:
            from components.fashion_workflow import FashionWorkflowComponent
            current_component = FashionWorkflowComponent()
            print("✅ 当前版本导入成功")
            
            # 检查可用方法
            current_methods = [method for method in dir(current_component) if not method.startswith('__')]
            print(f"📋 当前版本方法: {len(current_methods)} 个")
            
        except Exception as e:
            print(f"❌ 当前版本导入失败: {e}")
            current_methods = []
        
        # 由于backup版本在不同路径，我们通过文件分析来比较
        print(f"\n📊 方法对比:")
        for method in sorted(current_methods):
            print(f"   🔧 {method}")
            
    except Exception as e:
        print(f"❌ 功能测试异常: {e}")

def generate_difference_report():
    """生成差异报告"""
    
    print(f"\n📋 差异分析报告")
    print("=" * 80)
    
    print("🎯 分析目标:")
    print("   1. 文件基本信息对比")
    print("   2. 详细内容差异分析")
    print("   3. 函数级别差异分析")
    print("   4. 导入语句差异分析")
    print("   5. 功能差异测试")
    
    print(f"\n⏰ 分析完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    try:
        print("🔍 Fashion Workflow文件差异对比测试")
        print("=" * 80)
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 文件路径
        current_file = "streamlit_team_management_modular/components/fashion_workflow.py"
        backup_file = "streamlit_team_management_modular/backup_before_fix/fashion_workflow.py"
        
        # 检查文件是否存在
        if not os.path.exists(current_file):
            print(f"❌ 当前文件不存在: {current_file}")
            exit(1)
        
        if not os.path.exists(backup_file):
            print(f"❌ 备份文件不存在: {backup_file}")
            exit(1)
        
        # 执行各项对比分析
        compare_file_structure(current_file, backup_file)
        analyze_detailed_differences(current_file, backup_file)
        analyze_function_differences(current_file, backup_file)
        analyze_import_differences(current_file, backup_file)
        test_functionality_differences()
        
        # 生成报告
        generate_difference_report()
        
    except Exception as e:
        print(f"❌ 测试执行异常: {e}")
        import traceback
        traceback.print_exc()
