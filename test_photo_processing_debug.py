#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试照片处理调试信息
Test Photo Processing Debug Information
"""

import os
import re

def analyze_photo_processing_debug():
    """分析照片处理调试信息"""
    print("🔍 分析照片处理调试信息")
    print("=" * 80)
    
    # 从用户截图中发现的照片处理调试信息
    photo_debug_info = [
        {
            "content": "正在处理第 1/1 张照片...",
            "type": "处理进度信息",
            "severity": "🟡 中等 - 可能是调试信息"
        },
        {
            "content": "处理照片: ab3b63e6b8284d95bd5a76c046f0a827.jpg",
            "type": "文件名调试信息",
            "severity": "🟡 中等 - 内部文件名"
        },
        {
            "content": "步骤1: 背景移除处理...",
            "type": "处理步骤信息",
            "severity": "🔴 高 - 明显的调试信息"
        },
        {
            "content": "背景移除请求失败: HTTPSConnectionPool(host='api.302.ai', port=443): Read timed out. (read timeout=60)",
            "type": "错误调试信息",
            "severity": "🔴 高 - 详细错误信息"
        },
        {
            "content": "ab3b63e6b8284d95bd5a76c046f0a827.jpg 处理失败: 步骤1背景移除失败",
            "type": "失败调试信息",
            "severity": "🔴 高 - 详细失败信息"
        }
    ]
    
    print("📋 发现的照片处理调试信息:")
    for i, info in enumerate(photo_debug_info, 1):
        print(f"\n{i}. {info['severity']}")
        print(f"   类型: {info['type']}")
        print(f"   内容: {info['content']}")
    
    return photo_debug_info

def search_photo_processing_sources():
    """搜索照片处理调试信息的源头"""
    print(f"\n🔍 搜索照片处理调试信息源头")
    print("=" * 80)
    
    # 搜索关键词
    photo_debug_keywords = [
        "正在处理第",
        "张照片",
        "处理照片:",
        "步骤1:",
        "背景移除处理",
        "背景移除请求失败",
        "处理失败:",
        "步骤1背景移除失败",
        "HTTPSConnectionPool",
        "Read timed out"
    ]
    
    search_results = {}
    
    # 重点搜索照片处理相关文件
    photo_files = [
        "streamlit_team_management_modular/services/ai_image_generation_service.py",
        "streamlit_team_management_modular/services/enhanced_ai_service.py",
        "streamlit_team_management_modular/pages/AI_Photo_Editor.py",
        "streamlit_team_management_modular/components/ai_chat.py"
    ]
    
    # 搜索整个目录
    for root, dirs, files in os.walk("streamlit_team_management_modular"):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    for keyword in photo_debug_keywords:
                        if keyword in content:
                            if file_path not in search_results:
                                search_results[file_path] = []
                            
                            # 找到包含关键词的行
                            lines = content.split('\n')
                            for i, line in enumerate(lines, 1):
                                if keyword in line:
                                    search_results[file_path].append({
                                        'line': i,
                                        'keyword': keyword,
                                        'content': line.strip()
                                    })
                                    
                except Exception as e:
                    continue
    
    print("📁 找到照片处理调试信息的文件:")
    for file_path, findings in search_results.items():
        print(f"\n📄 {file_path}")
        for finding in findings:
            print(f"   第{finding['line']}行: {finding['content'][:80]}...")
    
    return search_results

def search_photo_debug_patterns():
    """搜索照片处理调试模式"""
    print(f"\n🎯 搜索照片处理调试模式")
    print("=" * 80)
    
    # 搜索模式
    photo_debug_patterns = [
        r'st\.write.*正在处理',
        r'st\.write.*张照片',
        r'st\.write.*处理照片',
        r'st\.write.*步骤\d+',
        r'st\.write.*背景移除',
        r'st\.error.*处理失败',
        r'st\.error.*背景移除',
        r'st\.warning.*失败',
        r'print.*处理照片',
        r'print.*步骤\d+'
    ]
    
    pattern_results = {}
    
    for root, dirs, files in os.walk("streamlit_team_management_modular"):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    lines = content.split('\n')
                    for i, line in enumerate(lines, 1):
                        for pattern in photo_debug_patterns:
                            if re.search(pattern, line, re.IGNORECASE):
                                if file_path not in pattern_results:
                                    pattern_results[file_path] = []
                                pattern_results[file_path].append({
                                    'line': i,
                                    'pattern': pattern,
                                    'content': line.strip()
                                })
                                
                except Exception as e:
                    continue
    
    print("🎯 找到匹配照片处理调试模式的文件:")
    for file_path, findings in pattern_results.items():
        print(f"\n📄 {file_path}")
        for finding in findings:
            print(f"   🔍 第{finding['line']}行: {finding['content'][:80]}...")
    
    return pattern_results

def analyze_ai_image_service():
    """分析AI图像生成服务"""
    print(f"\n🔍 分析AI图像生成服务")
    print("=" * 80)
    
    ai_image_file = "streamlit_team_management_modular/services/ai_image_generation_service.py"
    
    if os.path.exists(ai_image_file):
        try:
            with open(ai_image_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找调试相关的输出
            lines = content.split('\n')
            debug_lines = []
            
            for i, line in enumerate(lines, 1):
                if any(keyword in line.lower() for keyword in ['st.write', 'st.error', 'st.warning', 'st.info', 'print', '处理照片', '步骤', '背景移除']):
                    debug_lines.append({
                        'line': i,
                        'content': line.strip()
                    })
            
            print(f"📄 AI图像生成服务调试信息:")
            if debug_lines:
                for debug_line in debug_lines:
                    print(f"   第{debug_line['line']}行: {debug_line['content'][:80]}...")
            else:
                print(f"   ✅ 未发现明显的调试输出")
            
            return debug_lines
            
        except Exception as e:
            print(f"   ❌ 读取失败: {e}")
            return []
    else:
        print(f"   ❌ 文件不存在: {ai_image_file}")
        return []

def analyze_photo_editor_page():
    """分析照片编辑器页面"""
    print(f"\n🔍 分析照片编辑器页面")
    print("=" * 80)
    
    photo_editor_file = "streamlit_team_management_modular/pages/AI_Photo_Editor.py"
    
    if os.path.exists(photo_editor_file):
        try:
            with open(photo_editor_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找调试相关的输出
            lines = content.split('\n')
            debug_lines = []
            
            for i, line in enumerate(lines, 1):
                if any(keyword in line.lower() for keyword in ['st.write', 'st.error', 'st.warning', 'st.info', 'print', '处理', '步骤', '背景']):
                    debug_lines.append({
                        'line': i,
                        'content': line.strip()
                    })
            
            print(f"📄 照片编辑器页面调试信息:")
            if debug_lines:
                for debug_line in debug_lines[:10]:  # 只显示前10个
                    print(f"   第{debug_line['line']}行: {debug_line['content'][:80]}...")
                if len(debug_lines) > 10:
                    print(f"   ... 还有 {len(debug_lines)-10} 个调试输出")
            else:
                print(f"   ✅ 未发现明显的调试输出")
            
            return debug_lines
            
        except Exception as e:
            print(f"   ❌ 读取失败: {e}")
            return []
    else:
        print(f"   ❌ 文件不存在: {photo_editor_file}")
        return []

def generate_photo_debug_removal_plan():
    """生成照片处理调试信息移除计划"""
    print(f"\n📋 生成照片处理调试信息移除计划")
    print("=" * 80)
    
    removal_plan = {
        "高优先级移除": [
            {
                "target": "步骤1: 背景移除处理...",
                "reason": "明显的处理步骤调试信息",
                "action": "删除或改为用户友好的进度提示"
            },
            {
                "target": "背景移除请求失败: HTTPSConnectionPool(...)",
                "reason": "详细的技术错误信息，用户不需要看到",
                "action": "删除或改为简单的错误提示"
            },
            {
                "target": "ab3b63e6b8284d95bd5a76c046f0a827.jpg 处理失败",
                "reason": "暴露内部文件名的调试信息",
                "action": "删除或改为通用错误提示"
            }
        ],
        
        "中优先级移除": [
            {
                "target": "正在处理第 1/1 张照片...",
                "reason": "可能是调试信息，需要确认是否为用户需要的进度提示",
                "action": "检查是否可以改为更友好的进度显示"
            },
            {
                "target": "处理照片: [文件名].jpg",
                "reason": "暴露内部文件名",
                "action": "删除或改为通用描述"
            }
        ],
        
        "检查项": [
            {
                "target": "所有包含文件名的调试输出",
                "reason": "避免暴露内部文件结构",
                "action": "全局搜索并清理"
            },
            {
                "target": "所有包含'步骤X:'的输出",
                "reason": "确保没有遗漏的处理步骤调试信息",
                "action": "全局搜索并清理"
            }
        ]
    }
    
    for category, items in removal_plan.items():
        print(f"\n🎯 {category}:")
        for item in items:
            print(f"   📌 目标: {item['target']}")
            print(f"      原因: {item['reason']}")
            print(f"      行动: {item['action']}")
    
    return removal_plan

def main():
    """主函数"""
    print("🔍 照片处理调试信息分析")
    print("=" * 80)
    
    print("🎯 分析目标:")
    print("   用户发现了照片处理相关的调试信息")
    print("   需要找到这些调试信息的源头并制定清理计划")
    
    # 1. 分析照片处理调试信息
    photo_debug = analyze_photo_processing_debug()
    
    # 2. 搜索照片处理调试信息源头
    photo_search_results = search_photo_processing_sources()
    
    # 3. 搜索照片处理调试模式
    pattern_results = search_photo_debug_patterns()
    
    # 4. 分析AI图像生成服务
    ai_image_debug = analyze_ai_image_service()
    
    # 5. 分析照片编辑器页面
    photo_editor_debug = analyze_photo_editor_page()
    
    # 6. 生成移除计划
    removal_plan = generate_photo_debug_removal_plan()
    
    # 总结
    print(f"\n🎯 分析总结")
    print("=" * 80)
    
    print("✅ 主要发现:")
    print(f"   🔍 发现 {len(photo_debug)} 个照片处理调试信息")
    print(f"   📁 涉及 {len(photo_search_results)} 个照片处理相关文件")
    print(f"   🎯 主要问题：照片处理调试信息泄露到用户界面")
    
    print(f"\n⚠️ 问题严重性:")
    print("   🔴 高：详细错误信息和处理步骤调试")
    print("   🟡 中：内部文件名和处理进度信息")
    print("   🟢 低：部分进度提示可能是用户需要的")
    
    print(f"\n💡 解决建议:")
    print("   1. 立即移除所有处理步骤调试信息")
    print("   2. 删除详细的技术错误信息")
    print("   3. 隐藏内部文件名信息")
    print("   4. 将进度信息改为用户友好格式")
    
    print(f"\n🎉 结论:")
    print("   ✅ 成功定位了照片处理调试信息的源头")
    print("   📋 制定了详细的清理计划")
    print("   🎯 需要清理照片处理相关的调试输出")

if __name__ == "__main__":
    main()
