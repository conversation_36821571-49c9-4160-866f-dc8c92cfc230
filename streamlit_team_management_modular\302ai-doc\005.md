# Removebg-V2（背景消除V2）

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /302/submit/removebg-v2:
    post:
      summary: Removebg-V2（背景消除V2）
      deprecated: false
      description: >-
        302.AI的API，来自于我们在云GPU上自己部署的模型。部分模型为开源模型，部分模型为我们自己微调或开发的。


        背景消除。

        平均运行时长3-5s

        **价格：0.01 PTC/次**



        ![image.png](https://api.apifox.com/api/v1/projects/4012774/resources/435331/image-preview)
      tags:
        - 图片处理/302.AI
      parameters:
        - name: Authorization
          in: header
          description: ''
          required: true
          example: Bearer {{YOUR_API_KEY}}
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                image_url:
                  type: string
              required:
                - image_url
              x-apifox-orders:
                - image_url
            example:
              image_url: ''
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  completed_at:
                    type: string
                  created_at:
                    type: string
                  error:
                    type: string
                  id:
                    type: string
                  model:
                    type: string
                  output:
                    type: string
                  started_at:
                    type: string
                  status:
                    type: string
                required:
                  - completed_at
                  - created_at
                  - error
                  - id
                  - model
                  - output
                  - started_at
                  - status
                x-apifox-orders:
                  - completed_at
                  - created_at
                  - error
                  - id
                  - model
                  - output
                  - started_at
                  - status
              example:
                completed_at: ''
                created_at: ''
                error: ''
                id: ''
                model: ''
                output: ''
                started_at: ''
                status: ''
          headers: {}
          x-apifox-name: OK
      security: []
      x-apifox-folder: 图片处理/302.AI
      x-apifox-status: released
      x-run-in-apifox: https://app.apifox.com/web/project/4012774/apis/api-195966451-run
components:
  schemas: {}
  securitySchemes: {}
servers:
  - url: https://api.302.ai
    description: 正式环境
  - url: https://api.302ai.cn
    description: 国内中转
security: []

```