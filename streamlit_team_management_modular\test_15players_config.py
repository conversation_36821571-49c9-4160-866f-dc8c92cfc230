#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试15人模板配置
"""

import sys
import os
import json
import tempfile
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入测试配置
from config.settings_test_15players import test_app_settings
from word_generator_service import WordGeneratorService

def test_15players_template():
    """测试15人模板配置"""
    print("🧪 开始测试15人模板配置...")
    
    # 获取配置路径
    word_config = test_app_settings.word_generator
    paths = word_config.get_absolute_paths("test_user", test_app_settings.paths)
    
    print(f"📁 JAR路径: {paths['jar_path']}")
    print(f"📄 模板路径: {paths['template_path']}")
    print(f"📂 输出目录: {paths['output_dir']}")
    
    # 检查文件是否存在
    jar_exists = os.path.exists(paths['jar_path'])
    template_exists = os.path.exists(paths['template_path'])
    
    print(f"✅ JAR文件存在: {jar_exists}")
    print(f"✅ 模板文件存在: {template_exists}")
    
    if not jar_exists:
        print("❌ JAR文件不存在，无法继续测试")
        return False
        
    if not template_exists:
        print("❌ 15人模板文件不存在，无法继续测试")
        return False
    
    # 创建Word生成服务
    try:
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        print("✅ WordGeneratorService创建成功")
    except Exception as e:
        print(f"❌ WordGeneratorService创建失败: {e}")
        return False
    
    # 准备测试数据
    team_data = {
        'name': '测试Python配置15人模板队',
        'leader': '张三',
        'coach': '李四',
        'doctor': '王五'
    }
    
    players_data = [
        {
            'name': '张雷',
            'jersey_number': '10',
            'photo': 'word_zc/ai-football-generator/java_word_photos/player1.png'
        },
        {
            'name': '李明',
            'jersey_number': '9',
            'photo': 'word_zc/ai-football-generator/java_word_photos/player2.jpg'
        }
    ]
    
    print("📝 开始生成Word报名表...")
    
    # 生成Word文档
    try:
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print(f"🎉 Word报名表生成成功!")
            print(f"📄 文件路径: {result['file_path']}")
            print(f"⚽ 球队名称: {result['team_name']}")
            print(f"👥 球员数量: {result['player_count']}")
            
            # 检查生成的文件是否存在
            if os.path.exists(result['file_path']):
                file_size = os.path.getsize(result['file_path'])
                print(f"📊 文件大小: {file_size} bytes")
                return True
            else:
                print("❌ 生成的文件不存在")
                return False
        else:
            print(f"❌ Word报名表生成失败: {result['message']}")
            if 'error' in result:
                print(f"🔍 错误详情: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ 生成过程出错: {e}")
        return False

if __name__ == "__main__":
    success = test_15players_template()
    if success:
        print("\n🎯 测试结论: Python配置方法可以成功使用15人模板!")
    else:
        print("\n❌ 测试失败: Python配置方法存在问题")
