#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试AI导出数据中的"自动填充"问题
"""

import os
import sys
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def find_ai_export_files():
    """查找所有AI导出文件"""
    print("=" * 60)
    print("🔍 查找AI导出文件")
    print("=" * 60)
    
    ai_export_files = []
    
    # 查找data目录下的AI导出文件
    data_dir = "data"
    if os.path.exists(data_dir):
        for root, dirs, files in os.walk(data_dir):
            for file in files:
                if file.endswith("_ai_export.json"):
                    file_path = os.path.join(root, file)
                    ai_export_files.append(file_path)
    
    print(f"📄 找到 {len(ai_export_files)} 个AI导出文件:")
    for file_path in ai_export_files:
        print(f"   {file_path}")
    
    return ai_export_files

def analyze_ai_export_file(file_path):
    """分析单个AI导出文件"""
    print(f"\n" + "=" * 60)
    print(f"🔍 分析文件: {os.path.basename(file_path)}")
    print("=" * 60)
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📄 文件结构:")
        print_dict_structure(data, indent=0)
        
        # 检查是否有"自动填充"字符串
        auto_fill_found = find_auto_fill_strings(data)
        
        if auto_fill_found:
            print(f"\n❌ 发现 {len(auto_fill_found)} 个'自动填充'字符串:")
            for path, value in auto_fill_found:
                print(f"   {path}: '{value}'")
        else:
            print(f"\n✅ 未发现'自动填充'字符串")
        
        # 检查颜色字段
        color_info = extract_color_info(data)
        if color_info:
            print(f"\n🎨 颜色信息:")
            for path, value in color_info:
                print(f"   {path}: '{value}'")
        
        return data, auto_fill_found, color_info
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return None, [], []

def print_dict_structure(data, indent=0):
    """打印字典结构"""
    prefix = "  " * indent
    
    if isinstance(data, dict):
        for key, value in data.items():
            if isinstance(value, (dict, list)):
                print(f"{prefix}{key}:")
                print_dict_structure(value, indent + 1)
            else:
                print(f"{prefix}{key}: '{value}'")
    elif isinstance(data, list):
        for i, item in enumerate(data):
            print(f"{prefix}[{i}]:")
            print_dict_structure(item, indent + 1)

def find_auto_fill_strings(data, path=""):
    """递归查找'自动填充'字符串"""
    auto_fill_found = []
    
    if isinstance(data, dict):
        for key, value in data.items():
            current_path = f"{path}.{key}" if path else key
            if isinstance(value, str) and value == "自动填充":
                auto_fill_found.append((current_path, value))
            elif isinstance(value, (dict, list)):
                auto_fill_found.extend(find_auto_fill_strings(value, current_path))
    elif isinstance(data, list):
        for i, item in enumerate(data):
            current_path = f"{path}[{i}]"
            if isinstance(item, str) and item == "自动填充":
                auto_fill_found.append((current_path, item))
            elif isinstance(item, (dict, list)):
                auto_fill_found.extend(find_auto_fill_strings(item, current_path))
    
    return auto_fill_found

def extract_color_info(data, path=""):
    """提取颜色信息"""
    color_info = []
    color_keywords = ["color", "颜色", "jersey", "shorts", "socks", "goalkeeper"]
    
    if isinstance(data, dict):
        for key, value in data.items():
            current_path = f"{path}.{key}" if path else key
            if any(keyword in key.lower() for keyword in color_keywords):
                if isinstance(value, str):
                    color_info.append((current_path, value))
            elif isinstance(value, (dict, list)):
                color_info.extend(extract_color_info(value, current_path))
    elif isinstance(data, list):
        for i, item in enumerate(data):
            current_path = f"{path}[{i}]"
            if isinstance(item, (dict, list)):
                color_info.extend(extract_color_info(item, current_path))
    
    return color_info

def create_fixed_ai_export_data():
    """创建修复后的AI导出数据示例"""
    print(f"\n" + "=" * 60)
    print("🔧 创建修复后的AI导出数据示例")
    print("=" * 60)
    
    fixed_data = {
        "team_info": {
            "name": "修复测试队",
            "ai_extracted_info": {
                "basic_info": {
                    "team_name": "修复测试队",
                    "contact_person": "张三",
                    "contact_phone": "13812345678",
                    "leader_name": "张三",  # 不再是"自动填充"
                    "team_doctor": "张三"   # 不再是"自动填充"
                },
                "kit_colors": {
                    "jersey_color": "粉色",
                    "shorts_color": "黑色",
                    "socks_color": "粉色",
                    "goalkeeper_kit_color": "绿色"
                },
                "additional_info": {
                    "coach_name": "张三"  # 不再是"自动填充"
                }
            }
        }
    }
    
    print(f"📄 修复后的数据结构:")
    print_dict_structure(fixed_data)
    
    # 保存修复后的示例文件
    output_file = "data/fixed_example_ai_export.json"
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(fixed_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 修复示例已保存: {output_file}")
    
    return fixed_data

def test_workflow_with_fixed_data():
    """测试使用修复后数据的工作流"""
    print(f"\n" + "=" * 60)
    print("🧪 测试使用修复后数据的工作流")
    print("=" * 60)
    
    # 模拟工作流的数据处理逻辑
    fixed_data = create_fixed_ai_export_data()
    
    ai_extracted_info = fixed_data["team_info"]["ai_extracted_info"]
    basic_info = ai_extracted_info.get("basic_info", {})
    kit_colors = ai_extracted_info.get("kit_colors", {})
    additional_info = ai_extracted_info.get("additional_info", {})
    
    def is_valid_value(value):
        """检查值是否有效（不是占位符）"""
        if not value or value in ["待定", "未知", "暂无", "", "自动填充"]:
            return False
        return True

    def auto_fill_with_contact(value, contact_person):
        """自动填充逻辑：如果值是'自动填充'，则使用联系人信息"""
        if value == "自动填充":
            return contact_person
        elif is_valid_value(value):
            return value
        return None
    
    # 获取联系人信息
    contact_person = basic_info.get("contact_person", "")
    
    # 应用自动填充逻辑
    team_data = {"name": "修复测试队"}
    
    # 联系人信息
    if is_valid_value(contact_person):
        team_data["contact_person"] = contact_person
    if is_valid_value(basic_info.get("contact_phone")):
        team_data["contact_phone"] = basic_info.get("contact_phone")
    
    # 人员信息自动填充逻辑
    leader_value = auto_fill_with_contact(basic_info.get("leader_name"), contact_person)
    if leader_value:
        team_data["leader"] = leader_value
    
    coach_value = auto_fill_with_contact(additional_info.get("coach_name"), contact_person)
    if coach_value:
        team_data["coach"] = coach_value
    
    doctor_value = auto_fill_with_contact(basic_info.get("team_doctor"), contact_person)
    if doctor_value:
        team_data["doctor"] = doctor_value
    
    # 颜色字段合并
    for color_field in ["jersey_color", "shorts_color", "socks_color", "goalkeeper_kit_color"]:
        if is_valid_value(kit_colors.get(color_field)):
            team_data[color_field] = kit_colors.get(color_field)
    
    print(f"📄 处理后的team_data:")
    for key, value in team_data.items():
        print(f"   {key}: '{value}'")
    
    # 检查是否还有"自动填充"
    auto_fill_check = []
    for key, value in team_data.items():
        if value == "自动填充":
            auto_fill_check.append(key)
    
    if auto_fill_check:
        print(f"\n❌ 仍有'自动填充'字段: {auto_fill_check}")
    else:
        print(f"\n✅ 所有字段都已正确填充，无'自动填充'字符串")
    
    return team_data

def main():
    """主函数"""
    print("🚀 调试AI导出数据中的'自动填充'问题")
    print(f"调试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 查找AI导出文件
        ai_export_files = find_ai_export_files()
        
        # 2. 分析每个文件
        total_auto_fill = 0
        for file_path in ai_export_files:
            data, auto_fill_found, color_info = analyze_ai_export_file(file_path)
            total_auto_fill += len(auto_fill_found)
        
        # 3. 创建修复示例
        test_workflow_with_fixed_data()
        
        print("\n" + "=" * 60)
        print("📋 调试总结")
        print("=" * 60)
        
        print(f"📊 统计信息:")
        print(f"   分析文件数: {len(ai_export_files)}")
        print(f"   发现'自动填充'总数: {total_auto_fill}")
        
        if total_auto_fill > 0:
            print(f"\n🎯 问题根源:")
            print(f"   AI导出数据中存储了'自动填充'字符串")
            print(f"   这些字符串被直接传递到Word模板中")
            
            print(f"\n💡 解决方案:")
            print(f"   1. 修改AI服务，不要存储'自动填充'字符串")
            print(f"   2. 在工作流中增强is_valid_value函数，过滤'自动填充'")
            print(f"   3. 确保自动填充逻辑在数据存储前就完成")
        else:
            print(f"\n✅ 未发现'自动填充'字符串问题")
            print(f"   问题可能在其他环节")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
