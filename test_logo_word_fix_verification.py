#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
队徽到Word修复验证测试
Logo to Word Fix Verification Test

验证修复后的AI队徽到Word集成功能
"""

import os
import sys
import json
import time
from datetime import datetime
from typing import Dict, Any

# 添加项目路径
sys.path.append('streamlit_team_management_modular')

class LogoWordFixVerificationTester:
    """队徽到Word修复验证测试器"""
    
    def __init__(self):
        self.test_team_name = "修复验证测试队"
        self.test_results = {}
        
    def run_fix_verification_test(self):
        """运行修复验证测试"""
        
        print("🔧 队徽到Word修复验证测试")
        print("=" * 80)
        print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 测试球队: {self.test_team_name}")
        print()
        
        # 测试步骤
        test_steps = [
            ("1. 工作流程服务初始化", self.test_workflow_service_init),
            ("2. 队徽生成和数据创建", self.test_logo_generation_with_data_creation),
            ("3. 球队数据验证", self.test_team_data_verification),
            ("4. 模拟换装数据准备", self.test_fashion_data_preparation),
            ("5. Word文档生成测试", self.test_word_document_generation),
            ("6. 队徽在Word中验证", self.test_logo_in_word_verification),
            ("7. 端到端集成验证", self.test_end_to_end_integration),
            ("8. 修复效果评估", self.test_fix_effectiveness)
        ]
        
        for step_name, test_func in test_steps:
            print(f"\n{step_name}")
            print("-" * 60)
            try:
                result = test_func()
                self.test_results[step_name] = result
                if result.get('success'):
                    print(f"✅ {step_name} - 通过")
                    if result.get('details'):
                        print(f"   📋 详情: {result['details']}")
                else:
                    print(f"❌ {step_name} - 失败: {result.get('error', '未知错误')}")
            except Exception as e:
                print(f"❌ {step_name} - 异常: {e}")
                self.test_results[step_name] = {'success': False, 'error': str(e)}
                import traceback
                traceback.print_exc()
        
        # 生成修复验证报告
        self.generate_fix_verification_report()

    def test_workflow_service_init(self):
        """测试工作流程服务初始化"""
        
        try:
            from services.fashion_workflow_service import FashionWorkflowService
            
            # 创建工作流程服务实例
            self.workflow_service = FashionWorkflowService()
            
            # 检查新增的方法是否存在
            has_create_basic_team_data = hasattr(self.workflow_service, '_create_basic_team_data')
            has_ensure_team_data_exists = hasattr(self.workflow_service, '_ensure_team_data_exists')
            
            return {
                'success': True,
                'details': f"用户ID: {getattr(self.workflow_service, 'user_id', 'N/A')}",
                'has_create_basic_team_data': has_create_basic_team_data,
                'has_ensure_team_data_exists': has_ensure_team_data_exists,
                'fix_methods_available': has_create_basic_team_data and has_ensure_team_data_exists
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_logo_generation_with_data_creation(self):
        """测试队徽生成和数据创建"""
        
        try:
            # 清理可能存在的旧数据
            self._cleanup_test_data()
            
            # 调用队徽生成方法（现在应该自动创建球队数据）
            logo_path = self.workflow_service._auto_generate_team_logo(self.test_team_name)
            
            if logo_path and os.path.exists(logo_path):
                # 分析队徽文件
                stat = os.stat(logo_path)
                
                return {
                    'success': True,
                    'logo_path': logo_path,
                    'file_size': stat.st_size,
                    'details': f"队徽文件: {os.path.basename(logo_path)} ({stat.st_size/1024:.1f}KB)"
                }
            else:
                return {
                    'success': False,
                    'error': f"队徽生成失败或文件不存在: {logo_path}"
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_team_data_verification(self):
        """测试球队数据验证"""
        
        try:
            # 检查球队数据是否已创建
            team_data = self.workflow_service.team_service.load_team_data_for_user(
                self.workflow_service.user_id, 
                self.test_team_name
            )
            
            if team_data:
                # 验证数据结构
                has_logo_path = 'logo_path' in team_data
                has_team_info = 'team_info' in team_data
                has_players = 'players' in team_data
                
                logo_path_in_team_info = False
                if has_team_info and 'logo_path' in team_data['team_info']:
                    logo_path_in_team_info = True
                
                return {
                    'success': True,
                    'team_data_exists': True,
                    'has_logo_path': has_logo_path,
                    'has_team_info': has_team_info,
                    'has_players': has_players,
                    'logo_path_in_team_info': logo_path_in_team_info,
                    'logo_path': team_data.get('logo_path', 'N/A'),
                    'details': f"球队数据已创建，包含队徽路径: {has_logo_path}"
                }
            else:
                return {
                    'success': False,
                    'error': "球队数据未创建",
                    'team_data_exists': False
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_fashion_data_preparation(self):
        """测试换装数据准备"""
        
        try:
            # 创建模拟球员数据和换装映射
            self.mock_players = [
                {
                    'id': f'player_{i}',
                    'name': f'修复测试球员{i}',
                    'jersey_number': str(i),
                    'photo': f'mock_photo_{i}.jpg'
                }
                for i in range(1, 4)  # 减少到3个球员以加快测试
            ]
            
            # 创建球员图片映射
            self.player_photo_mapping = {}
            for player in self.mock_players:
                player_id = player['id']
                fashion_photo_path = f'temp_files/fashion_result_{player_id}.jpg'
                self.player_photo_mapping[player_id] = fashion_photo_path
            
            return {
                'success': True,
                'player_count': len(self.mock_players),
                'mapping_count': len(self.player_photo_mapping),
                'details': f"准备了 {len(self.mock_players)} 名球员的换装数据"
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_word_document_generation(self):
        """测试Word文档生成"""
        
        try:
            # 获取队徽路径
            logo_path = self.test_results.get("2. 队徽生成和数据创建", {}).get('logo_path')
            
            if not logo_path:
                return {
                    'success': False,
                    'error': '队徽路径不可用，无法测试Word生成'
                }
            
            print(f"📄 使用队徽路径: {logo_path}")
            print(f"📋 使用球员映射: {len(self.player_photo_mapping)} 个球员")
            
            # 调用Word文档生成方法
            word_result = self.workflow_service._auto_generate_word_document(
                self.test_team_name,
                self.player_photo_mapping,
                logo_path
            )
            
            if word_result.get('success'):
                word_file_path = word_result.get('file_path')
                
                # 检查Word文件是否存在
                if word_file_path and os.path.exists(word_file_path):
                    stat = os.stat(word_file_path)
                    
                    return {
                        'success': True,
                        'word_file_path': word_file_path,
                        'file_size': stat.st_size,
                        'details': f"Word文件: {os.path.basename(word_file_path)} ({stat.st_size/1024:.1f}KB)",
                        'word_result': word_result
                    }
                else:
                    return {
                        'success': False,
                        'error': f"Word文件生成成功但文件不存在: {word_file_path}",
                        'word_result': word_result
                    }
            else:
                return {
                    'success': False,
                    'error': f"Word生成失败: {word_result.get('error', '未知错误')}",
                    'word_result': word_result
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_logo_in_word_verification(self):
        """测试队徽在Word中的验证"""
        
        try:
            word_test_result = self.test_results.get("5. Word文档生成测试", {})
            team_data_result = self.test_results.get("3. 球队数据验证", {})
            
            if not word_test_result.get('success'):
                return {
                    'success': False,
                    'error': 'Word文档生成失败，无法验证队徽'
                }
            
            word_file_path = word_test_result.get('word_file_path')
            logo_path = team_data_result.get('logo_path')
            
            # 检查文件是否存在
            word_exists = os.path.exists(word_file_path) if word_file_path else False
            logo_exists = os.path.exists(logo_path) if logo_path else False
            
            # 检查球队数据中是否包含队徽路径
            has_logo_in_team_data = team_data_result.get('has_logo_path', False)
            
            return {
                'success': word_exists and logo_exists and has_logo_in_team_data,
                'word_file_exists': word_exists,
                'logo_file_exists': logo_exists,
                'has_logo_in_team_data': has_logo_in_team_data,
                'word_file_path': word_file_path,
                'logo_file_path': logo_path,
                'details': f"Word存在: {word_exists}, 队徽存在: {logo_exists}, 数据包含队徽: {has_logo_in_team_data}"
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_end_to_end_integration(self):
        """测试端到端集成"""
        
        try:
            # 统计所有测试结果
            total_tests = len(self.test_results)
            successful_tests = sum(1 for result in self.test_results.values() if result.get('success'))
            
            # 检查关键步骤
            logo_generated = self.test_results.get("2. 队徽生成和数据创建", {}).get('success', False)
            team_data_created = self.test_results.get("3. 球队数据验证", {}).get('success', False)
            word_generated = self.test_results.get("5. Word文档生成测试", {}).get('success', False)
            logo_verified = self.test_results.get("6. 队徽在Word中验证", {}).get('success', False)
            
            # 检查修复方法是否可用
            fix_methods_available = self.test_results.get("1. 工作流程服务初始化", {}).get('fix_methods_available', False)
            
            integration_success = logo_generated and team_data_created and word_generated and logo_verified
            
            return {
                'success': integration_success,
                'total_tests': total_tests,
                'successful_tests': successful_tests,
                'success_rate': successful_tests / total_tests * 100,
                'logo_generated': logo_generated,
                'team_data_created': team_data_created,
                'word_generated': word_generated,
                'logo_verified': logo_verified,
                'fix_methods_available': fix_methods_available,
                'details': f"端到端集成 {'成功' if integration_success else '失败'}: {successful_tests}/{total_tests} 步骤通过"
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_fix_effectiveness(self):
        """测试修复效果评估"""
        
        try:
            # 对比修复前后的状态
            integration_result = self.test_results.get("7. 端到端集成验证", {})
            
            # 修复前的问题：队徽生成成功，但Word生成失败（球队数据不存在）
            # 修复后的期望：队徽生成成功，自动创建球队数据，Word生成成功
            
            logo_generated = integration_result.get('logo_generated', False)
            team_data_created = integration_result.get('team_data_created', False)
            word_generated = integration_result.get('word_generated', False)
            fix_methods_available = integration_result.get('fix_methods_available', False)
            
            # 评估修复效果
            fix_successful = logo_generated and team_data_created and word_generated and fix_methods_available
            
            improvement_points = []
            if logo_generated:
                improvement_points.append("✅ 队徽生成功能正常")
            if team_data_created:
                improvement_points.append("✅ 自动创建球队数据功能正常")
            if word_generated:
                improvement_points.append("✅ Word生成功能正常")
            if fix_methods_available:
                improvement_points.append("✅ 修复方法已正确添加")
            
            remaining_issues = []
            if not logo_generated:
                remaining_issues.append("❌ 队徽生成仍有问题")
            if not team_data_created:
                remaining_issues.append("❌ 球队数据自动创建失败")
            if not word_generated:
                remaining_issues.append("❌ Word生成仍然失败")
            if not fix_methods_available:
                remaining_issues.append("❌ 修复方法未正确添加")
            
            return {
                'success': fix_successful,
                'fix_successful': fix_successful,
                'improvement_points': improvement_points,
                'remaining_issues': remaining_issues,
                'overall_success_rate': integration_result.get('success_rate', 0),
                'details': f"修复效果: {'成功' if fix_successful else '部分成功'}"
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _cleanup_test_data(self):
        """清理测试数据"""
        
        try:
            # 删除可能存在的测试球队数据
            user_id = getattr(self.workflow_service, 'user_id', 'default_user')
            team_file = os.path.join('streamlit_team_management_modular/data', user_id, 'teams', f'{self.test_team_name}.json')
            
            if os.path.exists(team_file):
                os.remove(team_file)
                print(f"🧹 已清理旧的测试数据: {team_file}")
                
        except Exception as e:
            print(f"⚠️ 清理测试数据时出错: {e}")

    def generate_fix_verification_report(self):
        """生成修复验证报告"""
        
        print(f"\n📋 修复验证报告")
        print("=" * 80)
        
        # 获取修复效果评估结果
        fix_result = self.test_results.get("8. 修复效果评估", {})
        
        if fix_result.get('fix_successful'):
            print("🎉 修复验证成功！")
            print("✅ AI队徽到Word集成问题已解决")
        else:
            print("⚠️ 修复验证部分成功")
            print("🔧 仍需进一步调整")
        
        # 显示改进点
        improvement_points = fix_result.get('improvement_points', [])
        if improvement_points:
            print(f"\n🚀 修复改进点:")
            for point in improvement_points:
                print(f"   {point}")
        
        # 显示剩余问题
        remaining_issues = fix_result.get('remaining_issues', [])
        if remaining_issues:
            print(f"\n🔍 剩余问题:")
            for issue in remaining_issues:
                print(f"   {issue}")
        
        # 整体成功率
        success_rate = fix_result.get('overall_success_rate', 0)
        print(f"\n📊 整体成功率: {success_rate:.1f}%")
        
        print(f"\n⏰ 验证完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 保存详细报告
        self.save_verification_report()

    def save_verification_report(self):
        """保存验证报告"""
        
        report_data = {
            'test_time': datetime.now().isoformat(),
            'test_team_name': self.test_team_name,
            'test_results': self.test_results,
            'fix_verification': {
                'fix_successful': self.test_results.get("8. 修复效果评估", {}).get('fix_successful', False),
                'overall_success_rate': self.test_results.get("8. 修复效果评估", {}).get('overall_success_rate', 0)
            }
        }
        
        report_file = f"logo_word_fix_verification_report_{int(time.time())}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"📄 详细验证报告已保存: {report_file}")

if __name__ == "__main__":
    tester = LogoWordFixVerificationTester()
    tester.run_fix_verification_test()
