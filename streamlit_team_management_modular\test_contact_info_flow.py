#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试联系人信息的完整流程：AI聊天 → 自动保存 → Word生成
"""

import sys
import os
import json
import tempfile
from pathlib import Path

def test_ai_chat_contact_extraction():
    """测试AI聊天中联系人信息提取"""
    print("🧪 测试1: AI聊天中联系人信息提取")
    print("=" * 60)
    
    try:
        # 模拟用户在AI聊天中输入联系人信息
        user_input = "我是张三，电话是13800138000，我们球队叫测试联系人队"
        
        # 测试AI服务的信息提取功能
        from services.ai_service import AIService
        from config.settings import app_settings
        
        ai_service = AIService()
        
        if ai_service.has_enhanced_features():
            print("✅ AI增强功能可用")
            
            # 测试信息提取
            extracted_info = ai_service.extract_team_info_from_text(user_input)
            print(f"📄 提取结果: {extracted_info}")
            
            # 检查是否提取到联系人信息
            if 'contact_person' in extracted_info or 'contact_phone' in extracted_info:
                print("✅ 成功提取联系人信息")
                return True
            else:
                print("⚠️ 未提取到联系人信息")
                return False
        else:
            print("⚠️ AI增强功能不可用，使用基础功能")
            return True
            
    except Exception as e:
        print(f"❌ AI聊天测试失败: {e}")
        return False

def test_data_bridge_sync():
    """测试数据桥接服务的同步功能"""
    print("\n🧪 测试2: 数据桥接服务同步")
    print("=" * 60)
    
    try:
        from services.data_bridge_service import DataBridgeService
        from services.team_service import TeamService
        from services.ai_service import AIService
        
        # 初始化服务
        team_service = TeamService()
        ai_service = AIService()
        data_bridge = DataBridgeService(team_service, ai_service)
        
        # 模拟聊天消息（包含联系人信息）
        chat_messages = [
            {"role": "user", "content": "我是张三，电话13800138000"},
            {"role": "assistant", "content": "好的，我已记录您的联系信息"},
            {"role": "user", "content": "我们球队叫测试联系人队"},
            {"role": "assistant", "content": "球队名称已记录"}
        ]
        
        # 测试信息提取
        extracted_info = data_bridge.extract_team_info_from_chat(chat_messages)
        print(f"📄 提取的球队信息: {extracted_info}")
        
        # 检查联系人信息
        has_contact_person = 'contact_person' in extracted_info
        has_contact_phone = 'contact_phone' in extracted_info
        
        print(f"✅ 联系人: {'有' if has_contact_person else '无'}")
        print(f"✅ 联系电话: {'有' if has_contact_phone else '无'}")
        
        return has_contact_person or has_contact_phone
        
    except Exception as e:
        print(f"❌ 数据桥接测试失败: {e}")
        return False

def test_word_generator_contact_mapping():
    """测试Word生成器中联系人信息的映射"""
    print("\n🧪 测试3: Word生成器联系人映射")
    print("=" * 60)
    
    try:
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 获取配置路径
        paths = app_settings.word_generator.get_absolute_paths("test_user", app_settings.paths)
        
        # 准备包含联系人信息的测试数据
        team_data = {
            'name': '测试联系人映射队',
            'leader': '张三',
            'coach': '李四',
            'doctor': '王五',
            'contact_person': '张三',  # 联系人
            'contact_phone': '13800138000'  # 联系电话
        }
        
        players_data = [
            {
                'name': '测试球员',
                'jersey_number': '10',
                'photo': 'word_zc/ai-football-generator/java_word_photos/player1.png'
            }
        ]
        
        # 创建Word生成服务
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        # 检查JSON数据准备
        json_data = word_service._prepare_json_data(team_data, players_data)
        print(f"📄 准备的JSON数据:")
        print(f"   球队信息: {json_data['teamInfo']}")
        
        # 检查是否包含联系人信息
        team_info = json_data['teamInfo']
        has_contact_in_json = 'contactPerson' in team_info or 'contactPhone' in team_info
        
        if not has_contact_in_json:
            print("⚠️ JSON数据中缺少联系人字段，需要添加映射")
            return False
        else:
            print("✅ JSON数据包含联系人信息")
            return True
            
    except Exception as e:
        print(f"❌ Word生成器测试失败: {e}")
        return False

def test_java_template_placeholders():
    """测试Java模板中的占位符处理"""
    print("\n🧪 测试4: Java模板占位符处理")
    print("=" * 60)
    
    try:
        # 检查Java源码中的占位符映射
        java_files = [
            "word_zc/ai-football-generator/src/main/java/WordGeneratorCore.java",
            "word_zc/ai-football-generator/src/main/java/FootballReportGenerator.java"
        ]
        
        placeholder_found = False
        
        for java_file in java_files:
            if os.path.exists(java_file):
                with open(java_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否有联系人相关的映射
                if 'contactPerson' in content or 'contactPhone' in content:
                    print(f"✅ {java_file} 包含联系人占位符映射")
                    placeholder_found = True
                else:
                    print(f"⚠️ {java_file} 未找到联系人占位符映射")
        
        return placeholder_found
        
    except Exception as e:
        print(f"❌ Java占位符测试失败: {e}")
        return False

def test_complete_contact_flow():
    """测试完整的联系人信息流程"""
    print("\n🧪 测试5: 完整联系人信息流程")
    print("=" * 60)
    
    try:
        # 1. 模拟AI聊天提取联系人信息
        print("📝 步骤1: 模拟AI聊天...")
        chat_data = {
            'contact_person': '张三',
            'contact_phone': '13800138000'
        }
        
        # 2. 准备球队数据（包含联系人信息）
        print("📝 步骤2: 准备球队数据...")
        team_data = {
            'name': '完整流程测试队',
            'leader': '张三',
            'coach': '李四',
            'doctor': '王五',
            **chat_data  # 合并聊天提取的联系人信息
        }
        
        players_data = [
            {
                'name': '测试球员',
                'jersey_number': '10',
                'photo': 'word_zc/ai-football-generator/java_word_photos/player1.png'
            }
        ]
        
        # 3. 生成Word文档
        print("📝 步骤3: 生成Word文档...")
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        paths = app_settings.word_generator.get_absolute_paths("test_user", app_settings.paths)
        
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print(f"✅ Word文档生成成功: {result['file_path']}")
            
            # 4. 检查生成的文件
            if os.path.exists(result['file_path']):
                file_size = os.path.getsize(result['file_path'])
                print(f"📊 文件大小: {file_size:,} 字节")
                return True
            else:
                print("❌ 生成的文件不存在")
                return False
        else:
            print(f"❌ Word文档生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        return False

def analyze_current_logic():
    """分析当前的逻辑实现"""
    print("\n📊 当前逻辑分析")
    print("=" * 60)
    
    analysis = {
        "AI聊天提取": "✅ 支持从聊天中提取联系人信息",
        "数据桥接": "✅ 支持将聊天数据同步到球队系统",
        "Word生成映射": "❓ 需要检查是否正确映射联系人字段",
        "Java占位符": "❓ 需要检查Java代码中的占位符处理",
        "模板占位符": "✅ 模板中有{{contactPerson}}和{{contactPhone}}"
    }
    
    print("🔍 功能模块分析:")
    for module, status in analysis.items():
        print(f"   {module}: {status}")
    
    print("\n💡 预期流程:")
    print("1. 用户在AI聊天中输入: '我是张三，电话13800138000'")
    print("2. AI自动提取并保存联系人信息")
    print("3. 数据同步到球队系统")
    print("4. 生成Word时自动填入{{contactPerson}}和{{contactPhone}}")
    
    return analysis

def main():
    """主测试函数"""
    print("🎯 测试联系人信息的完整流程")
    print("=" * 70)
    
    # 分析当前逻辑
    analysis = analyze_current_logic()
    
    # 运行测试
    tests = [
        ("AI聊天联系人提取", test_ai_chat_contact_extraction),
        ("数据桥接同步", test_data_bridge_sync),
        ("Word生成器映射", test_word_generator_contact_mapping),
        ("Java占位符处理", test_java_template_placeholders),
        ("完整流程测试", test_complete_contact_flow)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n📊 测试结果汇总")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    # 结论
    if passed >= 4:
        print("\n🎉 结论: 联系人信息流程基本完整！")
        print("💡 用户在AI聊天中输入联系人和电话后，系统会自动保存并传入Word模板")
    elif passed >= 2:
        print("\n⚠️ 结论: 联系人信息流程部分实现")
        print("💡 需要完善某些环节以实现完整的自动化流程")
    else:
        print("\n❌ 结论: 联系人信息流程需要重新设计")
        print("💡 当前实现可能不支持自动保存和传入Word模板")

if __name__ == "__main__":
    main()
