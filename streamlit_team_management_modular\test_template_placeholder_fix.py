#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试和修复模板中的占位符格式问题
"""

import zipfile
import xml.etree.ElementTree as ET
import re
import os
import shutil
import tempfile

def analyze_template_xml_structure():
    """分析模板XML结构中的占位符问题"""
    print("🔍 分析模板XML结构中的占位符")
    print("=" * 60)
    
    template_path = "../word_zc/template_15players_fixed.docx"
    
    try:
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                print("📄 搜索联系人占位符的XML结构:")
                
                # 查找包含contact的XML片段
                contact_patterns = [
                    r'contact[^}]*}',
                    r'\{\{[^}]*contact[^}]*\}\}',
                    r'\{\{[^}]*contact[^}]*',
                    r'contact[^}]*\}\}',
                    r'联系人[^}]*}',
                    r'电话[^}]*}'
                ]
                
                for pattern in contact_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        print(f"   找到模式 '{pattern}': {matches}")
                
                # 查找XML中被分割的占位符
                print(f"\n📄 查找被分割的占位符:")
                
                # 查找所有包含contact的XML元素
                try:
                    root = ET.fromstring(content)
                    
                    contact_elements = []
                    for elem in root.iter():
                        if elem.text and ('contact' in elem.text.lower() or '联系' in elem.text):
                            contact_elements.append({
                                'tag': elem.tag,
                                'text': elem.text,
                                'parent': elem.getparent().tag if elem.getparent() is not None else None
                            })
                    
                    if contact_elements:
                        print("   找到包含联系人信息的XML元素:")
                        for elem in contact_elements:
                            print(f"     标签: {elem['tag']}, 文本: '{elem['text']}', 父标签: {elem['parent']}")
                    
                    # 查找可能的占位符片段
                    placeholder_fragments = []
                    for elem in root.iter():
                        if elem.text:
                            if '{{' in elem.text or '}}' in elem.text or 'contact' in elem.text.lower():
                                placeholder_fragments.append(elem.text)
                    
                    if placeholder_fragments:
                        print(f"\n   可能的占位符片段:")
                        for fragment in placeholder_fragments:
                            print(f"     '{fragment}'")
                    
                except ET.ParseError as e:
                    print(f"   XML解析错误: {e}")
                
                # 查找原始XML中的联系人相关内容
                print(f"\n📄 原始XML中的联系人相关内容:")
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if 'contact' in line.lower() or '联系' in line:
                        print(f"   第{i+1}行: {line.strip()}")
                
                return content
                
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def create_fixed_template():
    """创建修复后的模板"""
    print("\n🔧 创建修复后的模板")
    print("=" * 60)
    
    template_path = "../word_zc/template_15players_fixed.docx"
    fixed_template_path = "../word_zc/template_15players_fixed.docx"
    
    try:
        # 复制原模板
        shutil.copy2(template_path, fixed_template_path)
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 解压模板
            with zipfile.ZipFile(fixed_template_path, 'r') as zip_file:
                zip_file.extractall(temp_dir)
            
            # 读取document.xml
            doc_xml_path = os.path.join(temp_dir, 'word', 'document.xml')
            with open(doc_xml_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print("📄 原始内容中的联系人相关部分:")
            
            # 查找并显示需要修复的部分
            contact_lines = []
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'contact' in line.lower() or '联系' in line:
                    contact_lines.append((i+1, line))
                    print(f"   第{i+1}行: {line.strip()}")
            
            # 修复占位符
            print(f"\n🔧 修复占位符:")
            
            # 修复可能的占位符格式问题
            fixes = [
                # 修复被分割的占位符
                (r'\{\{\s*contactPerson\s*\}\s*\}', '{{contactPerson}}'),
                (r'\{\{\s*contactPhone\s*\}\s*\}', '{{contactPhone}}'),
                (r'\{\s*\{\s*contactPerson\s*\}\s*\}', '{{contactPerson}}'),
                (r'\{\s*\{\s*contactPhone\s*\}\s*\}', '{{contactPhone}}'),
                # 修复XML中可能的分割
                (r'contact\s*Person\s*\}', 'contactPerson}'),
                (r'contact\s*Phone\s*\}', 'contactPhone}'),
                # 修复空格问题
                (r'\{\{\s+contactPerson\s+\}\}', '{{contactPerson}}'),
                (r'\{\{\s+contactPhone\s+\}\}', '{{contactPhone}}'),
            ]
            
            original_content = content
            for pattern, replacement in fixes:
                new_content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
                if new_content != content:
                    print(f"   应用修复: {pattern} -> {replacement}")
                    content = new_content
            
            # 如果没有找到占位符，手动添加
            if '{{contactPerson}}' not in content and '{{contactPhone}}' not in content:
                print(f"   未找到占位符，尝试手动添加...")
                
                # 查找联系人相关的位置
                if '联系人' in content and '电话' in content:
                    # 尝试在联系人后面添加占位符
                    content = re.sub(
                        r'(联系人\s*[:：]\s*)[^<]*',
                        r'\1{{contactPerson}}',
                        content
                    )
                    content = re.sub(
                        r'(电话\s*[:：]\s*)[^<]*',
                        r'\1{{contactPhone}}',
                        content
                    )
                    print(f"   手动添加了联系人和电话占位符")
            
            # 保存修复后的内容
            with open(doc_xml_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 重新打包
            with zipfile.ZipFile(fixed_template_path, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_path = os.path.relpath(file_path, temp_dir)
                        zip_file.write(file_path, arc_path)
            
            print(f"✅ 创建修复后的模板: {fixed_template_path}")
            
            # 验证修复效果
            return verify_fixed_template(fixed_template_path)
            
    except Exception as e:
        print(f"❌ 创建修复模板失败: {e}")
        return False

def verify_fixed_template(template_path):
    """验证修复后的模板"""
    print(f"\n🔍 验证修复后的模板")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 检查占位符
                has_contact_person = '{{contactPerson}}' in content
                has_contact_phone = '{{contactPhone}}' in content
                
                print(f"📄 修复后的占位符检查:")
                print(f"   {{{{contactPerson}}}}: {'✅ 找到' if has_contact_person else '❌ 未找到'}")
                print(f"   {{{{contactPhone}}}}: {'✅ 找到' if has_contact_phone else '❌ 未找到'}")
                
                if has_contact_person and has_contact_phone:
                    print("✅ 模板修复成功")
                    
                    # 显示修复后的联系人相关内容
                    lines = content.split('\n')
                    for i, line in enumerate(lines):
                        if '{{contactPerson}}' in line or '{{contactPhone}}' in line:
                            print(f"   第{i+1}行: {line.strip()}")
                    
                    return True
                else:
                    print("❌ 模板修复失败")
                    return False
                
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def test_fixed_template():
    """测试修复后的模板"""
    print(f"\n🧪 测试修复后的模板")
    print("=" * 60)
    
    fixed_template_path = "../word_zc/template_15players_fixed.docx"
    
    if not os.path.exists(fixed_template_path):
        print("❌ 修复后的模板不存在")
        return False
    
    try:
        # 创建测试数据
        test_data = {
            "teamInfo": {
                "title": "修复模板测试报名表",
                "organizationName": "修复模板测试队",
                "teamLeader": "张三",
                "coach": "李四",
                "teamDoctor": "王五",
                "contactPerson": "赵六",
                "contactPhone": "13800138000"
            },
            "players": [
                {
                    "number": "10",
                    "name": "测试球员",
                    "photoPath": "java_word_photos/player1.png"
                }
            ],
            "config": {
                "templatePath": fixed_template_path,
                "outputDir": "../word_zc/ai-football-generator/output",
                "photosDir": "java_word_photos"
            }
        }
        
        # 写入测试文件
        test_file = "test_fixed_template.json"
        with open(test_file, 'w', encoding='utf-8') as f:
            import json
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建测试文件: {test_file}")
        print(f"📄 使用修复后的模板: {fixed_template_path}")
        
        # 运行Java程序
        import subprocess
        result = subprocess.run(
            ["java", "-cp", "target/word-generator.jar", "CommandLineMain", test_file],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=30,
            errors='ignore',
            cwd="../word_zc/ai-football-generator"
        )
        
        print(f"📊 Java返回码: {result.returncode}")
        
        if result.returncode == 0:
            print("✅ Java程序运行成功")
            
            # 检查生成的文件
            output_dir = "../word_zc/ai-football-generator/output"
            if os.path.exists(output_dir):
                files = [f for f in os.listdir(output_dir) if f.endswith('.docx')]
                if files:
                    latest_file = max([os.path.join(output_dir, f) for f in files], 
                                    key=os.path.getmtime)
                    print(f"📄 生成文件: {latest_file}")
                    
                    # 检查生成文件的内容
                    return check_generated_file_content(latest_file)
        else:
            print(f"❌ Java程序运行失败")
            if result.stderr:
                print("错误信息:")
                print(result.stderr[:500])
        
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists("test_fixed_template.json"):
            os.remove("test_fixed_template.json")

def check_generated_file_content(file_path):
    """检查生成文件的内容"""
    print(f"\n🔍 检查生成文件内容")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    # 检查联系人信息
                    has_contact_person = "赵六" in full_text
                    has_contact_phone = "13800138000" in full_text
                    has_placeholder_person = "{{contactPerson}}" in content
                    has_placeholder_phone = "{{contactPhone}}" in content
                    
                    print(f"📄 内容检查结果:")
                    print(f"   联系人'赵六': {'✅ 找到' if has_contact_person else '❌ 未找到'}")
                    print(f"   电话'13800138000': {'✅ 找到' if has_contact_phone else '❌ 未找到'}")
                    print(f"   占位符{{{{contactPerson}}}}: {'⚠️ 仍存在' if has_placeholder_person else '✅ 已替换'}")
                    print(f"   占位符{{{{contactPhone}}}}: {'⚠️ 仍存在' if has_placeholder_phone else '✅ 已替换'}")
                    
                    if has_contact_person and has_contact_phone:
                        print("🎉 修复成功！联系人信息正确显示")
                        
                        # 显示联系人相关的上下文
                        words = full_text.split()
                        for i, word in enumerate(words):
                            if "赵六" in word or "13800138000" in word:
                                start = max(0, i-5)
                                end = min(len(words), i+6)
                                context = ' '.join(words[start:end])
                                print(f"   上下文: ...{context}...")
                                break
                        
                        return True
                    else:
                        print("❌ 联系人信息仍未正确显示")
                        return False
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return False
                
    except Exception as e:
        print(f"❌ 检查文件内容失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 模板占位符修复测试")
    print("=" * 70)
    
    # 1. 分析模板XML结构
    template_content = analyze_template_xml_structure()
    
    # 2. 创建修复后的模板
    template_fixed = create_fixed_template()
    
    # 3. 测试修复后的模板
    if template_fixed:
        test_result = test_fixed_template()
        
        print(f"\n📊 最终结果")
        print("=" * 70)
        
        if test_result:
            print("🎉 模板修复完全成功！")
            print("✅ 联系人信息现在能够正确显示在Word文档中")
            print("💡 建议使用修复后的模板替换原模板")
        else:
            print("⚠️ 模板修复部分成功")
            print("💡 需要进一步调试模板引擎的占位符处理")
    else:
        print("❌ 模板修复失败")
        print("💡 需要手动检查和修复模板文件")

if __name__ == "__main__":
    main()
