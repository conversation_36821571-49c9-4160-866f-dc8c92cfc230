#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析联系人信息映射的缺失环节
"""

import sys
import os
import json

def analyze_word_generator_mapping():
    """分析Word生成器中的字段映射"""
    print("🔍 分析Word生成器字段映射")
    print("=" * 60)
    
    try:
        from word_generator_service import WordGeneratorService
        
        # 查看_prepare_json_data方法的源码
        import inspect
        source = inspect.getsource(WordGeneratorService._prepare_json_data)
        
        print("📄 当前_prepare_json_data方法:")
        print(source)
        
        # 检查是否包含联系人字段映射
        has_contact_person = 'contact_person' in source or 'contactPerson' in source
        has_contact_phone = 'contact_phone' in source or 'contactPhone' in source
        
        print(f"\n📊 字段映射检查:")
        print(f"   联系人字段: {'✅ 有' if has_contact_person else '❌ 无'}")
        print(f"   联系电话字段: {'✅ 有' if has_contact_phone else '❌ 无'}")
        
        return has_contact_person and has_contact_phone
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def analyze_java_placeholder_mapping():
    """分析Java代码中的占位符映射"""
    print("\n🔍 分析Java占位符映射")
    print("=" * 60)
    
    java_files = [
        "../word_zc/ai-football-generator/src/main/java/WordGeneratorCore.java",
        "../word_zc/ai-football-generator/src/main/java/FootballReportGenerator.java",
        "../word_zc/ai-football-generator/src/main/java/PythonIntegrationAdapter.java"
    ]
    
    contact_mappings = []
    
    for java_file in java_files:
        if os.path.exists(java_file):
            try:
                with open(java_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"\n📄 检查文件: {java_file}")
                
                # 检查联系人相关的映射
                if 'contactPerson' in content:
                    print("   ✅ 找到 contactPerson 映射")
                    contact_mappings.append('contactPerson')
                else:
                    print("   ❌ 未找到 contactPerson 映射")
                
                if 'contactPhone' in content:
                    print("   ✅ 找到 contactPhone 映射")
                    contact_mappings.append('contactPhone')
                else:
                    print("   ❌ 未找到 contactPhone 映射")
                
                # 查找prepareTemplateData方法
                if 'prepareTemplateData' in content:
                    print("   📝 找到 prepareTemplateData 方法")
                    
                    # 提取方法内容
                    start = content.find('prepareTemplateData')
                    if start != -1:
                        # 找到方法的大致范围
                        method_content = content[start:start+2000]
                        print("   📄 方法内容片段:")
                        print("   " + method_content[:500] + "...")
                
            except Exception as e:
                print(f"   ❌ 读取文件失败: {e}")
    
    return len(contact_mappings) >= 2

def test_template_placeholders():
    """测试模板中的占位符"""
    print("\n🔍 测试模板占位符")
    print("=" * 60)
    
    # 创建测试JSON数据，包含联系人信息
    test_data = {
        "teamInfo": {
            "title": "测试占位符报名表",
            "organizationName": "测试占位符队",
            "teamLeader": "张三",
            "coach": "李四",
            "teamDoctor": "王五",
            "contactPerson": "张三",  # 添加联系人
            "contactPhone": "13800138000"  # 添加联系电话
        },
        "players": [
            {
                "number": "10",
                "name": "测试球员",
                "photoPath": "java_word_photos/player1.png"
            }
        ],
        "config": {
            "templatePath": "../word_zc/template_15players_fixed.docx",
            "outputDir": "output",
            "photosDir": "java_word_photos"
        }
    }
    
    # 写入测试文件
    test_file = "test_contact_placeholders.json"
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 创建测试文件: {test_file}")
    print("📄 测试数据包含:")
    print(f"   联系人: {test_data['teamInfo']['contactPerson']}")
    print(f"   联系电话: {test_data['teamInfo']['contactPhone']}")
    
    # 尝试使用Java程序生成Word
    try:
        import subprocess
        
        print("\n🚀 运行Java Word生成器...")
        result = subprocess.run(
            ["java", "-cp", "../word_zc/ai-football-generator/target/word-generator.jar", 
             "CommandLineMain", test_file],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=30,
            cwd="../word_zc/ai-football-generator"
        )
        
        print(f"📊 返回码: {result.returncode}")
        
        if result.stdout:
            print("📝 输出:")
            print(result.stdout)
        
        if result.stderr:
            print("⚠️ 错误:")
            print(result.stderr)
        
        # 检查是否生成了文件
        output_dir = "../word_zc/ai-football-generator/output"
        if os.path.exists(output_dir):
            files = [f for f in os.listdir(output_dir) if f.endswith('.docx')]
            if files:
                latest_file = max([os.path.join(output_dir, f) for f in files], 
                                key=os.path.getmtime)
                print(f"✅ 生成文件: {latest_file}")
                return True
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Java测试失败: {e}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)

def analyze_missing_links():
    """分析缺失的环节"""
    print("\n🔍 分析缺失的环节")
    print("=" * 60)
    
    missing_links = []
    
    # 1. 检查AI提取的数据格式
    print("1. AI提取数据格式:")
    print("   当前: extracted_info['raw_text'] 包含JSON字符串")
    print("   需要: 解析JSON并提取contact_person和contact_phone")
    missing_links.append("AI提取数据解析")
    
    # 2. 检查Word生成器映射
    print("\n2. Word生成器字段映射:")
    print("   当前: 只映射 teamLeader, coach, teamDoctor")
    print("   需要: 添加 contactPerson, contactPhone 映射")
    missing_links.append("Word生成器字段映射")
    
    # 3. 检查Java占位符处理
    print("\n3. Java占位符处理:")
    print("   当前: 可能缺少 contactPerson, contactPhone 的数据映射")
    print("   需要: 在prepareTemplateData中添加这两个字段")
    missing_links.append("Java占位符处理")
    
    # 4. 检查数据流转
    print("\n4. 数据流转:")
    print("   AI聊天 → 提取信息 → 数据桥接 → 球队数据 → Word生成")
    print("   问题: 联系人信息在某个环节丢失")
    missing_links.append("数据流转完整性")
    
    return missing_links

def generate_fix_recommendations():
    """生成修复建议"""
    print("\n💡 修复建议")
    print("=" * 60)
    
    recommendations = [
        {
            "问题": "Word生成器缺少联系人字段映射",
            "文件": "word_generator_service.py",
            "方法": "_prepare_json_data",
            "修复": "在team_info中添加contactPerson和contactPhone字段"
        },
        {
            "问题": "Java代码缺少联系人占位符处理",
            "文件": "WordGeneratorCore.java",
            "方法": "prepareTemplateData",
            "修复": "添加data.put('contactPerson', ...)和data.put('contactPhone', ...)"
        },
        {
            "问题": "AI提取的数据格式需要解析",
            "文件": "enhanced_ai_service.py",
            "方法": "_extract_team_info_from_text",
            "修复": "解析JSON字符串并提取结构化数据"
        }
    ]
    
    for i, rec in enumerate(recommendations, 1):
        print(f"\n{i}. {rec['问题']}")
        print(f"   📁 文件: {rec['文件']}")
        print(f"   🔧 方法: {rec['方法']}")
        print(f"   💡 修复: {rec['修复']}")
    
    return recommendations

def main():
    """主函数"""
    print("🎯 联系人信息映射详细分析")
    print("=" * 70)
    
    # 运行分析
    tests = [
        ("Word生成器映射", analyze_word_generator_mapping),
        ("Java占位符映射", analyze_java_placeholder_mapping),
        ("模板占位符测试", test_template_placeholders)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 分析异常: {e}")
            results.append((test_name, False))
    
    # 分析缺失环节
    missing_links = analyze_missing_links()
    
    # 生成修复建议
    recommendations = generate_fix_recommendations()
    
    # 总结
    print("\n📊 分析总结")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"🎯 测试结果: {passed}/{total} 项通过")
    print(f"🔗 缺失环节: {len(missing_links)} 个")
    print(f"💡 修复建议: {len(recommendations)} 条")
    
    print("\n🎯 结论:")
    if passed == 0:
        print("❌ 联系人信息流程完全缺失，需要全面实现")
        print("💡 主要问题: Word生成器和Java代码都缺少联系人字段处理")
    elif passed < total:
        print("⚠️ 联系人信息流程部分实现，需要补充缺失环节")
        print("💡 主要问题: 某些环节缺少联系人字段映射")
    else:
        print("✅ 联系人信息流程基本完整")
        print("💡 可能只需要微调即可正常工作")

if __name__ == "__main__":
    main()
