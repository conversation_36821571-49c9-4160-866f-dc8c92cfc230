#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实用户数据中的字段缺失问题
"""

import os
import sys
import json
import zipfile
import xml.etree.ElementTree as ET
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from word_generator_service import WordGeneratorService
from config.settings import app_settings
from utils.debug_utils import debug

def analyze_real_user_data():
    """分析真实用户数据中的字段问题"""
    print("=" * 60)
    print("🔍 分析真实用户数据中的字段问题")
    print("=" * 60)
    
    data_dir = "data"
    user_dirs = [d for d in os.listdir(data_dir) if d.startswith('user_')]
    
    print(f"📁 找到用户目录: {len(user_dirs)}个")
    
    color_field_stats = {
        "basic_info_has_colors": 0,
        "kit_colors_has_colors": 0,
        "total_workflows": 0,
        "missing_kit_colors_section": 0
    }
    
    for user_dir in user_dirs:
        user_path = os.path.join(data_dir, user_dir)
        fashion_workflow_dir = os.path.join(user_path, "fashion_workflow")
        
        if not os.path.exists(fashion_workflow_dir):
            continue
            
        print(f"\n📂 用户: {user_dir}")
        
        workflow_files = [f for f in os.listdir(fashion_workflow_dir) 
                         if f.startswith('workflow_') and f.endswith('.json')]
        
        for workflow_file in workflow_files:
            workflow_path = os.path.join(fashion_workflow_dir, workflow_file)
            color_field_stats["total_workflows"] += 1
            
            try:
                with open(workflow_path, 'r', encoding='utf-8') as f:
                    workflow_data = json.load(f)
                
                team_name = workflow_file.replace('workflow_', '').replace('.json', '').split('_')[0]
                print(f"   📄 {team_name}:")
                
                ai_export_data = workflow_data.get("ai_export_data", {})
                if ai_export_data:
                    team_info = ai_export_data.get("team_info", {})
                    ai_extracted_info = team_info.get("ai_extracted_info", {})
                    
                    basic_info = ai_extracted_info.get("basic_info", {})
                    kit_colors = ai_extracted_info.get("kit_colors", {})
                    
                    # 检查basic_info中是否有颜色字段
                    basic_colors = [basic_info.get(f) for f in ["jersey_color", "shorts_color", "socks_color", "goalkeeper_kit_color"]]
                    basic_has_colors = any(c for c in basic_colors if c and c != "MISSING")
                    
                    # 检查kit_colors中是否有颜色字段
                    kit_color_values = [kit_colors.get(f) for f in ["jersey_color", "shorts_color", "socks_color", "goalkeeper_kit_color"]]
                    kit_has_colors = any(c for c in kit_color_values if c and c != "MISSING")
                    
                    if basic_has_colors:
                        color_field_stats["basic_info_has_colors"] += 1
                        print(f"      ✅ basic_info中有颜色字段")
                    
                    if kit_has_colors:
                        color_field_stats["kit_colors_has_colors"] += 1
                        print(f"      ✅ kit_colors中有颜色字段")
                        for field in ["jersey_color", "shorts_color", "socks_color", "goalkeeper_kit_color"]:
                            value = kit_colors.get(field, "MISSING")
                            if value and value != "MISSING":
                                print(f"         {field}: '{value}'")
                    else:
                        if not kit_colors:
                            color_field_stats["missing_kit_colors_section"] += 1
                            print(f"      ❌ 缺少kit_colors部分")
                        else:
                            print(f"      ⚠️ kit_colors部分存在但无颜色数据")
                
            except Exception as e:
                print(f"      ❌ 读取失败: {e}")
    
    print(f"\n📊 统计结果:")
    print(f"   总工作流文件: {color_field_stats['total_workflows']}")
    print(f"   basic_info中有颜色: {color_field_stats['basic_info_has_colors']}")
    print(f"   kit_colors中有颜色: {color_field_stats['kit_colors_has_colors']}")
    print(f"   缺少kit_colors部分: {color_field_stats['missing_kit_colors_section']}")
    
    return color_field_stats

def test_fashion_workflow_service_with_real_data():
    """使用真实数据测试fashion_workflow_service"""
    print("\n" + "=" * 60)
    print("🔍 使用真实数据测试fashion_workflow_service")
    print("=" * 60)
    
    # 查找一个有kit_colors数据的真实workflow文件
    data_dir = "data"
    user_dirs = [d for d in os.listdir(data_dir) if d.startswith('user_')]
    
    real_workflow_data = None
    real_team_name = None
    
    for user_dir in user_dirs:
        user_path = os.path.join(data_dir, user_dir)
        fashion_workflow_dir = os.path.join(user_path, "fashion_workflow")
        
        if not os.path.exists(fashion_workflow_dir):
            continue
            
        workflow_files = [f for f in os.listdir(fashion_workflow_dir) 
                         if f.startswith('workflow_') and f.endswith('.json')]
        
        for workflow_file in workflow_files:
            workflow_path = os.path.join(fashion_workflow_dir, workflow_file)
            
            try:
                with open(workflow_path, 'r', encoding='utf-8') as f:
                    workflow_data = json.load(f)
                
                ai_export_data = workflow_data.get("ai_export_data", {})
                if ai_export_data:
                    team_info = ai_export_data.get("team_info", {})
                    ai_extracted_info = team_info.get("ai_extracted_info", {})
                    kit_colors = ai_extracted_info.get("kit_colors", {})
                    
                    # 找到有颜色数据的workflow
                    if kit_colors and any(kit_colors.get(f) for f in ["jersey_color", "shorts_color", "socks_color", "goalkeeper_kit_color"]):
                        real_workflow_data = ai_export_data
                        real_team_name = workflow_file.replace('workflow_', '').replace('.json', '').split('_')[0]
                        print(f"✅ 找到真实数据: {real_team_name}")
                        break
                        
            except Exception as e:
                continue
        
        if real_workflow_data:
            break
    
    if not real_workflow_data:
        print("❌ 未找到包含颜色数据的真实workflow")
        return
    
    # 模拟当前错误的合并逻辑
    print(f"\n🔍 测试当前的合并逻辑 (错误版本):")
    ai_extracted_info = real_workflow_data["team_info"]["ai_extracted_info"]
    basic_info = ai_extracted_info.get("basic_info", {})
    kit_colors = ai_extracted_info.get("kit_colors", {})
    
    team_data_wrong = {"name": real_team_name}
    
    def is_valid_value(value):
        if not value or value in ["待定", "未知", "暂无", "", "自动填充"]:
            return False
        return True
    
    # 当前错误的逻辑（从basic_info读取）
    for color_field in ["jersey_color", "shorts_color", "socks_color", "goalkeeper_kit_color"]:
        if is_valid_value(basic_info.get(color_field)):
            team_data_wrong[color_field] = basic_info.get(color_field)
            print(f"   ✅ 设置{color_field}: '{basic_info.get(color_field)}'")
        else:
            print(f"   ❌ {color_field}未设置（从basic_info中未找到有效值）")
    
    # 正确的逻辑（从kit_colors读取）
    print(f"\n🔍 测试正确的合并逻辑:")
    team_data_correct = {"name": real_team_name}
    
    for color_field in ["jersey_color", "shorts_color", "socks_color", "goalkeeper_kit_color"]:
        if is_valid_value(kit_colors.get(color_field)):
            team_data_correct[color_field] = kit_colors.get(color_field)
            print(f"   ✅ 设置{color_field}: '{kit_colors.get(color_field)}'")
        else:
            print(f"   ⚠️ {color_field}未设置（kit_colors中无有效值）")
    
    print(f"\n📊 对比结果:")
    print(f"   错误逻辑设置的颜色字段数: {len([k for k in team_data_wrong.keys() if 'color' in k])}")
    print(f"   正确逻辑设置的颜色字段数: {len([k for k in team_data_correct.keys() if 'color' in k])}")
    
    return team_data_wrong, team_data_correct

def check_word_document_content(docx_path):
    """检查Word文档内容中的字段填充情况"""
    print(f"\n🔍 检查Word文档内容: {os.path.basename(docx_path)}")
    
    if not os.path.exists(docx_path):
        print(f"❌ 文件不存在: {docx_path}")
        return
    
    try:
        # 解压docx文件并读取document.xml
        with zipfile.ZipFile(docx_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        print(f"📄 文档内容分析:")
        
        # 检查颜色字段是否被填充
        color_fields = {
            "jerseyColor": "球衣颜色",
            "shortsColor": "球裤颜色", 
            "socksColor": "球袜颜色",
            "goalkeeperKitColor": "守门员服装颜色"
        }
        
        for field, display_name in color_fields.items():
            # 检查是否还有未替换的占位符
            placeholder = f"{{{{{field}}}}}"
            if placeholder in content:
                print(f"   ❌ {display_name}: 占位符未被替换 ({placeholder})")
            else:
                # 尝试找到可能的颜色值
                if any(color in content for color in ["红色", "蓝色", "绿色", "黄色", "白色", "黑色", "粉色"]):
                    print(f"   ✅ {display_name}: 可能已填充颜色值")
                else:
                    print(f"   ⚠️ {display_name}: 状态不明确")
        
        # 检查联系人信息
        contact_fields = {
            "contactPerson": "联系人",
            "contactPhone": "联系电话"
        }
        
        for field, display_name in contact_fields.items():
            placeholder = f"{{{{{field}}}}}"
            if placeholder in content:
                print(f"   ❌ {display_name}: 占位符未被替换 ({placeholder})")
            else:
                print(f"   ✅ {display_name}: 可能已填充")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查文档内容失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试真实用户数据中的字段问题")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 分析真实用户数据
        stats = analyze_real_user_data()
        
        # 2. 测试fashion_workflow_service
        test_fashion_workflow_service_with_real_data()
        
        # 3. 检查最近生成的Word文档
        output_dir = "data/comprehensive_test/word_output"
        if os.path.exists(output_dir):
            docx_files = [f for f in os.listdir(output_dir) if f.endswith('.docx')]
            if docx_files:
                latest_docx = max(docx_files, key=lambda f: os.path.getctime(os.path.join(output_dir, f)))
                check_word_document_content(os.path.join(output_dir, latest_docx))
        
        print("\n" + "=" * 60)
        print("📋 最终测试总结")
        print("=" * 60)
        
        print("🔍 确认的问题:")
        print("   1. ✅ 颜色数据存储在kit_colors中")
        print("   2. ❌ fashion_workflow_service从basic_info读取颜色（错误）")
        print("   3. ✅ WordGeneratorService映射逻辑正确")
        print("   4. ✅ Java程序工作正常")
        
        print(f"\n📊 数据统计:")
        print(f"   kit_colors中有颜色数据的比例: {stats['kit_colors_has_colors']}/{stats['total_workflows']}")
        
        print(f"\n🎯 需要修复的代码位置:")
        print(f"   文件: services/fashion_workflow_service.py")
        print(f"   方法: _auto_generate_word_document")
        print(f"   问题: 颜色字段从basic_info读取，应该从kit_colors读取")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
