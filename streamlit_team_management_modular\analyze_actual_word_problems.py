#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析实际生成的Word文档中的所有问题
基于联系人信息修复成功的经验
"""

import zipfile
import xml.etree.ElementTree as ET
import os
import json

def analyze_latest_generated_word():
    """分析最新生成的Word文档"""
    print("🔍 分析最新生成的Word文档中的所有问题")
    print("=" * 60)
    
    # 查找最新的Word文档
    user_data_path = "data/user_44ecbeed9db2/word_output"
    
    if not os.path.exists(user_data_path):
        print(f"❌ Word输出路径不存在: {user_data_path}")
        return None
    
    # 找到最新的Word文件
    word_files = [f for f in os.listdir(user_data_path) if f.endswith('.docx')]
    if not word_files:
        print(f"❌ 未找到Word文件")
        return None
    
    latest_word_file = max([os.path.join(user_data_path, f) for f in word_files], 
                          key=os.path.getmtime)
    
    print(f"📄 分析文件: {os.path.basename(latest_word_file)}")
    
    try:
        with zipfile.ZipFile(latest_word_file, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    print(f"📄 Word文档完整内容:")
                    print(f"   {full_text}")
                    
                    return analyze_all_field_problems(full_text, content)
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return None
                
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def analyze_all_field_problems(full_text, xml_content):
    """分析所有字段问题"""
    print(f"\n🔍 详细分析所有字段问题")
    print("=" * 60)
    
    problems = []
    
    # 1. 分析团队名称问题
    print(f"📋 1. 团队名称分析:")
    if "足球队" in full_text and "天依003" not in full_text:
        print(f"   ❌ 问题: 显示为'足球队'而不是实际团队名'天依003'")
        print(f"   💡 原因: organizationName字段被替换为默认值")
        problems.append({
            'field': 'organizationName',
            'expected': '天依003',
            'actual': '足球队',
            'issue': '团队名称被替换为默认值'
        })
    
    # 2. 分析"自动填充"问题
    print(f"\n📋 2. '自动填充'问题分析:")
    auto_fill_count = full_text.count("自动填充")
    if auto_fill_count > 0:
        print(f"   ❌ 问题: 发现{auto_fill_count}个'自动填充'字样")
        print(f"   💡 原因: AI数据中的占位符值被直接使用")
        
        # 分析具体哪些字段是"自动填充"
        words = full_text.split()
        for i, word in enumerate(words):
            if "自动填充" in word:
                start = max(0, i-3)
                end = min(len(words), i+3)
                context = ' '.join(words[start:end])
                print(f"     发现: {context}")
        
        problems.append({
            'field': 'multiple',
            'expected': '实际人员姓名',
            'actual': '自动填充',
            'issue': 'AI数据中使用了占位符值而不是实际数据'
        })
    
    # 3. 分析颜色字段问题
    print(f"\n📋 3. 颜色字段分析:")
    color_areas = []
    words = full_text.split()
    for i, word in enumerate(words):
        if any(keyword in word for keyword in ['球衣', '球裤', '球袜', '守门员']):
            start = max(0, i-2)
            end = min(len(words), i+8)
            context = ' '.join(words[start:end])
            color_areas.append(context)
    
    for area in color_areas:
        print(f"   颜色区域: {area}")
        if not any(color in area for color in ['红', '蓝', '白', '黄', '绿', '黑', '色']):
            print(f"   ❌ 问题: 该区域缺少颜色信息")
    
    problems.append({
        'field': 'colors',
        'expected': '具体颜色',
        'actual': '空白',
        'issue': '颜色字段映射缺失'
    })
    
    # 4. 分析球员信息问题
    print(f"\n📋 4. 球员信息分析:")
    # 检查是否有球员姓名
    player_names_found = []
    common_names = ['张三', '李四', '王五', '赵六', '天依']
    for name in common_names:
        if name in full_text:
            player_names_found.append(name)
    
    print(f"   找到球员姓名: {player_names_found}")
    
    if len(player_names_found) < 2:  # 假设应该有多个球员
        print(f"   ⚠️ 可能问题: 球员信息较少，可能存在数据传递问题")
        problems.append({
            'field': 'players',
            'expected': '多个球员信息',
            'actual': f'仅{len(player_names_found)}个球员',
            'issue': '球员数据可能传递不完整'
        })
    
    # 5. 分析占位符残留问题
    print(f"\n📋 5. 占位符残留分析:")
    import re
    remaining_placeholders = re.findall(r'\{\{[^}]+\}\}', xml_content)
    unique_remaining = list(set(remaining_placeholders))
    
    if unique_remaining:
        print(f"   ❌ 发现{len(unique_remaining)}个未替换占位符:")
        for placeholder in unique_remaining:
            print(f"     • {placeholder}")
        
        problems.append({
            'field': 'placeholders',
            'expected': '所有占位符被替换',
            'actual': f'{len(unique_remaining)}个未替换',
            'issue': '占位符映射不完整'
        })
    else:
        print(f"   ✅ 所有占位符都已替换")
    
    return problems

def compare_with_ai_data_and_team_data():
    """对比AI数据和团队数据"""
    print(f"\n🔍 对比AI数据和团队数据")
    print("=" * 60)
    
    try:
        # 加载AI数据
        ai_file = "data/user_44ecbeed9db2/enhanced_ai_data/天依003_ai_data.json"
        if os.path.exists(ai_file):
            with open(ai_file, 'r', encoding='utf-8') as f:
                ai_data = json.load(f)
            
            print(f"📄 AI数据分析:")
            extracted_info = ai_data.get('extracted_info', {})
            basic_info = extracted_info.get('basic_info', {})
            additional_info = extracted_info.get('additional_info', {})
            
            print(f"   basic_info内容:")
            for key, value in basic_info.items():
                print(f"     {key}: '{value}'")
            
            print(f"   additional_info内容:")
            for key, value in additional_info.items():
                print(f"     {key}: '{value}'")
            
            # 检查是否有"自动填充"值
            auto_fill_fields = []
            for key, value in basic_info.items():
                if value == "自动填充":
                    auto_fill_fields.append(key)
            for key, value in additional_info.items():
                if value == "自动填充":
                    auto_fill_fields.append(key)
            
            if auto_fill_fields:
                print(f"\n   ❌ 发现'自动填充'字段: {auto_fill_fields}")
                print(f"   💡 问题根源: AI数据中包含占位符值而不是实际数据")
            
            return ai_data
        else:
            print(f"❌ AI数据文件不存在")
            return None
            
    except Exception as e:
        print(f"❌ 对比失败: {e}")
        return None

def analyze_data_flow_problems():
    """分析数据流问题"""
    print(f"\n🔍 分析数据流问题")
    print("=" * 60)
    
    try:
        import streamlit as st
        
        # 设置session state
        if 'user_id' not in st.session_state:
            st.session_state.user_id = 'user_44ecbeed9db2'
        
        from services.fashion_workflow_service import FashionWorkflowService
        
        # 创建工作流服务
        workflow_service = FashionWorkflowService('user_44ecbeed9db2')
        
        # 测试AI数据加载
        print("📄 测试AI数据加载:")
        ai_data = workflow_service._load_ai_export_data("天依003")
        
        if ai_data:
            print("✅ AI数据加载成功")
            
            # 检查数据结构
            team_info = ai_data.get("team_info", {})
            ai_extracted_info = team_info.get("ai_extracted_info", {})
            basic_info = ai_extracted_info.get("basic_info", {})
            additional_info = ai_extracted_info.get("additional_info", {})
            
            print(f"\n📄 数据流分析:")
            print(f"   basic_info中的问题字段:")
            for key, value in basic_info.items():
                if value in ["自动填充", "", None]:
                    print(f"     {key}: '{value}' ❌")
                else:
                    print(f"     {key}: '{value}' ✅")
            
            print(f"\n   additional_info中的问题字段:")
            for key, value in additional_info.items():
                if value in ["自动填充", "", None]:
                    print(f"     {key}: '{value}' ❌")
                else:
                    print(f"     {key}: '{value}' ✅")
            
            return ai_data
        else:
            print("❌ AI数据加载失败")
            return None
            
    except Exception as e:
        print(f"❌ 数据流分析失败: {e}")
        return None

def generate_comprehensive_problem_report(problems, ai_data):
    """生成综合问题报告"""
    print(f"\n📊 综合问题报告")
    print("=" * 70)
    
    print(f"🎯 基于联系人信息修复成功经验的问题分析:")
    
    # 按问题类型分类
    data_mapping_problems = []
    ai_data_problems = []
    service_problems = []
    
    for problem in problems:
        if problem['issue'] in ['团队名称被替换为默认值', '颜色字段映射缺失', '占位符映射不完整']:
            service_problems.append(problem)
        elif problem['issue'] == 'AI数据中使用了占位符值而不是实际数据':
            ai_data_problems.append(problem)
        else:
            data_mapping_problems.append(problem)
    
    print(f"\n📋 1. WordGeneratorService问题 (类似联系人信息问题):")
    if service_problems:
        for problem in service_problems:
            print(f"   ❌ {problem['field']}: {problem['issue']}")
            print(f"     期望: {problem['expected']}")
            print(f"     实际: {problem['actual']}")
        
        print(f"\n   💡 修复方案 (参考联系人信息修复):")
        print(f"     1. 检查WordGeneratorService的_prepare_json_data方法")
        print(f"     2. 添加缺失字段的映射逻辑")
        print(f"     3. 确保字段名称匹配模板占位符")
    
    print(f"\n📋 2. AI数据质量问题:")
    if ai_data_problems:
        for problem in ai_data_problems:
            print(f"   ❌ {problem['field']}: {problem['issue']}")
        
        print(f"\n   💡 修复方案:")
        print(f"     1. 检查AI数据提取逻辑")
        print(f"     2. 确保提取真实数据而不是占位符")
        print(f"     3. 验证用户输入的数据质量")
    
    print(f"\n📋 3. 数据传递问题:")
    if data_mapping_problems:
        for problem in data_mapping_problems:
            print(f"   ❌ {problem['field']}: {problem['issue']}")
        
        print(f"\n   💡 修复方案 (参考联系人信息修复):")
        print(f"     1. 检查_auto_generate_word_document方法")
        print(f"     2. 确保AI数据正确合并到team_data")
        print(f"     3. 验证数据传递链路完整性")
    
    # 优先级排序
    print(f"\n🎯 修复优先级 (基于联系人信息修复经验):")
    print(f"   1. 🔥 高优先级: WordGeneratorService字段映射问题")
    print(f"      - 团队名称默认值问题")
    print(f"      - 颜色字段映射缺失")
    print(f"   2. 🔥 高优先级: AI数据'自动填充'问题")
    print(f"      - 需要提取真实数据而不是占位符")
    print(f"   3. 🔶 中优先级: 球员数据传递问题")
    print(f"      - 确保多球员数据正确传递")
    
    print(f"\n💡 修复策略 (基于成功经验):")
    print(f"   参考联系人信息修复的成功模式:")
    print(f"   1. 先修复WordGeneratorService的字段映射")
    print(f"   2. 再修复数据传递和合并逻辑")
    print(f"   3. 最后优化AI数据提取质量")

def main():
    """主函数"""
    print("🎯 分析实际生成Word文档中的所有问题")
    print("=" * 70)
    print("基于联系人信息修复成功的经验，全面分析所有字段问题")
    print("=" * 70)
    
    # 1. 分析最新生成的Word文档
    problems = analyze_latest_generated_word()
    
    if not problems:
        print("❌ 无法分析Word文档")
        return
    
    # 2. 对比AI数据和团队数据
    ai_data = compare_with_ai_data_and_team_data()
    
    # 3. 分析数据流问题
    data_flow_result = analyze_data_flow_problems()
    
    # 4. 生成综合问题报告
    generate_comprehensive_problem_report(problems, ai_data)

if __name__ == "__main__":
    main()
