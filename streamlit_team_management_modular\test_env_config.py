#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试环境变量配置方法
"""

import sys
import os
import shutil
from pathlib import Path

def create_env_based_config():
    """创建基于环境变量的配置"""
    config_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于环境变量的配置文件
"""

import os
from dataclasses import dataclass
from typing import Dict, Any


@dataclass
class WordGeneratorSettings:
    """Word生成器配置 - 支持环境变量"""
    JAR_PATH: str = "../word_zc/ai-football-generator/target/word-generator.jar"
    # 支持环境变量覆盖模板路径
    TEMPLATE_PATH: str = os.getenv("WORD_TEMPLATE_PATH", "../word_zc/ai-football-generator/template.docx")
    TEMP_DIR: str = "temp"
    MAX_FILES: int = 20
    TIMEOUT: int = 60

    def get_absolute_paths(self, user_id: str = None, paths_instance=None) -> Dict[str, str]:
        """获取绝对路径"""
        paths = {
            'jar_path': os.path.abspath(self.JAR_PATH),
            'template_path': os.path.abspath(self.TEMPLATE_PATH),
            'temp_dir': os.path.abspath(self.TEMP_DIR)
        }

        if user_id and paths_instance:
            from config.settings import PathSettings
            paths_obj = PathSettings()
            user_word_output = paths_obj.get_user_data_path(user_id, "word_output")
            paths['output_dir'] = os.path.abspath(user_word_output)
        else:
            paths['output_dir'] = os.path.abspath(self.TEMP_DIR)

        return paths


# 环境变量配置实例
env_word_settings = WordGeneratorSettings()
'''
    
    with open("config/env_settings.py", "w", encoding="utf-8") as f:
        f.write(config_content)
    
    print("✅ 已创建基于环境变量的配置文件")

def test_env_config():
    """测试环境变量配置"""
    print("🧪 测试环境变量配置方法...")
    
    # 设置环境变量
    template_path = "../word_zc/template_15players_fixed.docx"
    os.environ["WORD_TEMPLATE_PATH"] = template_path
    print(f"🔧 设置环境变量 WORD_TEMPLATE_PATH = {template_path}")
    
    try:
        # 导入环境变量配置
        from config.env_settings import env_word_settings
        from word_generator_service import WordGeneratorService
        
        # 获取配置路径
        paths = env_word_settings.get_absolute_paths("test_user")
        
        print(f"📄 当前模板路径: {paths['template_path']}")
        
        # 检查模板文件是否存在
        template_exists = os.path.exists(paths['template_path'])
        print(f"✅ 模板文件存在: {template_exists}")
        
        if not template_exists:
            print("❌ 15人模板文件不存在")
            return False
        
        # 创建Word生成服务并测试
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        # 准备测试数据
        team_data = {
            'name': '测试环境变量配置队',
            'leader': '张三',
            'coach': '李四',
            'doctor': '王五'
        }
        
        players_data = [
            {
                'name': '张雷',
                'jersey_number': '10',
                'photo': 'word_zc/ai-football-generator/java_word_photos/player1.png'
            }
        ]
        
        print("📝 开始生成Word报名表...")
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print(f"🎉 Word报名表生成成功!")
            print(f"📄 文件路径: {result['file_path']}")
            return True
        else:
            print(f"❌ Word报名表生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False
    finally:
        # 清理环境变量
        if "WORD_TEMPLATE_PATH" in os.environ:
            del os.environ["WORD_TEMPLATE_PATH"]

def main():
    """主测试函数"""
    print("🎯 开始测试环境变量配置方法\n")
    
    try:
        # 1. 创建环境变量配置
        create_env_based_config()
        
        # 2. 测试环境变量配置
        success = test_env_config()
        
        if success:
            print("\n🎯 测试结论: 环境变量配置方法可以成功使用15人模板!")
            print("💡 使用方法: 设置环境变量 WORD_TEMPLATE_PATH=../word_zc/template_15players_fixed.docx")
        else:
            print("\n❌ 测试失败: 环境变量配置方法存在问题")
            
    except Exception as e:
        print(f"❌ 测试过程出现异常: {e}")

if __name__ == "__main__":
    main()
