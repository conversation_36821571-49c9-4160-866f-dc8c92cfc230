#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面错误检测测试
Comprehensive Error Detection Test
"""

import os
import re
from collections import defaultdict

def search_error_patterns():
    """搜索所有错误模式"""
    print("🔍 全面错误检测分析")
    print("=" * 80)
    
    # 错误模式定义
    error_patterns = {
        "高风险错误": [
            r'st\.error.*Exception.*:',
            r'st\.error.*Traceback',
            r'st\.error.*File.*line',
            r'st\.error.*HTTPSConnectionPool',
            r'st\.error.*timeout',
            r'st\.error.*Connection.*failed',
            r'st\.error.*API.*key',
            r'st\.error.*token',
            r'st\.error.*password',
            r'st\.error.*secret'
        ],
        
        "中风险错误": [
            r'st\.error.*失败.*:.*\{.*\}',
            r'st\.error.*错误.*:.*\{.*\}',
            r'st\.warning.*失败.*:.*\{.*\}',
            r'st\.error.*status_code',
            r'st\.error.*response\.text',
            r'st\.error.*\.py.*line',
            r'st\.error.*调用失败',
            r'st\.warning.*调用失败'
        ],
        
        "低风险错误": [
            r'st\.error.*".*失败"',
            r'st\.warning.*".*失败"',
            r'st\.error.*".*错误"',
            r'st\.warning.*".*错误"',
            r'st\.info.*失败',
            r'st\.info.*错误'
        ],
        
        "网络和API错误": [
            r'requests\.post.*timeout',
            r'HTTPSConnectionPool',
            r'Read timed out',
            r'Connection.*refused',
            r'api\.302\.ai',
            r'response\.status_code',
            r'response\.json\(\)',
            r'requests\.exceptions'
        ],
        
        "文件和路径错误": [
            r'FileNotFoundError',
            r'PermissionError',
            r'OSError',
            r'IOError',
            r'path.*not.*found',
            r'file.*not.*exist',
            r'directory.*not.*exist'
        ]
    }
    
    return error_patterns

def scan_files_for_errors():
    """扫描文件中的错误"""
    print("📁 扫描文件中的错误信息")
    print("=" * 80)
    
    error_patterns = search_error_patterns()
    results = defaultdict(lambda: defaultdict(list))
    
    # 扫描目录
    for root, dirs, files in os.walk("streamlit_team_management_modular"):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    lines = content.split('\n')
                    
                    # 检查每种错误模式
                    for category, patterns in error_patterns.items():
                        for pattern in patterns:
                            for i, line in enumerate(lines, 1):
                                if re.search(pattern, line, re.IGNORECASE):
                                    results[category][file_path].append({
                                        'line': i,
                                        'pattern': pattern,
                                        'content': line.strip()
                                    })
                                    
                except Exception as e:
                    continue
    
    return results

def analyze_specific_error_types():
    """分析特定类型的错误"""
    print("🎯 分析特定类型的错误")
    print("=" * 80)
    
    specific_searches = {
        "API超时错误": [
            "HTTPSConnectionPool",
            "Read timed out",
            "timeout=60",
            "api.302.ai"
        ],
        
        "背景移除错误": [
            "背景移除请求失败",
            "背景移除失败",
            "步骤1背景移除失败",
            "step2_remove_background"
        ],
        
        "文件处理错误": [
            "处理照片",
            "处理失败",
            "文件不存在",
            "保存失败"
        ],
        
        "AI服务错误": [
            "AI助手",
            "AI聊天",
            "AI图像生成",
            "enhanced_ai_service"
        ],
        
        "Word生成错误": [
            "Word生成失败",
            "报名表生成",
            "word_generator",
            "docx"
        ]
    }
    
    specific_results = {}
    
    for error_type, keywords in specific_searches.items():
        specific_results[error_type] = {}
        
        for root, dirs, files in os.walk("streamlit_team_management_modular"):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        lines = content.split('\n')
                        
                        for keyword in keywords:
                            for i, line in enumerate(lines, 1):
                                if keyword in line and ('st.error' in line or 'st.warning' in line or 'st.exception' in line):
                                    if file_path not in specific_results[error_type]:
                                        specific_results[error_type][file_path] = []
                                    
                                    specific_results[error_type][file_path].append({
                                        'line': i,
                                        'keyword': keyword,
                                        'content': line.strip()
                                    })
                                    
                    except Exception:
                        continue
    
    return specific_results

def find_exception_handlers():
    """查找异常处理器"""
    print("⚠️ 查找异常处理器")
    print("=" * 80)
    
    exception_patterns = [
        r'except.*Exception.*as.*e:',
        r'except.*as.*e:',
        r'try:.*\n.*except',
        r'raise.*Exception',
        r'raise.*Error'
    ]
    
    exception_results = {}
    
    for root, dirs, files in os.walk("streamlit_team_management_modular"):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    lines = content.split('\n')
                    
                    for pattern in exception_patterns:
                        for i, line in enumerate(lines, 1):
                            if re.search(pattern, line, re.IGNORECASE):
                                if file_path not in exception_results:
                                    exception_results[file_path] = []
                                
                                # 查找接下来几行是否有错误输出
                                context_lines = []
                                for j in range(max(0, i-2), min(len(lines), i+5)):
                                    context_lines.append(f"{j+1}: {lines[j]}")
                                
                                exception_results[file_path].append({
                                    'line': i,
                                    'pattern': pattern,
                                    'content': line.strip(),
                                    'context': context_lines
                                })
                                
                except Exception:
                    continue
    
    return exception_results

def analyze_user_facing_errors():
    """分析面向用户的错误"""
    print("👤 分析面向用户的错误")
    print("=" * 80)
    
    user_error_patterns = [
        r'st\.error\(',
        r'st\.warning\(',
        r'st\.exception\(',
        r'st\.info.*失败',
        r'st\.info.*错误'
    ]
    
    user_errors = {}
    
    for root, dirs, files in os.walk("streamlit_team_management_modular"):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    lines = content.split('\n')
                    
                    for pattern in user_error_patterns:
                        for i, line in enumerate(lines, 1):
                            if re.search(pattern, line, re.IGNORECASE):
                                if file_path not in user_errors:
                                    user_errors[file_path] = []
                                
                                # 评估错误的严重性
                                severity = "🟢 低"
                                if any(keyword in line.lower() for keyword in ['exception', 'traceback', 'error:', 'failed:', 'timeout']):
                                    severity = "🔴 高"
                                elif any(keyword in line.lower() for keyword in ['status_code', 'response', 'api', 'connection']):
                                    severity = "🟡 中"
                                
                                user_errors[file_path].append({
                                    'line': i,
                                    'severity': severity,
                                    'content': line.strip()
                                })
                                
                except Exception:
                    continue
    
    return user_errors

def generate_error_report(scan_results, specific_results, exception_results, user_errors):
    """生成错误报告"""
    print("📋 生成错误报告")
    print("=" * 80)
    
    # 统计总数
    total_files = set()
    total_errors = 0
    
    for category, files in scan_results.items():
        total_files.update(files.keys())
        for file_path, errors in files.items():
            total_errors += len(errors)
    
    print(f"📊 错误统计总览:")
    print(f"   涉及文件数: {len(total_files)}")
    print(f"   错误总数: {total_errors}")
    
    # 按风险等级分类
    print(f"\n🎯 按风险等级分类:")
    for category, files in scan_results.items():
        if files:
            error_count = sum(len(errors) for errors in files.values())
            print(f"\n📂 {category} ({error_count} 个错误):")
            
            for file_path, errors in files.items():
                print(f"   📄 {file_path}")
                for error in errors[:3]:  # 只显示前3个
                    print(f"      第{error['line']}行: {error['content'][:80]}...")
                if len(errors) > 3:
                    print(f"      ... 还有 {len(errors)-3} 个错误")
    
    # 特定错误类型
    print(f"\n🎯 特定错误类型分析:")
    for error_type, files in specific_results.items():
        if files:
            error_count = sum(len(errors) for errors in files.values())
            print(f"\n📂 {error_type} ({error_count} 个错误):")
            
            for file_path, errors in files.items():
                print(f"   📄 {file_path}")
                for error in errors[:2]:  # 只显示前2个
                    print(f"      第{error['line']}行: {error['content'][:80]}...")
    
    # 用户面向错误
    print(f"\n👤 用户面向错误分析:")
    high_risk_count = 0
    medium_risk_count = 0
    low_risk_count = 0
    
    for file_path, errors in user_errors.items():
        for error in errors:
            if "🔴 高" in error['severity']:
                high_risk_count += 1
            elif "🟡 中" in error['severity']:
                medium_risk_count += 1
            else:
                low_risk_count += 1
    
    print(f"   🔴 高风险错误: {high_risk_count} 个")
    print(f"   🟡 中风险错误: {medium_risk_count} 个")
    print(f"   🟢 低风险错误: {low_risk_count} 个")

def main():
    """主函数"""
    print("🔍 全面错误检测测试")
    print("=" * 80)
    
    print("🎯 测试目标:")
    print("   全面扫描系统中所有可能的错误输出")
    print("   识别可能暴露给用户的技术错误信息")
    print("   按风险等级分类错误信息")
    
    # 1. 扫描文件中的错误
    scan_results = scan_files_for_errors()
    
    # 2. 分析特定类型错误
    specific_results = analyze_specific_error_types()
    
    # 3. 查找异常处理器
    exception_results = find_exception_handlers()
    
    # 4. 分析用户面向错误
    user_errors = analyze_user_facing_errors()
    
    # 5. 生成错误报告
    generate_error_report(scan_results, specific_results, exception_results, user_errors)
    
    # 总结
    print(f"\n🎯 检测总结")
    print("=" * 80)
    
    print("✅ 完成的检测:")
    print("   🔍 错误模式扫描")
    print("   🎯 特定错误类型分析")
    print("   ⚠️ 异常处理器检查")
    print("   👤 用户面向错误分析")
    
    print(f"\n💡 主要发现:")
    print("   📊 系统中存在多种类型的错误输出")
    print("   🔴 部分错误可能暴露技术细节")
    print("   🎯 需要重点关注高风险和中风险错误")
    
    print(f"\n🎉 结论:")
    print("   ✅ 成功完成全面错误检测")
    print("   📋 生成了详细的错误分析报告")
    print("   🎯 为错误信息优化提供了基础数据")

if __name__ == "__main__":
    main()
