#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面调试测试 - 找出所有可能的问题源
"""

import os
import sys
import json
import tempfile
import zipfile
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ai_service_directly():
    """直接测试AI服务的自动填充"""
    print("=" * 60)
    print("🔍 测试AI服务的自动填充")
    print("=" * 60)
    
    from services.ai_service import AIService
    
    ai_service = AIService()
    
    # 测试数据
    test_data = {
        "contact_person": "张三",
        "contact_phone": "13812345678",
        "jersey_color": "粉色"
    }
    
    print(f"📄 输入数据:")
    for key, value in test_data.items():
        print(f"   {key}: '{value}'")
    
    # 应用智能填充
    result = ai_service._apply_smart_fill_logic(test_data.copy())
    
    print(f"\n📄 AI服务输出:")
    for key, value in result.items():
        print(f"   {key}: '{value}'")
    
    # 检查是否有问题字符串
    problem_strings = ["自动填充", "智能搭配", "智能填充", "AI搭配"]
    problems_found = []
    
    for key, value in result.items():
        if isinstance(value, str) and any(problem in value for problem in problem_strings):
            problems_found.append((key, value))
    
    if problems_found:
        print(f"\n❌ 发现问题字符串:")
        for key, value in problems_found:
            print(f"   {key}: '{value}'")
    else:
        print(f"\n✅ AI服务输出正常")
    
    return result, problems_found

def test_enhanced_ai_assistant():
    """测试增强AI助手"""
    print(f"\n" + "=" * 60)
    print("🔍 测试增强AI助手")
    print("=" * 60)
    
    from services.enhanced_ai_assistant import EnhancedAIAssistant
    
    enhanced_ai = EnhancedAIAssistant()
    
    # 模拟基本信息和颜色信息
    basic_info = {
        "contact_person": "李四",
        "contact_phone": "13987654321",
        "leader_name": "",
        "team_doctor": ""
    }
    
    kit_colors = {
        "jersey_color": "粉色",
        "shorts_color": "",
        "socks_color": "",
        "goalkeeper_kit_color": ""
    }
    
    print(f"📄 输入基本信息:")
    for key, value in basic_info.items():
        print(f"   {key}: '{value}'")
    
    print(f"📄 输入颜色信息:")
    for key, value in kit_colors.items():
        print(f"   {key}: '{value}'")
    
    # 测试人员自动填充
    filled_basic = enhanced_ai._auto_fill_personnel(basic_info.copy())
    
    # 测试颜色自动填充
    filled_colors = enhanced_ai._auto_fill_kit_colors(kit_colors.copy())
    
    print(f"\n📄 增强AI助手输出 - 基本信息:")
    for key, value in filled_basic.items():
        print(f"   {key}: '{value}'")
    
    print(f"📄 增强AI助手输出 - 颜色信息:")
    for key, value in filled_colors.items():
        print(f"   {key}: '{value}'")
    
    # 检查问题字符串
    problem_strings = ["自动填充", "智能搭配", "智能填充", "AI搭配"]
    problems_found = []
    
    all_data = {**filled_basic, **filled_colors}
    for key, value in all_data.items():
        if isinstance(value, str) and any(problem in value for problem in problem_strings):
            problems_found.append((key, value))
    
    if problems_found:
        print(f"\n❌ 发现问题字符串:")
        for key, value in problems_found:
            print(f"   {key}: '{value}'")
    else:
        print(f"\n✅ 增强AI助手输出正常")
    
    return all_data, problems_found

def test_workflow_service():
    """测试工作流服务的数据处理"""
    print(f"\n" + "=" * 60)
    print("🔍 测试工作流服务数据处理")
    print("=" * 60)
    
    # 模拟AI导出数据
    ai_export_data = {
        "team_info": {
            "name": "全面测试队",
            "ai_extracted_info": {
                "basic_info": {
                    "team_name": "全面测试队",
                    "contact_person": "王五",
                    "contact_phone": "13666777888",
                    "leader_name": "",
                    "team_doctor": ""
                },
                "kit_colors": {
                    "jersey_color": "粉色",
                    "shorts_color": "",
                    "socks_color": "",
                    "goalkeeper_kit_color": ""
                },
                "additional_info": {
                    "coach_name": ""
                }
            }
        }
    }
    
    print(f"📄 模拟AI导出数据:")
    basic_info = ai_export_data["team_info"]["ai_extracted_info"]["basic_info"]
    kit_colors = ai_export_data["team_info"]["ai_extracted_info"]["kit_colors"]
    additional_info = ai_export_data["team_info"]["ai_extracted_info"]["additional_info"]
    
    print(f"   basic_info: {basic_info}")
    print(f"   kit_colors: {kit_colors}")
    print(f"   additional_info: {additional_info}")
    
    # 模拟工作流的数据处理逻辑
    def is_valid_value(value):
        """检查值是否有效（不是占位符）"""
        if not value or value in ["待定", "未知", "暂无", "", "自动填充", "智能搭配", "智能填充", "AI搭配"]:
            return False
        return True

    def auto_fill_with_contact(value, contact_person):
        """自动填充逻辑：如果值是占位符，则使用联系人信息"""
        if value in ["自动填充", "智能搭配", "智能填充", "AI搭配", "", None]:
            return contact_person
        elif is_valid_value(value):
            return value
        return None
    
    # 获取联系人信息
    contact_person = basic_info.get("contact_person", "")
    
    # 应用自动填充逻辑
    team_data = {"name": "全面测试队"}
    
    # 联系人信息
    if is_valid_value(contact_person):
        team_data["contact_person"] = contact_person
    if is_valid_value(basic_info.get("contact_phone")):
        team_data["contact_phone"] = basic_info.get("contact_phone")
    
    # 人员信息自动填充逻辑
    leader_value = auto_fill_with_contact(basic_info.get("leader_name"), contact_person)
    if leader_value:
        team_data["leader"] = leader_value
    
    coach_value = auto_fill_with_contact(additional_info.get("coach_name"), contact_person)
    if coach_value:
        team_data["coach"] = coach_value
    
    doctor_value = auto_fill_with_contact(basic_info.get("team_doctor"), contact_person)
    if doctor_value:
        team_data["doctor"] = doctor_value
    
    # 颜色字段处理 - 应用智能填充
    from services.ai_service import AIService
    ai_service = AIService()
    
    # 先设置球衣颜色
    if is_valid_value(kit_colors.get("jersey_color")):
        team_data["jersey_color"] = kit_colors.get("jersey_color")
    
    # 应用颜色自动填充逻辑
    color_filled = ai_service._auto_fill_kit_colors(team_data.copy())
    
    # 合并颜色数据
    for color_field in ["shorts_color", "socks_color", "goalkeeper_kit_color"]:
        if color_field in color_filled and is_valid_value(color_filled[color_field]):
            team_data[color_field] = color_filled[color_field]
    
    print(f"\n📄 工作流处理后的数据:")
    for key, value in team_data.items():
        print(f"   {key}: '{value}'")
    
    # 检查问题字符串
    problem_strings = ["自动填充", "智能搭配", "智能填充", "AI搭配"]
    problems_found = []
    
    for key, value in team_data.items():
        if isinstance(value, str) and any(problem in value for problem in problem_strings):
            problems_found.append((key, value))
    
    if problems_found:
        print(f"\n❌ 发现问题字符串:")
        for key, value in problems_found:
            print(f"   {key}: '{value}'")
    else:
        print(f"\n✅ 工作流数据处理正常")
    
    return team_data, problems_found

def test_word_generation_complete():
    """测试完整的Word生成流程"""
    print(f"\n" + "=" * 60)
    print("🔍 测试完整Word生成流程")
    print("=" * 60)
    
    # 使用前面测试的数据
    team_data, _ = test_workflow_service()
    
    players_data = [
        {"name": "测试球员1", "jersey_number": "1", "photo": ""},
        {"name": "测试球员2", "jersey_number": "2", "photo": ""}
    ]
    
    try:
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 创建WordGeneratorService
        paths = app_settings.word_generator.get_absolute_paths("comprehensive_test", app_settings.paths)
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        print(f"📄 准备生成Word文档...")
        print(f"   模板: {os.path.basename(paths['template_path'])}")
        
        # 检查准备的JSON数据
        json_data = word_service._prepare_json_data(team_data, players_data)
        team_info = json_data.get("teamInfo", {})
        
        print(f"\n📄 准备的JSON数据:")
        for key, value in team_info.items():
            print(f"   {key}: '{value}'")
        
        # 检查JSON数据中的问题字符串
        problem_strings = ["自动填充", "智能搭配", "智能填充", "AI搭配"]
        json_problems = []
        
        for key, value in team_info.items():
            if isinstance(value, str) and any(problem in value for problem in problem_strings):
                json_problems.append((key, value))
        
        if json_problems:
            print(f"\n❌ JSON数据中发现问题字符串:")
            for key, value in json_problems:
                print(f"   {key}: '{value}'")
        else:
            print(f"\n✅ JSON数据正常")
        
        # 生成Word文档
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            output_file = result['file_path']
            print(f"\n✅ Word生成成功: {os.path.basename(output_file)}")
            
            # 检查Word文档内容
            return check_word_document_content(output_file)
        else:
            print(f"\n❌ Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ Word生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_word_document_content(docx_path):
    """检查Word文档内容"""
    print(f"\n📄 检查Word文档内容:")
    
    try:
        with zipfile.ZipFile(docx_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        # 检查问题字符串
        problem_strings = ["自动填充", "智能搭配", "智能填充", "AI搭配"]
        problems_found = []
        
        for problem_string in problem_strings:
            count = content.count(problem_string)
            if count > 0:
                problems_found.append((problem_string, count))
        
        if problems_found:
            print(f"❌ Word文档中发现问题字符串:")
            for problem_string, count in problems_found:
                print(f"   '{problem_string}': {count} 次")
            
            # 查找上下文
            import re
            for problem_string, _ in problems_found:
                pattern = f'.{{0,30}}{re.escape(problem_string)}.{{0,30}}'
                matches = re.findall(pattern, content)
                print(f"\n   '{problem_string}'的上下文:")
                for i, match in enumerate(matches[:3]):
                    clean_match = match.replace('<', '&lt;').replace('>', '&gt;')
                    print(f"      {i+1}. {clean_match}")
            
            return False
        else:
            print(f"✅ Word文档中未发现问题字符串")
            
            # 检查颜色是否正确显示
            expected_colors = ["粉色", "黑色", "绿色"]
            colors_found = []
            
            for color in expected_colors:
                if color in content:
                    colors_found.append(color)
            
            print(f"🎨 颜色检查:")
            for color in expected_colors:
                status = "✅" if color in colors_found else "❌"
                print(f"   {status} {color}")
            
            return len(colors_found) == len(expected_colors)
            
    except Exception as e:
        print(f"❌ 检查Word内容失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 全面调试测试 - 找出所有可能的问题源")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 测试AI服务
        ai_result, ai_problems = test_ai_service_directly()
        
        # 2. 测试增强AI助手
        enhanced_result, enhanced_problems = test_enhanced_ai_assistant()
        
        # 3. 测试工作流服务
        workflow_result, workflow_problems = test_workflow_service()
        
        # 4. 测试完整Word生成
        word_success = test_word_generation_complete()
        
        print("\n" + "=" * 60)
        print("📋 全面测试总结")
        print("=" * 60)
        
        print(f"📊 测试结果:")
        print(f"   AI服务问题: {len(ai_problems)}")
        print(f"   增强AI助手问题: {len(enhanced_problems)}")
        print(f"   工作流服务问题: {len(workflow_problems)}")
        print(f"   Word生成成功: {'✅' if word_success else '❌'}")
        
        total_problems = len(ai_problems) + len(enhanced_problems) + len(workflow_problems)
        
        if total_problems > 0 or not word_success:
            print(f"\n🎯 发现的问题:")
            
            if ai_problems:
                print(f"   AI服务问题: {ai_problems}")
            if enhanced_problems:
                print(f"   增强AI助手问题: {enhanced_problems}")
            if workflow_problems:
                print(f"   工作流服务问题: {workflow_problems}")
            if not word_success:
                print(f"   Word生成有问题")
                
            print(f"\n💡 需要进一步调查的方向:")
            print(f"   1. 检查是否有其他地方生成占位符文本")
            print(f"   2. 检查模板文件是否包含占位符文本")
            print(f"   3. 检查Java程序是否生成占位符文本")
            print(f"   4. 检查用户实际使用的数据流程")
        else:
            print(f"\n✅ 所有测试通过!")
            print(f"   问题可能出现在特定的用户操作流程中")
            print(f"   建议检查用户实际使用的数据和流程")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
