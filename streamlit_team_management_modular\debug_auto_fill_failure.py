#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试自动填充失效问题
"""

import os
import sys
import json
import glob
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def find_user_data(team_name):
    """查找指定球队的用户数据"""
    print(f"🔍 查找球队'{team_name}'的用户数据")
    
    # 搜索所有用户目录
    user_dirs = glob.glob("data/user_*")
    found_data = []
    
    for user_dir in user_dirs:
        # 检查enhanced_ai_data目录
        ai_data_dir = os.path.join(user_dir, "enhanced_ai_data")
        if os.path.exists(ai_data_dir):
            for file in os.listdir(ai_data_dir):
                if file.endswith('.json') and team_name in file:
                    file_path = os.path.join(ai_data_dir, file)
                    found_data.append(file_path)
                    print(f"   找到AI数据: {file_path}")
        
        # 检查fashion_workflow目录
        workflow_dir = os.path.join(user_dir, "fashion_workflow")
        if os.path.exists(workflow_dir):
            for file in os.listdir(workflow_dir):
                if file.endswith('.json') and team_name in file:
                    file_path = os.path.join(workflow_dir, file)
                    found_data.append(file_path)
                    print(f"   找到工作流数据: {file_path}")
    
    return found_data

def analyze_user_data(file_path):
    """分析用户数据"""
    print(f"\n📄 分析文件: {os.path.basename(file_path)}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 提取关键信息
        if "ai_extracted_info" in data:
            # enhanced_ai_data格式
            extracted_info = data["ai_extracted_info"]
        elif "team_info" in data and "ai_extracted_info" in data["team_info"]:
            # fashion_workflow格式
            extracted_info = data["team_info"]["ai_extracted_info"]
        else:
            print("   ❌ 无法识别数据格式")
            return None
        
        basic_info = extracted_info.get("basic_info", {})
        kit_colors = extracted_info.get("kit_colors", {})
        additional_info = extracted_info.get("additional_info", {})
        
        print(f"   📋 基本信息:")
        for key, value in basic_info.items():
            print(f"      {key}: '{value}'")
        
        print(f"   🎨 颜色信息:")
        for key, value in kit_colors.items():
            print(f"      {key}: '{value}'")
        
        print(f"   ➕ 附加信息:")
        for key, value in additional_info.items():
            print(f"      {key}: '{value}'")
        
        return {
            "basic_info": basic_info,
            "kit_colors": kit_colors,
            "additional_info": additional_info
        }
        
    except Exception as e:
        print(f"   ❌ 分析失败: {e}")
        return None

def test_auto_fill_logic(extracted_info):
    """测试自动填充逻辑"""
    print(f"\n🧪 测试自动填充逻辑")
    
    try:
        from services.ai_service import AIService
        
        ai_service = AIService()
        
        # 准备测试数据
        test_data = {}
        
        # 从提取的信息中获取基础数据
        basic_info = extracted_info.get("basic_info", {})
        kit_colors = extracted_info.get("kit_colors", {})
        additional_info = extracted_info.get("additional_info", {})
        
        # 合并所有信息
        test_data.update(basic_info)
        test_data.update(kit_colors)
        test_data.update(additional_info)
        
        print(f"   📄 输入数据:")
        for key, value in test_data.items():
            print(f"      {key}: '{value}'")
        
        # 应用自动填充逻辑
        filled_data = ai_service._apply_smart_fill_logic(test_data.copy())
        
        print(f"\n   📄 自动填充后:")
        for key, value in filled_data.items():
            print(f"      {key}: '{value}'")
        
        # 检查自动填充是否生效
        auto_fill_success = False
        
        # 检查人员自动填充
        contact_person = filled_data.get("contact_person", "")
        if contact_person:
            if (filled_data.get("leader_name") == contact_person or 
                filled_data.get("team_doctor") == contact_person):
                auto_fill_success = True
                print(f"   ✅ 人员自动填充生效")
        
        # 检查颜色自动填充
        jersey_color = filled_data.get("jersey_color", "")
        if jersey_color:
            if (filled_data.get("shorts_color") or 
                filled_data.get("socks_color") or 
                filled_data.get("goalkeeper_kit_color")):
                auto_fill_success = True
                print(f"   ✅ 颜色自动填充生效")
        
        if not auto_fill_success:
            print(f"   ❌ 自动填充未生效")
        
        return filled_data, auto_fill_success
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, False

def test_workflow_processing(extracted_info):
    """测试工作流处理"""
    print(f"\n🔄 测试工作流处理")
    
    try:
        # 模拟工作流的数据处理逻辑
        def is_valid_value(value):
            """检查值是否有效（不是占位符）"""
            if not value or value in ["待定", "未知", "暂无", "", "自动填充", "智能搭配", "智能填充", "AI搭配"]:
                return False
            return True

        def auto_fill_with_contact(value, contact_person):
            """自动填充逻辑：如果值是占位符，则使用联系人信息"""
            if value in ["自动填充", "智能搭配", "智能填充", "AI搭配", "", None]:
                return contact_person
            elif is_valid_value(value):
                return value
            return None
        
        basic_info = extracted_info.get("basic_info", {})
        kit_colors = extracted_info.get("kit_colors", {})
        additional_info = extracted_info.get("additional_info", {})
        
        # 获取联系人信息
        contact_person = basic_info.get("contact_person", "")
        
        print(f"   📄 联系人: '{contact_person}'")
        print(f"   📄 is_valid_value(contact_person): {is_valid_value(contact_person)}")
        
        # 应用自动填充逻辑
        team_data = {"name": basic_info.get("team_name", "测试队")}
        
        # 联系人信息
        if is_valid_value(contact_person):
            team_data["contact_person"] = contact_person
            print(f"   ✅ 设置联系人: '{contact_person}'")
        else:
            print(f"   ❌ 联系人无效: '{contact_person}'")
        
        if is_valid_value(basic_info.get("contact_phone")):
            team_data["contact_phone"] = basic_info.get("contact_phone")
            print(f"   ✅ 设置联系电话: '{basic_info.get('contact_phone')}'")
        
        # 人员信息自动填充逻辑
        leader_value = auto_fill_with_contact(basic_info.get("leader_name"), contact_person)
        if leader_value:
            team_data["leader"] = leader_value
            print(f"   ✅ 设置领队: '{leader_value}'")
        else:
            print(f"   ❌ 领队未设置，原值: '{basic_info.get('leader_name')}'")
        
        coach_value = auto_fill_with_contact(additional_info.get("coach_name"), contact_person)
        if coach_value:
            team_data["coach"] = coach_value
            print(f"   ✅ 设置教练: '{coach_value}'")
        else:
            print(f"   ❌ 教练未设置，原值: '{additional_info.get('coach_name')}'")
        
        doctor_value = auto_fill_with_contact(basic_info.get("team_doctor"), contact_person)
        if doctor_value:
            team_data["doctor"] = doctor_value
            print(f"   ✅ 设置队医: '{doctor_value}'")
        else:
            print(f"   ❌ 队医未设置，原值: '{basic_info.get('team_doctor')}'")
        
        # 颜色字段处理
        jersey_color = kit_colors.get("jersey_color", "")
        if is_valid_value(jersey_color):
            team_data["jersey_color"] = jersey_color
            print(f"   ✅ 设置球衣颜色: '{jersey_color}'")
            
            # 应用颜色自动填充逻辑
            from services.ai_service import AIService
            ai_service = AIService()
            color_filled = ai_service._auto_fill_kit_colors(team_data.copy())
            
            # 合并颜色数据
            for color_field in ["shorts_color", "socks_color", "goalkeeper_kit_color"]:
                if color_field in color_filled and is_valid_value(color_filled[color_field]):
                    team_data[color_field] = color_filled[color_field]
                    print(f"   ✅ 自动填充{color_field}: '{color_filled[color_field]}'")
        else:
            print(f"   ❌ 球衣颜色无效: '{jersey_color}'")
        
        print(f"\n   📄 最终工作流数据:")
        for key, value in team_data.items():
            print(f"      {key}: '{value}'")
        
        return team_data
        
    except Exception as e:
        print(f"   ❌ 工作流测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_word_generation(team_data):
    """测试Word生成"""
    print(f"\n📄 测试Word生成")
    
    try:
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        players_data = [
            {"name": "测试球员1", "jersey_number": "1", "photo": ""},
            {"name": "测试球员2", "jersey_number": "2", "photo": ""}
        ]
        
        # 生成Word文档
        paths = app_settings.word_generator.get_absolute_paths("debug_auto_fill", app_settings.paths)
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            output_file = result['file_path']
            print(f"   ✅ Word生成成功: {os.path.basename(output_file)}")
            return True
        else:
            print(f"   ❌ Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"   ❌ Word生成测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 调试自动填充失效问题")
    print(f"调试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 查找用户数据
        team_name = "天依909"
        user_data_files = find_user_data(team_name)
        
        if not user_data_files:
            print(f"❌ 未找到球队'{team_name}'的数据")
            return
        
        # 2. 分析最新的用户数据
        latest_file = max(user_data_files, key=os.path.getmtime)
        extracted_info = analyze_user_data(latest_file)
        
        if not extracted_info:
            print(f"❌ 无法分析用户数据")
            return
        
        # 3. 测试自动填充逻辑
        filled_data, auto_fill_success = test_auto_fill_logic(extracted_info)
        
        # 4. 测试工作流处理
        workflow_data = test_workflow_processing(extracted_info)
        
        # 5. 测试Word生成
        if workflow_data:
            word_success = test_word_generation(workflow_data)
        else:
            word_success = False
        
        print("\n" + "=" * 60)
        print("📋 调试总结")
        print("=" * 60)
        
        print(f"📊 测试结果:")
        print(f"   找到用户数据: {'✅' if user_data_files else '❌'}")
        print(f"   自动填充逻辑: {'✅' if auto_fill_success else '❌'}")
        print(f"   工作流处理: {'✅' if workflow_data else '❌'}")
        print(f"   Word生成: {'✅' if word_success else '❌'}")
        
        if not auto_fill_success:
            print(f"\n🎯 问题诊断:")
            print(f"   自动填充逻辑失效，可能原因:")
            print(f"   1. 联系人信息为空或无效")
            print(f"   2. is_valid_value函数过度过滤")
            print(f"   3. 自动填充条件不满足")
            
            # 检查具体原因
            basic_info = extracted_info.get("basic_info", {})
            contact_person = basic_info.get("contact_person", "")
            
            if not contact_person:
                print(f"   ❌ 联系人为空")
            elif contact_person in ["待定", "未知", "暂无", "", "自动填充", "智能搭配", "智能填充", "AI搭配"]:
                print(f"   ❌ 联系人是无效值: '{contact_person}'")
            else:
                print(f"   ✅ 联系人有效: '{contact_person}'")
                print(f"   问题可能在自动填充逻辑中")
        
        print(f"\n💡 修复建议:")
        if not auto_fill_success:
            print(f"   1. 检查用户数据中的联系人信息")
            print(f"   2. 确保is_valid_value函数不会过度过滤")
            print(f"   3. 验证自动填充条件逻辑")
        print(f"   4. 确保用户重新输入完整的基本信息")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
