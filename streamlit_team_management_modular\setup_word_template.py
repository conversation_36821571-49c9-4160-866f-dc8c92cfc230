#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word模板文件设置脚本
Word Template Setup Script
"""

import os
import shutil
from pathlib import Path

def check_current_template_setup():
    """检查当前模板设置"""
    print("🔍 检查当前Word模板设置")
    print("=" * 50)
    
    # 从配置中获取模板路径
    try:
        from config.settings import app_settings
        global_paths = app_settings.word_generator.get_absolute_paths()
        
        template_path = global_paths.get('template_path', '')
        jar_path = global_paths.get('jar_path', '')
        
        print(f"配置的模板路径: {template_path}")
        print(f"配置的JAR路径: {jar_path}")
        
        # 检查文件是否存在
        template_exists = os.path.exists(template_path)
        jar_exists = os.path.exists(jar_path)
        
        print(f"模板文件存在: {'✅' if template_exists else '❌'}")
        print(f"JAR文件存在: {'✅' if jar_exists else '❌'}")
        
        if template_exists:
            template_size = os.path.getsize(template_path)
            print(f"当前模板大小: {template_size:,} 字节")
        
        return template_path, template_exists
        
    except Exception as e:
        print(f"❌ 检查配置失败: {e}")
        return None, False

def show_template_directory_structure():
    """显示模板目录结构"""
    print("\n📁 Word生成器目录结构")
    print("=" * 50)
    
    word_generator_dir = "../word_zc/ai-football-generator"
    
    if os.path.exists(word_generator_dir):
        print(f"Word生成器目录: {os.path.abspath(word_generator_dir)}")
        print("\n当前模板文件:")
        
        template_files = []
        for file in os.listdir(word_generator_dir):
            if file.endswith('.docx'):
                file_path = os.path.join(word_generator_dir, file)
                file_size = os.path.getsize(file_path)
                template_files.append((file, file_size))
                print(f"  📄 {file} ({file_size:,} 字节)")
        
        if not template_files:
            print("  ❌ 没有找到.docx模板文件")
        
        return word_generator_dir, template_files
    else:
        print(f"❌ Word生成器目录不存在: {word_generator_dir}")
        return None, []

def backup_current_template(template_path):
    """备份当前模板"""
    if not os.path.exists(template_path):
        print("⚠️ 当前模板不存在，无需备份")
        return True
    
    try:
        backup_path = template_path + ".backup"
        shutil.copy2(template_path, backup_path)
        print(f"✅ 当前模板已备份: {backup_path}")
        return True
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return False

def install_new_template(source_path, target_path):
    """安装新模板"""
    print(f"\n🔄 安装新模板")
    print("=" * 50)
    
    if not os.path.exists(source_path):
        print(f"❌ 源模板文件不存在: {source_path}")
        return False
    
    try:
        # 确保目标目录存在
        target_dir = os.path.dirname(target_path)
        os.makedirs(target_dir, exist_ok=True)
        
        # 复制文件
        shutil.copy2(source_path, target_path)
        
        # 验证复制结果
        if os.path.exists(target_path):
            source_size = os.path.getsize(source_path)
            target_size = os.path.getsize(target_path)
            
            print(f"✅ 模板安装成功!")
            print(f"   源文件: {source_path} ({source_size:,} 字节)")
            print(f"   目标文件: {target_path} ({target_size:,} 字节)")
            print(f"   大小匹配: {'✅' if source_size == target_size else '❌'}")
            
            return True
        else:
            print(f"❌ 复制后文件不存在: {target_path}")
            return False
            
    except Exception as e:
        print(f"❌ 安装失败: {e}")
        return False

def test_template_access():
    """测试模板访问"""
    print(f"\n🧪 测试模板访问")
    print("=" * 50)
    
    try:
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 获取配置路径
        global_paths = app_settings.word_generator.get_absolute_paths()
        
        # 创建Word生成器实例
        word_service = WordGeneratorService(
            jar_path=global_paths['jar_path'],
            template_path=global_paths['template_path'],
            output_dir="temp_test"
        )
        
        # 测试初始化
        if word_service.validate_environment():
            print("✅ Word生成器环境验证成功")
            print("✅ 模板文件可以正常访问")
            return True
        else:
            print("❌ Word生成器环境验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Word模板文件设置向导")
    print("=" * 60)
    
    # 1. 检查当前设置
    current_template_path, template_exists = check_current_template_setup()
    
    # 2. 显示目录结构
    word_dir, template_files = show_template_directory_structure()
    
    if not word_dir:
        print("\n❌ Word生成器目录不存在，请检查项目结构")
        return
    
    # 3. 显示安装指南
    print(f"\n📋 模板文件安装指南")
    print("=" * 50)
    
    source_template = "data/template_15players_fixed.docx"
    target_template = os.path.join(word_dir, "template.docx")
    
    print(f"您的新模板文件位置: {os.path.abspath(source_template)}")
    print(f"需要安装到的位置: {os.path.abspath(target_template)}")
    
    # 检查源文件是否存在
    if os.path.exists(source_template):
        print(f"✅ 源模板文件存在")
        
        # 4. 备份当前模板
        if template_exists:
            print(f"\n📦 备份当前模板")
            backup_current_template(current_template_path)
        
        # 5. 安装新模板
        success = install_new_template(source_template, target_template)
        
        if success:
            # 6. 测试模板访问
            test_template_access()
            
            print(f"\n🎉 模板安装完成!")
            print("=" * 50)
            print("✅ 新模板已成功安装")
            print("✅ 系统现在将使用template_15players_fixed.docx作为Word生成模板")
            print("✅ 可以开始测试Word生成功能")
            
            print(f"\n📝 下一步:")
            print("1. 重新启动Streamlit应用")
            print("2. 执行换装流程")
            print("3. 验证Word文档是否使用新模板生成")
        else:
            print(f"\n❌ 模板安装失败")
            
    else:
        print(f"❌ 源模板文件不存在: {os.path.abspath(source_template)}")
        print(f"\n💡 请将template_15players_fixed.docx文件放到以下位置:")
        print(f"   {os.path.abspath(source_template)}")
        print(f"   然后重新运行此脚本")
        
        # 显示手动安装指南
        print(f"\n🔧 手动安装指南:")
        print(f"1. 将template_15players_fixed.docx复制到:")
        print(f"   {os.path.abspath(target_template)}")
        print(f"2. 确保文件名为template.docx")
        print(f"3. 重新启动应用测试")

if __name__ == "__main__":
    main()
