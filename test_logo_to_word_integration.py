#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业级队徽到Word集成问题排查测试
Enterprise-level Logo to Word Integration Issue Investigation

系统性测试AI生成队徽后为什么没有出现在Word文件中
"""

import os
import sys
import json
import time
import shutil
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.append('streamlit_team_management_modular')

class LogoToWordIntegrationTester:
    """队徽到Word集成测试器"""
    
    def __init__(self):
        self.test_results = {}
        self.test_team_name = "集成测试队"
        self.test_start_time = datetime.now()
        
    def run_comprehensive_test(self):
        """运行全面的集成测试"""
        
        print("🔍 企业级队徽到Word集成问题排查")
        print("=" * 80)
        print(f"⏰ 测试开始时间: {self.test_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 测试目标: 找出AI生成队徽后为什么没有在Word文件中出现")
        print()
        
        # 测试步骤
        test_steps = [
            ("1. 端到端流程测试", self.test_end_to_end_workflow),
            ("2. 队徽生成验证", self.test_logo_generation),
            ("3. 数据流追踪测试", self.test_data_flow_tracking),
            ("4. Word模板分析", self.test_word_template_analysis),
            ("5. Word处理服务测试", self.test_word_processing_service),
            ("6. 文件系统集成测试", self.test_file_system_integration),
            ("7. 配置和依赖检查", self.test_configuration_dependencies),
            ("8. 错误处理验证", self.test_error_handling)
        ]
        
        for step_name, test_func in test_steps:
            print(f"\n{step_name}")
            print("-" * 60)
            try:
                result = test_func()
                self.test_results[step_name] = result
                if result.get('success'):
                    print(f"✅ {step_name} - 通过")
                else:
                    print(f"❌ {step_name} - 失败: {result.get('error', '未知错误')}")
            except Exception as e:
                print(f"❌ {step_name} - 异常: {e}")
                self.test_results[step_name] = {'success': False, 'error': str(e)}
                import traceback
                traceback.print_exc()
        
        # 生成综合报告
        self.generate_comprehensive_report()

    def test_end_to_end_workflow(self):
        """测试端到端工作流程"""
        
        print("🔄 执行完整的端到端工作流程测试...")
        
        try:
            # 导入必要的服务
            from services.fashion_workflow_service import FashionWorkflowService
            
            # 创建工作流程服务
            workflow_service = FashionWorkflowService()
            
            # 模拟完整的工作流程
            print(f"📝 开始为 '{self.test_team_name}' 执行完整工作流程...")
            
            # 检查工作流程服务的可用方法
            available_methods = [method for method in dir(workflow_service) 
                               if not method.startswith('__')]
            
            print(f"📋 工作流程服务可用方法: {len(available_methods)}")
            
            # 查找队徽相关的方法
            logo_methods = [method for method in available_methods 
                          if 'logo' in method.lower()]
            
            print(f"🎯 队徽相关方法: {logo_methods}")
            
            # 查找Word相关的方法
            word_methods = [method for method in available_methods 
                          if 'word' in method.lower() or 'document' in method.lower()]
            
            print(f"📄 Word相关方法: {word_methods}")
            
            # 尝试执行队徽生成
            logo_result = None
            if '_auto_generate_team_logo' in available_methods:
                print("🎨 执行队徽生成...")
                logo_result = workflow_service._auto_generate_team_logo(self.test_team_name)
                print(f"🖼️ 队徽生成结果: {logo_result}")
            
            # 尝试执行Word文档生成
            word_result = None
            if '_auto_generate_word_document' in available_methods:
                print("📄 执行Word文档生成...")
                # 需要准备测试数据
                test_data = self.prepare_test_data_for_word()
                word_result = workflow_service._auto_generate_word_document(test_data)
                print(f"📋 Word生成结果: {word_result}")
            
            return {
                'success': True,
                'logo_result': logo_result,
                'word_result': word_result,
                'available_methods': available_methods,
                'logo_methods': logo_methods,
                'word_methods': word_methods
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_logo_generation(self):
        """测试队徽生成功能"""
        
        print("🎨 测试队徽生成功能...")
        
        try:
            from services.enhanced_ai_service import enhanced_ai_assistant
            
            if not enhanced_ai_assistant.is_available():
                return {'success': False, 'error': 'AI服务不可用'}
            
            # 生成队徽
            logo_result = enhanced_ai_assistant._generate_team_logo({
                "team_name": self.test_team_name,
                "team_style": "现代",
                "color_preference": "蓝色"
            })
            
            if not logo_result.get('success'):
                return {'success': False, 'error': f"队徽生成失败: {logo_result.get('error')}"}
            
            logo_file_path = logo_result.get('logo_file_path')
            
            # 验证队徽文件
            if not logo_file_path or not os.path.exists(logo_file_path):
                return {'success': False, 'error': f"队徽文件不存在: {logo_file_path}"}
            
            # 分析队徽文件
            file_info = self.analyze_logo_file(logo_file_path)
            
            return {
                'success': True,
                'logo_file_path': logo_file_path,
                'logo_description': logo_result.get('logo_description'),
                'file_info': file_info
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_data_flow_tracking(self):
        """测试数据流追踪"""
        
        print("📊 测试数据流追踪...")
        
        try:
            # 检查数据存储位置
            data_locations = [
                'data',
                'streamlit_team_management_modular/data',
                'cache',
                'streamlit_team_management_modular/cache'
            ]
            
            data_flow_info = {}
            
            for location in data_locations:
                if os.path.exists(location):
                    files = os.listdir(location)
                    data_flow_info[location] = {
                        'exists': True,
                        'files': files,
                        'file_count': len(files)
                    }
                    print(f"📁 {location}: {len(files)} 个文件")
                else:
                    data_flow_info[location] = {'exists': False}
                    print(f"❌ {location}: 不存在")
            
            # 检查工作流程数据
            workflow_data_files = []
            for root, dirs, files in os.walk('.'):
                for file in files:
                    if 'workflow' in file.lower() and file.endswith('.json'):
                        workflow_data_files.append(os.path.join(root, file))
            
            print(f"🔄 找到工作流程数据文件: {len(workflow_data_files)}")
            for file in workflow_data_files:
                print(f"   - {file}")
            
            return {
                'success': True,
                'data_locations': data_flow_info,
                'workflow_data_files': workflow_data_files
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_word_template_analysis(self):
        """测试Word模板分析"""
        
        print("📄 测试Word模板分析...")
        
        try:
            # 查找Word模板文件
            template_locations = [
                'word_zc',
                'streamlit_team_management_modular/templates',
                'templates'
            ]
            
            template_files = []
            for location in template_locations:
                if os.path.exists(location):
                    for root, dirs, files in os.walk(location):
                        for file in files:
                            if file.endswith('.docx'):
                                template_files.append(os.path.join(root, file))
            
            print(f"📋 找到Word模板文件: {len(template_files)}")
            
            template_analysis = {}
            for template_file in template_files:
                print(f"🔍 分析模板: {template_file}")
                
                # 检查文件大小和修改时间
                stat = os.stat(template_file)
                file_info = {
                    'size': stat.st_size,
                    'modified': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                    'accessible': os.access(template_file, os.R_OK)
                }
                
                # 尝试分析模板内容（如果可能）
                try:
                    # 这里可以添加更详细的Word文档分析
                    # 但为了不修改主代码，我们只做基本检查
                    file_info['analysis_attempted'] = True
                except Exception as e:
                    file_info['analysis_error'] = str(e)
                
                template_analysis[template_file] = file_info
            
            return {
                'success': True,
                'template_files': template_files,
                'template_analysis': template_analysis
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def analyze_logo_file(self, logo_file_path):
        """分析队徽文件"""
        
        try:
            from PIL import Image
            
            stat = os.stat(logo_file_path)
            
            with Image.open(logo_file_path) as img:
                return {
                    'file_size': stat.st_size,
                    'dimensions': img.size,
                    'mode': img.mode,
                    'format': img.format,
                    'accessible': True,
                    'modified': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                }
        except Exception as e:
            return {'error': str(e), 'accessible': False}

    def prepare_test_data_for_word(self):
        """准备Word测试数据"""
        
        return {
            'team_name': self.test_team_name,
            'players': [
                {'name': f'测试球员{i}', 'position': '前锋', 'number': i} 
                for i in range(1, 6)
            ],
            'logo_path': None  # 这里会在实际测试中填入
        }

    def test_word_processing_service(self):
        """测试Word处理服务"""
        
        print("📝 测试Word处理服务...")
        
        try:
            # 查找Word处理相关的服务
            word_services = []
            
            # 检查可能的Word处理模块
            possible_modules = [
                'services.word_service',
                'services.document_service',
                'utils.word_utils',
                'components.word_generator'
            ]
            
            for module_name in possible_modules:
                try:
                    module = __import__(module_name, fromlist=[''])
                    word_services.append(module_name)
                    print(f"✅ 找到Word服务模块: {module_name}")
                except ImportError:
                    print(f"❌ Word服务模块不存在: {module_name}")
            
            return {
                'success': True,
                'word_services': word_services,
                'service_count': len(word_services)
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_file_system_integration(self):
        """测试文件系统集成"""
        
        print("💾 测试文件系统集成...")
        
        try:
            # 检查关键目录的权限
            key_directories = [
                'assets/logos',
                'temp_files',
                'word_zc',
                'streamlit_team_management_modular/data'
            ]
            
            directory_status = {}
            
            for directory in key_directories:
                if os.path.exists(directory):
                    status = {
                        'exists': True,
                        'readable': os.access(directory, os.R_OK),
                        'writable': os.access(directory, os.W_OK),
                        'files': len(os.listdir(directory)) if os.access(directory, os.R_OK) else 0
                    }
                else:
                    status = {'exists': False}
                
                directory_status[directory] = status
                print(f"📁 {directory}: {status}")
            
            return {
                'success': True,
                'directory_status': directory_status
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_configuration_dependencies(self):
        """测试配置和依赖"""
        
        print("⚙️ 测试配置和依赖...")
        
        try:
            # 检查Python包依赖
            required_packages = [
                'python-docx',
                'docx',
                'openpyxl',
                'PIL',
                'Pillow'
            ]
            
            package_status = {}
            
            for package in required_packages:
                try:
                    __import__(package.replace('-', '_'))
                    package_status[package] = {'installed': True}
                    print(f"✅ {package}: 已安装")
                except ImportError:
                    package_status[package] = {'installed': False}
                    print(f"❌ {package}: 未安装")
            
            return {
                'success': True,
                'package_status': package_status
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_error_handling(self):
        """测试错误处理"""
        
        print("🚨 测试错误处理...")
        
        try:
            # 检查日志文件
            log_locations = [
                'logs',
                'streamlit_team_management_modular/logs',
                '.'
            ]
            
            log_files = []
            for location in log_locations:
                if os.path.exists(location):
                    for file in os.listdir(location):
                        if file.endswith('.log') or 'log' in file.lower():
                            log_files.append(os.path.join(location, file))
            
            print(f"📋 找到日志文件: {len(log_files)}")
            
            # 分析最近的错误
            recent_errors = []
            for log_file in log_files:
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        for line in lines[-100:]:  # 检查最后100行
                            if 'error' in line.lower() or 'exception' in line.lower():
                                recent_errors.append({
                                    'file': log_file,
                                    'content': line.strip()
                                })
                except Exception:
                    pass
            
            return {
                'success': True,
                'log_files': log_files,
                'recent_errors': recent_errors[:10]  # 只返回前10个错误
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def generate_comprehensive_report(self):
        """生成综合报告"""
        
        print(f"\n📋 企业级问题排查报告")
        print("=" * 80)
        
        # 统计测试结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get('success'))
        failed_tests = total_tests - passed_tests
        
        print(f"📊 测试统计:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过: {passed_tests}")
        print(f"   失败: {failed_tests}")
        print(f"   成功率: {passed_tests/total_tests*100:.1f}%")
        
        # 关键发现
        print(f"\n🔍 关键发现:")
        
        # 分析队徽生成结果
        logo_test = self.test_results.get("2. 队徽生成验证", {})
        if logo_test.get('success'):
            logo_path = logo_test.get('logo_file_path')
            print(f"   ✅ 队徽生成成功: {logo_path}")
        else:
            print(f"   ❌ 队徽生成失败: {logo_test.get('error')}")
        
        # 分析Word模板
        template_test = self.test_results.get("4. Word模板分析", {})
        if template_test.get('success'):
            template_count = len(template_test.get('template_files', []))
            print(f"   📄 找到Word模板: {template_count}个")
        else:
            print(f"   ❌ Word模板分析失败: {template_test.get('error')}")
        
        # 分析数据流
        data_flow_test = self.test_results.get("3. 数据流追踪测试", {})
        if data_flow_test.get('success'):
            workflow_files = len(data_flow_test.get('workflow_data_files', []))
            print(f"   🔄 工作流程数据文件: {workflow_files}个")
        
        print(f"\n⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 保存详细报告
        self.save_detailed_report()

    def save_detailed_report(self):
        """保存详细报告"""
        
        report_data = {
            'test_start_time': self.test_start_time.isoformat(),
            'test_end_time': datetime.now().isoformat(),
            'test_team_name': self.test_team_name,
            'test_results': self.test_results
        }
        
        report_file = f"logo_to_word_integration_report_{int(time.time())}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"📄 详细报告已保存: {report_file}")

if __name__ == "__main__":
    tester = LogoToWordIntegrationTester()
    tester.run_comprehensive_test()
