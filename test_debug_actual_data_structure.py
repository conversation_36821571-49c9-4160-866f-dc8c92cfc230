#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试实际数据结构
Debug Actual Data Structure
"""

import os

def create_debug_display_function():
    """创建调试版本的显示函数"""
    print("🔧 创建调试版本的显示函数")
    print("=" * 80)
    
    debug_function = '''
def _display_team_info_friendly_debug(self, team_info):
    """调试版本的球队信息显示"""
    st.write("🔍 DEBUG: 显示函数接收到的team_info数据:")
    st.json(team_info)
    
    st.write("🔍 DEBUG: session_state中的extracted_team_info:")
    if "extracted_team_info" in st.session_state:
        st.json(st.session_state.extracted_team_info)
    else:
        st.write("❌ extracted_team_info 不存在于 session_state")
    
    st.write("🔍 DEBUG: session_state中所有以team开头的键:")
    team_keys = [key for key in st.session_state.keys() if 'team' in key.lower()]
    for key in team_keys:
        st.write(f"   {key}: {type(st.session_state[key])}")
    
    # 尝试原来的数据读取路径
    st.write("🔍 DEBUG: 原来的数据读取路径测试:")
    basic_info = team_info.get('ai_extracted_info', {}).get('basic_info', {})
    st.write(f"   ai_extracted_info.basic_info: {basic_info}")
    
    # 尝试可能的数据读取路径
    st.write("🔍 DEBUG: 可能的数据读取路径测试:")
    
    # 路径1: 直接从根级别读取
    if 'basic_info' in team_info:
        st.write(f"   路径1 - team_info.basic_info: {team_info['basic_info']}")
    
    # 路径2: 从extracted_info读取
    if 'extracted_info' in team_info:
        extracted = team_info['extracted_info']
        if 'basic_info' in extracted:
            st.write(f"   路径2 - team_info.extracted_info.basic_info: {extracted['basic_info']}")
    
    # 路径3: 从session_state.extracted_team_info读取
    if "extracted_team_info" in st.session_state:
        extracted_team = st.session_state.extracted_team_info
        if 'basic_info' in extracted_team:
            st.write(f"   路径3 - session_state.extracted_team_info.basic_info: {extracted_team['basic_info']}")
    
    # 如果找到了数据，尝试显示
    working_basic_info = None
    working_kit_colors = None
    
    # 按优先级尝试不同路径
    if "extracted_team_info" in st.session_state:
        extracted_team = st.session_state.extracted_team_info
        if 'basic_info' in extracted_team:
            working_basic_info = extracted_team['basic_info']
        if 'kit_colors' in extracted_team:
            working_kit_colors = extracted_team['kit_colors']
    
    if working_basic_info:
        st.write("✅ 找到可用的basic_info数据，尝试显示:")
        col1, col2 = st.columns(2)
        with col1:
            st.metric("球队名称", working_basic_info.get('team_name', '未设置'))
            st.metric("联系人", working_basic_info.get('contact_person', '未设置'))
        with col2:
            st.metric("联系电话", working_basic_info.get('contact_phone', '未设置'))
            st.metric("队长", working_basic_info.get('leader_name', '未设置'))
    
    if working_kit_colors:
        st.write("✅ 找到可用的kit_colors数据，尝试显示:")
        col1, col2 = st.columns(2)
        with col1:
            st.write(f"球衣颜色: {working_kit_colors.get('jersey_color', '未设置')}")
            st.write(f"短裤颜色: {working_kit_colors.get('shorts_color', '未设置')}")
        with col2:
            st.write(f"袜子颜色: {working_kit_colors.get('socks_color', '未设置')}")
            st.write(f"守门员球衣: {working_kit_colors.get('goalkeeper_kit_color', '未设置')}")
    '''
    
    print("📋 调试函数内容:")
    print(debug_function)
    
    return debug_function

def create_debug_modification_instructions():
    """创建调试修改指令"""
    print(f"\n📋 调试修改指令")
    print("=" * 80)
    
    instructions = {
        "步骤1": "临时添加调试函数到ai_chat.py",
        "步骤2": "修改第904行调用调试函数",
        "步骤3": "运行应用查看调试输出",
        "步骤4": "根据调试结果确定正确的数据路径",
        "步骤5": "修复原始显示函数",
        
        "具体修改": {
            "文件": "streamlit_team_management_modular/components/ai_chat.py",
            "位置": "第904行",
            "当前": "self._display_team_info_friendly(team_info)",
            "临时改为": "self._display_team_info_friendly_debug(team_info)",
            "目的": "查看实际数据结构"
        },
        
        "预期发现": [
            "extracted_team_info中的实际数据格式",
            "正确的数据读取路径",
            "键名映射关系",
            "数据层级结构"
        ],
        
        "根据发现调整": [
            "如果数据在extracted_team_info中，修改显示函数读取源",
            "如果数据结构不同，调整读取路径",
            "如果键名不匹配，更新键名映射",
            "如果数据格式正确，直接使用"
        ]
    }
    
    for key, value in instructions.items():
        print(f"\n📋 {key}:")
        if isinstance(value, dict):
            for subkey, subvalue in value.items():
                print(f"   {subkey}: {subvalue}")
        elif isinstance(value, list):
            for item in value:
                print(f"   • {item}")
        else:
            print(f"   {value}")
    
    return instructions

def analyze_expected_vs_actual():
    """分析期望与实际的数据结构"""
    print(f"\n🔍 分析期望与实际的数据结构")
    print("=" * 80)
    
    analysis = {
        "当前显示函数期望": {
            "数据源": "team_info (从session_state[team_info_{team_name}])",
            "数据路径": "team_info.get('ai_extracted_info', {}).get('basic_info', {})",
            "期望结构": {
                "ai_extracted_info": {
                    "basic_info": {
                        "team_name": "球队名称",
                        "contact_person": "联系人",
                        "contact_phone": "联系电话",
                        "leader_name": "队长"
                    },
                    "kit_colors": {
                        "jersey_color": "球衣颜色",
                        "shorts_color": "短裤颜色"
                    }
                }
            }
        },
        
        "实际可能的情况": [
            {
                "情况A": "数据在extracted_team_info中，结构正确",
                "数据源": "session_state.extracted_team_info",
                "结构": {
                    "basic_info": {"team_name": "火狐999"},
                    "kit_colors": {"jersey_color": "粉色"}
                },
                "解决": "修改显示函数从extracted_team_info读取"
            },
            {
                "情况B": "数据在extracted_team_info中，但键名不同",
                "数据源": "session_state.extracted_team_info",
                "结构": {
                    "extracted_info": {
                        "basic_info": {"team_name": "火狐999"}
                    }
                },
                "解决": "修改读取路径为extracted_info而不是ai_extracted_info"
            },
            {
                "情况C": "数据保存位置错误",
                "数据源": "session_state中的其他键",
                "结构": "数据存在但键名不是team_info_{team_name}",
                "解决": "找到正确的键名或修改保存逻辑"
            }
        ],
        
        "调试目标": [
            "确认extracted_team_info是否存在",
            "查看extracted_team_info的实际结构",
            "确认数据是否正确提取和保存",
            "找到正确的数据读取路径"
        ]
    }
    
    for category, details in analysis.items():
        print(f"\n🎯 {category}")
        if isinstance(details, dict):
            for key, value in details.items():
                if isinstance(value, dict):
                    print(f"   {key}:")
                    for subkey, subvalue in value.items():
                        print(f"      {subkey}: {subvalue}")
                else:
                    print(f"   {key}: {value}")
        elif isinstance(details, list):
            for item in details:
                if isinstance(item, dict):
                    for key, value in item.items():
                        print(f"   {key}: {value}")
                else:
                    print(f"   • {item}")
    
    return analysis

def generate_testing_plan():
    """生成测试计划"""
    print(f"\n📋 生成测试计划")
    print("=" * 80)
    
    testing_plan = {
        "测试目标": "找出AI信息显示断层的确切原因",
        
        "测试步骤": [
            {
                "步骤1": "添加调试函数",
                "操作": "在ai_chat.py中添加调试版本的显示函数",
                "预期": "能够查看实际的数据结构"
            },
            {
                "步骤2": "临时启用调试",
                "操作": "修改第904行调用调试函数",
                "预期": "在界面上看到调试输出"
            },
            {
                "步骤3": "运行应用测试",
                "操作": "启动应用，触发AI信息提取",
                "预期": "看到详细的数据结构信息"
            },
            {
                "步骤4": "分析调试结果",
                "操作": "根据调试输出确定数据结构",
                "预期": "找到正确的数据读取路径"
            },
            {
                "步骤5": "修复显示函数",
                "操作": "根据发现修改显示函数的数据读取逻辑",
                "预期": "信息正确显示"
            },
            {
                "步骤6": "移除调试代码",
                "操作": "恢复原始的显示函数调用",
                "预期": "保持修复效果"
            }
        ],
        
        "关键检查点": [
            "extracted_team_info是否存在于session_state",
            "extracted_team_info的数据结构是什么",
            "basic_info和kit_colors是否在正确位置",
            "键名是否与期望一致"
        ],
        
        "可能的发现": [
            "数据在extracted_team_info中，只需修改读取源",
            "数据结构层级不同，需要调整读取路径",
            "键名不匹配，需要更新键名映射",
            "数据未正确保存，需要修复保存逻辑"
        ]
    }
    
    for category, details in testing_plan.items():
        print(f"\n📋 {category}")
        if isinstance(details, list):
            for item in details:
                if isinstance(item, dict):
                    for key, value in item.items():
                        print(f"   {key}: {value}")
                else:
                    print(f"   • {item}")
        else:
            print(f"   {details}")
    
    return testing_plan

def main():
    """主函数"""
    print("🔍 调试实际数据结构分析")
    print("=" * 80)
    
    print("🎯 调试目标:")
    print("   查看AI信息提取后的实际数据结构")
    print("   找出显示函数读取失败的确切原因")
    print("   确定正确的数据读取路径")
    
    # 1. 创建调试显示函数
    debug_function = create_debug_display_function()
    
    # 2. 创建调试修改指令
    instructions = create_debug_modification_instructions()
    
    # 3. 分析期望与实际
    analysis = analyze_expected_vs_actual()
    
    # 4. 生成测试计划
    testing_plan = generate_testing_plan()
    
    # 总结
    print(f"\n🎯 调试策略总结")
    print("=" * 80)
    
    print("✅ 调试方法:")
    print("   🔧 添加调试函数查看实际数据")
    print("   📊 检查session_state中的所有相关数据")
    print("   🔍 测试多种可能的数据读取路径")
    print("   ✨ 根据发现修复显示逻辑")
    
    print(f"\n🔍 关键问题:")
    print("   1. extracted_team_info vs team_info_{team_name}")
    print("   2. ai_extracted_info vs extracted_info")
    print("   3. 数据结构层级是否匹配")
    print("   4. 键名映射是否正确")
    
    print(f"\n📋 下一步行动:")
    print("   1. 添加调试函数到ai_chat.py")
    print("   2. 临时启用调试显示")
    print("   3. 运行应用查看调试输出")
    print("   4. 根据发现修复显示逻辑")
    print("   5. 测试修复效果")

if __name__ == "__main__":
    main()
