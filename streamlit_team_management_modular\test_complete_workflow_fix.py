#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整工作流修复
"""

import os
import sys
import json
import zipfile
import re
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def simulate_ai_extraction_with_smart_fill():
    """模拟AI提取并应用智能填充"""
    print("=" * 60)
    print("🧪 模拟AI提取并应用智能填充")
    print("=" * 60)
    
    try:
        # 模拟用户输入："我们是天依369，联系人是赵六，电话18454432036，球衣是粉色，球裤是黑色，守门员服装是绿色"
        
        # 1. 模拟AI提取的原始结果（有空字段）
        raw_extracted_info = {
            "basic_info": {
                "team_name": "天依369",
                "contact_person": "赵六",
                "contact_phone": "18454432036",
                "leader_name": "",  # AI提取时为空
                "team_doctor": ""   # AI提取时为空
            },
            "kit_colors": {
                "jersey_color": "粉色",
                "shorts_color": "黑色",
                "socks_color": "",  # AI提取时为空
                "goalkeeper_kit_color": "绿色"
            },
            "additional_info": {
                "coach_name": "",   # AI提取时为空
                "notes": "新建球队档案"
            }
        }
        
        print(f"📄 AI提取的原始数据（有空字段）:")
        print(f"   联系人: '{raw_extracted_info['basic_info']['contact_person']}'")
        print(f"   领队: '{raw_extracted_info['basic_info']['leader_name']}' {'❌ 空' if not raw_extracted_info['basic_info']['leader_name'] else '✅'}")
        print(f"   教练: '{raw_extracted_info['additional_info']['coach_name']}' {'❌ 空' if not raw_extracted_info['additional_info']['coach_name'] else '✅'}")
        print(f"   队医: '{raw_extracted_info['basic_info']['team_doctor']}' {'❌ 空' if not raw_extracted_info['basic_info']['team_doctor'] else '✅'}")
        print(f"   球袜颜色: '{raw_extracted_info['kit_colors']['socks_color']}' {'❌ 空' if not raw_extracted_info['kit_colors']['socks_color'] else '✅'}")
        
        # 2. 应用智能填充逻辑（模拟修复后的ai_chat.py逻辑）
        from components.ai_chat import AIChatComponent
        
        # 创建AI聊天组件实例
        ai_chat = AIChatComponent()
        
        # 应用智能填充
        enhanced_extracted_info = ai_chat._apply_smart_auto_fill(raw_extracted_info)
        
        print(f"\n📄 应用智能填充后的数据:")
        basic_info = enhanced_extracted_info['basic_info']
        kit_colors = enhanced_extracted_info['kit_colors']
        additional_info = enhanced_extracted_info['additional_info']
        
        print(f"   联系人: '{basic_info['contact_person']}'")
        print(f"   领队: '{basic_info['leader_name']}' {'✅ 已填充' if basic_info['leader_name'] else '❌ 仍为空'}")
        print(f"   教练: '{additional_info['coach_name']}' {'✅ 已填充' if additional_info['coach_name'] else '❌ 仍为空'}")
        print(f"   队医: '{basic_info['team_doctor']}' {'✅ 已填充' if basic_info['team_doctor'] else '❌ 仍为空'}")
        print(f"   球袜颜色: '{kit_colors['socks_color']}' {'✅ 已填充' if kit_colors['socks_color'] else '❌ 仍为空'}")
        
        # 3. 模拟保存到文件
        team_name = "天依369_test"
        user_id = "test_user"
        
        ai_data_folder = os.path.join('data', user_id, 'enhanced_ai_data')
        os.makedirs(ai_data_folder, exist_ok=True)
        
        ai_data_file = os.path.join(ai_data_folder, f'{team_name}_ai_data.json')
        
        ai_data = {
            'team_name': team_name,
            'extracted_info': enhanced_extracted_info,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'data_source': 'ai_chat'
        }
        
        with open(ai_data_file, 'w', encoding='utf-8') as f:
            json.dump(ai_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 已保存增强数据到: {ai_data_file}")
        
        # 检查是否所有字段都已填充
        empty_fields = []
        all_fields = {**basic_info, **kit_colors, **additional_info}
        
        for key, value in all_fields.items():
            if not value or value == "":
                empty_fields.append(key)
        
        if empty_fields:
            print(f"\n❌ 仍有空字段: {empty_fields}")
            return False
        else:
            print(f"\n✅ 所有字段都已填充!")
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_word_generation_with_enhanced_data():
    """使用增强数据测试Word生成"""
    print(f"\n" + "=" * 60)
    print("🧪 使用增强数据测试Word生成")
    print("=" * 60)
    
    try:
        # 读取刚才保存的增强数据
        team_name = "天依369_test"
        user_id = "test_user"
        
        ai_data_file = os.path.join('data', user_id, 'enhanced_ai_data', f'{team_name}_ai_data.json')
        
        if not os.path.exists(ai_data_file):
            print(f"❌ 增强数据文件不存在: {ai_data_file}")
            return False
        
        with open(ai_data_file, 'r', encoding='utf-8') as f:
            ai_data = json.load(f)
        
        extracted_info = ai_data.get("extracted_info", {})
        basic_info = extracted_info.get("basic_info", {})
        kit_colors = extracted_info.get("kit_colors", {})
        additional_info = extracted_info.get("additional_info", {})
        
        # 准备Word生成数据
        team_data = {
            "name": basic_info.get("team_name", team_name),
            "contact_person": basic_info.get("contact_person", ""),
            "contact_phone": basic_info.get("contact_phone", ""),
            "leader": basic_info.get("leader_name", ""),
            "coach": additional_info.get("coach_name", ""),
            "doctor": basic_info.get("team_doctor", ""),
            "jersey_color": kit_colors.get("jersey_color", ""),
            "shorts_color": kit_colors.get("shorts_color", ""),
            "socks_color": kit_colors.get("socks_color", ""),
            "goalkeeper_kit_color": kit_colors.get("goalkeeper_kit_color", "")
        }
        
        players_data = [
            {"name": "张三", "jersey_number": "1", "photo": ""}
        ]
        
        print(f"📄 Word生成数据:")
        for key, value in team_data.items():
            status = "✅ 有值" if value else "❌ 空"
            print(f"   {key}: '{value}' {status}")
        
        # 生成Word文档
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        paths = app_settings.word_generator.get_absolute_paths("complete_workflow_test", app_settings.paths)
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            output_file = result['file_path']
            print(f"\n✅ Word生成成功: {os.path.basename(output_file)}")
            
            # 分析生成的文档
            with zipfile.ZipFile(output_file, 'r') as zip_file:
                with zip_file.open('word/document.xml') as xml_file:
                    content = xml_file.read().decode('utf-8')
            
            # 检查关键数据
            keywords = ["天依369", "赵六", "粉色", "黑色", "绿色"]
            found_count = 0
            
            for keyword in keywords:
                if keyword in content:
                    found_count += 1
                    print(f"   ✅ 找到: '{keyword}'")
                else:
                    print(f"   ❌ 未找到: '{keyword}'")
            
            # 统计关键信息出现次数
            zhao_liu_count = content.count("赵六")
            pink_count = content.count("粉色")
            black_count = content.count("黑色")
            green_count = content.count("绿色")
            
            print(f"\n📊 关键信息统计:")
            print(f"   '赵六'出现次数: {zhao_liu_count} (应该≥3)")
            print(f"   '粉色'出现次数: {pink_count} (应该≥2)")
            print(f"   '黑色'出现次数: {black_count} (应该≥1)")
            print(f"   '绿色'出现次数: {green_count} (应该≥1)")
            
            success = (zhao_liu_count >= 3 and pink_count >= 1 and 
                      black_count >= 1 and green_count >= 1)
            
            return success
        else:
            print(f"\n❌ Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 测试完整工作流修复")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 测试AI提取和智能填充
        smart_fill_success = simulate_ai_extraction_with_smart_fill()
        
        # 2. 测试Word生成
        word_success = test_word_generation_with_enhanced_data()
        
        print("\n" + "=" * 60)
        print("📋 完整工作流测试总结")
        print("=" * 60)
        
        if smart_fill_success and word_success:
            print("🎉 完整工作流修复成功！")
            print("✅ AI提取和智能填充正常工作")
            print("✅ Word生成测试通过")
            print("✅ 所有空字段问题已在源头解决")
            print("\n💡 现在整个系统应该能自动处理空字段了！")
            print("💡 用户在AI聊天后，数据会自动智能填充，Word文档也会完整显示！")
        else:
            print("❌ 完整工作流修复失败！")
            if smart_fill_success:
                print("✅ AI提取和智能填充正常")
            else:
                print("❌ AI提取和智能填充失败")
            
            if word_success:
                print("✅ Word生成测试通过")
            else:
                print("❌ Word生成测试失败")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
