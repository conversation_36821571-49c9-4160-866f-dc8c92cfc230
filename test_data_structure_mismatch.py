#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据结构不匹配问题
Test Data Structure Mismatch Issue
"""

import os

def analyze_extraction_vs_display():
    """分析提取函数与显示函数的数据结构差异"""
    print("🔍 分析提取函数与显示函数的数据结构差异")
    print("=" * 80)
    
    # 从代码分析发现的关键信息
    analysis = {
        "提取函数保存的数据": {
            "session_state键": "st.session_state.extracted_team_info = extracted_info",
            "数据来源": "self.ai_service.extract_team_info()",
            "可能的数据格式": "直接的extracted_info结构"
        },
        
        "显示函数期望的数据": {
            "读取路径": "team_info.get('ai_extracted_info', {}).get('basic_info', {})",
            "期望结构": {
                "ai_extracted_info": {
                    "basic_info": {
                        "team_name": "值",
                        "contact_person": "值"
                    }
                }
            }
        },
        
        "session_state键名差异": {
            "提取函数保存": "extracted_team_info",
            "显示函数读取": "team_info_{team_name}",
            "问题": "键名完全不匹配！"
        },
        
        "数据结构差异": {
            "提取函数输出": "可能是 {basic_info: {...}, kit_colors: {...}}",
            "显示函数期望": "需要 {ai_extracted_info: {basic_info: {...}}}",
            "问题": "数据结构层级不匹配！"
        }
    }
    
    for category, details in analysis.items():
        print(f"\n🎯 {category}")
        if isinstance(details, dict):
            for key, value in details.items():
                if isinstance(value, dict):
                    print(f"   {key}:")
                    for subkey, subvalue in value.items():
                        print(f"      {subkey}: {subvalue}")
                else:
                    print(f"   {key}: {value}")
    
    return analysis

def check_session_state_key_mismatch():
    """检查session_state键名不匹配问题"""
    print(f"\n🔍 检查session_state键名不匹配问题")
    print("=" * 80)
    
    ai_chat_file = "streamlit_team_management_modular/components/ai_chat.py"
    
    if os.path.exists(ai_chat_file):
        try:
            with open(ai_chat_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找提取函数中的保存逻辑
            extraction_save_lines = []
            display_read_lines = []
            
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                if 'extracted_team_info' in line and 'session_state' in line:
                    extraction_save_lines.append({
                        'line': i,
                        'content': line.strip()
                    })
                
                if 'team_info_key' in line or 'players_key' in line:
                    display_read_lines.append({
                        'line': i,
                        'content': line.strip()
                    })
            
            print("📋 提取函数的保存逻辑:")
            for line_info in extraction_save_lines:
                print(f"   第{line_info['line']}行: {line_info['content']}")
            
            print(f"\n📋 显示函数的读取逻辑:")
            for line_info in display_read_lines:
                print(f"   第{line_info['line']}行: {line_info['content']}")
            
            # 分析键名差异
            print(f"\n⚠️ 键名差异分析:")
            print("   提取函数保存: extracted_team_info (固定键名)")
            print("   显示函数读取: team_info_{team_name} (动态键名)")
            print("   🔴 问题: 键名完全不匹配，导致数据无法读取！")
            
            return {
                'extraction_save': extraction_save_lines,
                'display_read': display_read_lines
            }
            
        except Exception as e:
            print(f"❌ 检查失败: {e}")
            return {}
    else:
        print(f"❌ 文件不存在: {ai_chat_file}")
        return {}

def check_data_format_mismatch():
    """检查数据格式不匹配问题"""
    print(f"\n🔍 检查数据格式不匹配问题")
    print("=" * 80)
    
    format_analysis = {
        "AI服务可能返回的格式": [
            {
                "格式1": "直接结构",
                "示例": {
                    "basic_info": {
                        "team_name": "火狐999",
                        "contact_person": "赵六"
                    },
                    "kit_colors": {
                        "jersey_color": "粉色"
                    }
                }
            },
            {
                "格式2": "嵌套结构",
                "示例": {
                    "extracted_info": {
                        "basic_info": {"team_name": "火狐999"},
                        "kit_colors": {"jersey_color": "粉色"}
                    }
                }
            }
        ],
        
        "显示函数期望的格式": {
            "路径": "team_info.get('ai_extracted_info', {}).get('basic_info', {})",
            "期望": {
                "ai_extracted_info": {
                    "basic_info": {"team_name": "火狐999"},
                    "kit_colors": {"jersey_color": "粉色"}
                }
            }
        },
        
        "不匹配的原因": [
            "AI服务返回的键名可能是'extracted_info'而不是'ai_extracted_info'",
            "数据可能直接保存在根级别而不是嵌套结构",
            "键名映射可能不一致",
            "数据转换逻辑可能缺失"
        ]
    }
    
    for category, details in format_analysis.items():
        print(f"\n🎯 {category}")
        if isinstance(details, list):
            for item in details:
                if isinstance(item, dict):
                    for key, value in item.items():
                        print(f"   {key}: {value}")
                else:
                    print(f"   • {item}")
        elif isinstance(details, dict):
            for key, value in details.items():
                print(f"   {key}: {value}")
    
    return format_analysis

def find_ai_service_extract_function():
    """查找AI服务的提取函数"""
    print(f"\n🔍 查找AI服务的提取函数")
    print("=" * 80)
    
    # 查找AI服务相关文件
    ai_service_files = [
        "streamlit_team_management_modular/services/ai_service.py",
        "streamlit_team_management_modular/services/enhanced_ai_service.py",
        "streamlit_team_management_modular/services/enhanced_ai_assistant.py"
    ]
    
    extract_functions = {}
    
    for file_path in ai_service_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找extract_team_info函数
                if 'def extract_team_info(' in content:
                    # 提取函数定义
                    start_pos = content.find('def extract_team_info(')
                    if start_pos != -1:
                        lines = content[start_pos:].split('\n')
                        function_lines = []
                        indent_level = None
                        
                        for line in lines:
                            if line.strip() == "":
                                function_lines.append(line)
                                continue
                            
                            current_indent = len(line) - len(line.lstrip())
                            
                            if indent_level is None and line.strip().startswith("def "):
                                indent_level = current_indent
                                function_lines.append(line)
                            elif indent_level is not None:
                                if current_indent > indent_level or line.strip() == "":
                                    function_lines.append(line)
                                else:
                                    break
                        
                        extract_functions[file_path] = function_lines[:30]  # 只取前30行
                
            except Exception as e:
                print(f"❌ 读取文件失败 {file_path}: {e}")
    
    print("📋 找到的extract_team_info函数:")
    for file_path, function_lines in extract_functions.items():
        print(f"\n📄 {file_path}")
        for i, line in enumerate(function_lines[:15], 1):  # 只显示前15行
            if 'return' in line or 'extracted_info' in line or '{' in line:
                print(f"   第{i}行: {line}")
    
    return extract_functions

def generate_fix_strategy():
    """生成修复策略"""
    print(f"\n💡 生成修复策略")
    print("=" * 80)
    
    fix_strategies = {
        "问题根源": [
            "session_state键名不匹配: extracted_team_info vs team_info_{team_name}",
            "数据结构不匹配: 期望ai_extracted_info但可能是extracted_info",
            "数据层级不匹配: 期望嵌套结构但可能是平级结构"
        ],
        
        "修复方案": [
            {
                "方案1": "修改显示函数的数据读取逻辑",
                "具体": [
                    "从extracted_team_info读取数据而不是team_info_{team_name}",
                    "适配实际的数据结构路径",
                    "添加多种数据格式的兼容性"
                ],
                "优点": "不影响提取逻辑，只修改显示",
                "适用": "如果提取逻辑正确"
            },
            {
                "方案2": "修改提取函数的保存逻辑",
                "具体": [
                    "保存到team_info_{team_name}而不是extracted_team_info",
                    "调整数据格式为显示函数期望的结构",
                    "确保数据格式一致性"
                ],
                "优点": "保持显示逻辑不变",
                "适用": "如果显示逻辑设计合理"
            },
            {
                "方案3": "添加数据桥接逻辑",
                "具体": [
                    "在显示前将extracted_team_info转换为期望格式",
                    "复制数据到正确的session_state键",
                    "处理数据格式转换"
                ],
                "优点": "兼容性最好，不破坏现有逻辑",
                "适用": "作为临时或兼容性解决方案"
            }
        ],
        
        "推荐方案": {
            "选择": "方案1 - 修改显示函数的数据读取逻辑",
            "理由": [
                "提取逻辑可能已经正确工作",
                "只需要调整显示函数的数据读取路径",
                "影响范围最小，风险最低",
                "符合用户要求的只测试不修改主代码"
            ],
            "具体步骤": [
                "1. 检查extracted_team_info中的实际数据结构",
                "2. 修改显示函数从extracted_team_info读取",
                "3. 适配实际的数据结构路径",
                "4. 测试显示效果"
            ]
        }
    }
    
    for category, details in fix_strategies.items():
        print(f"\n💡 {category}")
        if isinstance(details, list):
            for item in details:
                if isinstance(item, dict):
                    for key, value in item.items():
                        if isinstance(value, list):
                            print(f"   {key}:")
                            for subitem in value:
                                print(f"      • {subitem}")
                        else:
                            print(f"   {key}: {value}")
                else:
                    print(f"   • {item}")
        elif isinstance(details, dict):
            for key, value in details.items():
                if isinstance(value, list):
                    print(f"   {key}:")
                    for item in value:
                        print(f"      • {item}")
                else:
                    print(f"   {key}: {value}")
    
    return fix_strategies

def main():
    """主函数"""
    print("🔍 数据结构不匹配问题深度分析")
    print("=" * 80)
    
    print("🎯 分析目标:")
    print("   找出AI信息收集成功但显示失败的根本原因")
    print("   分析数据结构和键名不匹配问题")
    print("   制定精确的修复策略")
    
    # 1. 分析提取与显示的差异
    extraction_vs_display = analyze_extraction_vs_display()
    
    # 2. 检查session_state键名不匹配
    key_mismatch = check_session_state_key_mismatch()
    
    # 3. 检查数据格式不匹配
    format_mismatch = check_data_format_mismatch()
    
    # 4. 查找AI服务提取函数
    ai_extract_functions = find_ai_service_extract_function()
    
    # 5. 生成修复策略
    fix_strategy = generate_fix_strategy()
    
    # 总结
    print(f"\n🎯 问题根源确认")
    print("=" * 80)
    
    print("🔴 确认的问题:")
    print("   1. session_state键名不匹配:")
    print("      提取保存: extracted_team_info")
    print("      显示读取: team_info_{team_name}")
    print("   2. 数据结构路径不匹配:")
    print("      显示期望: team_info.get('ai_extracted_info', {})")
    print("      实际可能: extracted_info 或直接结构")
    
    print(f"\n✅ 解决方案:")
    print("   🎯 修改显示函数的数据读取逻辑")
    print("   📊 从extracted_team_info读取数据")
    print("   🔧 适配实际的数据结构路径")
    print("   ✨ 保持提取逻辑不变")
    
    print(f"\n📋 下一步:")
    print("   1. 创建调试版本查看实际数据结构")
    print("   2. 根据实际结构调整显示函数")
    print("   3. 测试修复效果")
    print("   4. 确保不影响其他功能")

if __name__ == "__main__":
    main()
