#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前端日志输出问题
Test Frontend Log Output Issues
"""

import os
import sys
import re
from pathlib import Path

def find_streamlit_output_sources():
    """查找所有可能产生前端输出的代码"""
    print("🔍 查找前端日志输出源...")
    print("=" * 60)
    
    # 要搜索的目录
    search_dirs = [
        "streamlit_team_management_modular/services",
        "streamlit_team_management_modular/components", 
        "streamlit_team_management_modular/utils"
    ]
    
    # 要查找的输出模式
    output_patterns = [
        r'st\.info\(',
        r'st\.success\(',
        r'st\.error\(',
        r'st\.warning\(',
        r'st\.write\(',
        r'st\.text\(',
        r'st\.markdown\(',
        r'print\(',
        r'logging\.',
        r'logger\.',
    ]
    
    findings = {}
    
    for search_dir in search_dirs:
        if not os.path.exists(search_dir):
            continue
            
        print(f"\n📁 搜索目录: {search_dir}")
        
        for root, dirs, files in os.walk(search_dir):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    findings[file_path] = analyze_file_outputs(file_path, output_patterns)
    
    return findings

def analyze_file_outputs(file_path, patterns):
    """分析单个文件的输出语句"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        file_findings = []
        
        for i, line in enumerate(lines, 1):
            for pattern in patterns:
                matches = re.finditer(pattern, line)
                for match in matches:
                    # 提取完整的语句
                    statement = line.strip()
                    file_findings.append({
                        'line_number': i,
                        'pattern': pattern,
                        'statement': statement,
                        'context': get_line_context(lines, i-1, 2)
                    })
        
        return file_findings
        
    except Exception as e:
        return [{'error': str(e)}]

def get_line_context(lines, line_index, context_size):
    """获取行的上下文"""
    start = max(0, line_index - context_size)
    end = min(len(lines), line_index + context_size + 1)
    
    context_lines = []
    for i in range(start, end):
        marker = ">>> " if i == line_index else "    "
        context_lines.append(f"{marker}{i+1:3d}: {lines[i]}")
    
    return '\n'.join(context_lines)

def analyze_fashion_api_service():
    """专门分析fashion_api_service.py的输出"""
    print(f"\n🎯 专门分析 fashion_api_service.py...")
    print("=" * 60)
    
    file_path = "streamlit_team_management_modular/services/fashion_api_service.py"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # 查找所有st.输出语句
        st_outputs = []
        for i, line in enumerate(lines, 1):
            if 'st.' in line and any(method in line for method in ['info', 'success', 'error', 'warning', 'write']):
                st_outputs.append({
                    'line': i,
                    'content': line.strip(),
                    'method': extract_st_method(line),
                    'message': extract_st_message(line)
                })
        
        print(f"📊 找到 {len(st_outputs)} 个streamlit输出语句:")
        
        for output in st_outputs:
            print(f"\n📍 第{output['line']}行:")
            print(f"   方法: {output['method']}")
            print(f"   内容: {output['content']}")
            print(f"   消息: {output['message']}")
            
            # 分析是否是API相关的输出
            if is_api_related_output(output['message']):
                print(f"   🎯 这是API相关输出！")
        
        return st_outputs
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return []

def extract_st_method(line):
    """提取streamlit方法名"""
    match = re.search(r'st\.(\w+)', line)
    return match.group(1) if match else 'unknown'

def extract_st_message(line):
    """提取streamlit消息内容"""
    # 尝试提取引号内的内容
    patterns = [
        r'st\.\w+\(f?"([^"]*)"',
        r"st\.\w+\(f?'([^']*)'",
        r'st\.\w+\(f?"""([^"]*)"""',
        r"st\.\w+\(f?'''([^']*)'''",
    ]
    
    for pattern in patterns:
        match = re.search(pattern, line)
        if match:
            return match.group(1)
    
    return line.strip()

def is_api_related_output(message):
    """判断是否是API相关的输出"""
    api_keywords = [
        '任务ID', 'task_id', 'API', '响应', '状态码', 'status',
        '提交', '等待', '下载', '处理', '背景移除', '换装',
        '📤', '📡', '📋', '📥', '⏳', '📊', '✅', '❌'
    ]
    
    return any(keyword in message for keyword in api_keywords)

def find_specific_api_outputs():
    """查找特定的API输出语句"""
    print(f"\n🔍 查找特定API输出语句...")
    print("=" * 60)
    
    file_path = "streamlit_team_management_modular/services/fashion_api_service.py"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    # 从用户截图中看到的具体输出
    target_outputs = [
        "📤 提交背景移除任务",
        "📋 任务ID:",
        "⏳ 等待任务完成",
        "📊 任务状态:",
        "✅ 任务完成",
        "📥 下载处理结果",
        "✅ 背景移除完成",
        "📁 保存路径:",
        "API响应状态:",
        "API响应数据:"
    ]
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        found_outputs = []
        for target in target_outputs:
            for i, line in enumerate(lines, 1):
                if target in line:
                    found_outputs.append({
                        'target': target,
                        'line': i,
                        'content': line.strip(),
                        'context': get_line_context(lines, i-1, 3)
                    })
        
        print(f"📊 找到 {len(found_outputs)} 个目标输出:")
        
        for output in found_outputs:
            print(f"\n🎯 找到: {output['target']}")
            print(f"📍 第{output['line']}行: {output['content']}")
            print(f"📄 上下文:")
            print(output['context'])
            print("-" * 40)
        
        return found_outputs
        
    except Exception as e:
        print(f"❌ 查找失败: {e}")
        return []

def analyze_log_suppression_options():
    """分析日志抑制选项"""
    print(f"\n💡 日志抑制方案分析...")
    print("=" * 60)
    
    print("🔧 可能的解决方案:")
    print("1. 使用st.empty()容器控制显示")
    print("2. 添加verbose参数控制输出")
    print("3. 使用logging模块替代st.输出")
    print("4. 创建静默模式开关")
    print("5. 使用st.spinner()替代详细输出")
    
    print(f"\n📝 推荐方案:")
    print("方案1: 添加verbose参数")
    print("```python")
    print("def step2_remove_background(self, image_path: str, verbose: bool = False):")
    print("    if verbose:")
    print("        st.info('📤 提交背景移除任务...')")
    print("    # API调用逻辑")
    print("```")
    
    print(f"\n方案2: 使用st.spinner()")
    print("```python")
    print("with st.spinner('处理中...'):")
    print("    # API调用逻辑，不显示详细步骤")
    print("```")
    
    print(f"\n方案3: 使用st.empty()容器")
    print("```python")
    print("status_container = st.empty()")
    print("status_container.info('处理中...')")
    print("# 处理完成后清空")
    print("status_container.empty()")
    print("```")

def test_current_behavior():
    """测试当前行为"""
    print(f"\n🧪 测试当前输出行为...")
    print("=" * 60)
    
    # 模拟调用fashion_api_service
    print("模拟调用fashion_api_service.step2_remove_background():")
    print("预期会看到以下输出:")
    print("- 📤 提交背景移除任务...")
    print("- 📋 任务ID: xxxxx")
    print("- ⏳ 等待任务完成...")
    print("- 📊 任务状态: processing/succeeded")
    print("- 📥 下载处理结果...")
    print("- ✅ 背景移除完成！")
    print("- 📁 保存路径: xxxx")
    
    print(f"\n💡 这些输出都来自fashion_api_service.py中的st.info/st.success语句")

def main():
    """主函数"""
    print("🔧 前端日志输出问题分析")
    print("=" * 60)
    
    # 1. 查找所有输出源
    findings = find_streamlit_output_sources()
    
    # 2. 专门分析fashion_api_service
    api_outputs = analyze_fashion_api_service()
    
    # 3. 查找特定输出
    specific_outputs = find_specific_api_outputs()
    
    # 4. 分析解决方案
    analyze_log_suppression_options()
    
    # 5. 测试当前行为
    test_current_behavior()
    
    print(f"\n🎯 分析完成！")
    print(f"📊 总结:")
    print(f"- 主要输出来源: fashion_api_service.py")
    print(f"- 输出类型: st.info, st.success, st.error等")
    print(f"- 建议解决方案: 添加verbose参数或使用st.spinner")

if __name__ == "__main__":
    main()
