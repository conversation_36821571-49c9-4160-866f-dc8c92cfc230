#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试具体发现的问题
"""

import zipfile
import xml.etree.ElementTree as ET
import os
import json

def test_team_name_issue():
    """测试团队名称问题"""
    print("🔍 测试团队名称问题")
    print("=" * 60)
    
    try:
        import streamlit as st
        
        # 设置session state
        if 'user_id' not in st.session_state:
            st.session_state.user_id = 'user_44ecbeed9db2'
        
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 模拟正确的团队数据
        team_data = {
            'name': '天依003',
            'leader': '自动填充',
            'coach': '自动填充',
            'doctor': '自动填充',
            'contact_person': '赵六',
            'contact_phone': '18454432036'
        }
        
        players_data = [
            {
                'name': '张三',
                'jersey_number': '1',
                'photo': 'test.jpg'
            }
        ]
        
        print(f"📄 测试数据:")
        print(f"   团队名称: '{team_data['name']}'")
        print(f"   领队: '{team_data['leader']}'")
        print(f"   联系人: '{team_data['contact_person']}'")
        
        # 获取配置
        paths = app_settings.word_generator.get_absolute_paths("team_name_test", app_settings.paths)
        
        # 创建服务
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        # 检查数据准备
        json_data = word_service._prepare_json_data(team_data, players_data)
        team_info = json_data['teamInfo']
        
        print(f"\n📄 WordGeneratorService准备的数据:")
        print(f"   organizationName: '{team_info.get('organizationName', 'MISSING')}'")
        print(f"   teamLeader: '{team_info.get('teamLeader', 'MISSING')}'")
        print(f"   contactPerson: '{team_info.get('contactPerson', 'MISSING')}'")
        print(f"   contactPhone: '{team_info.get('contactPhone', 'MISSING')}'")
        print(f"   title: '{team_info.get('title', 'MISSING')}'")
        
        # 生成Word
        print(f"\n🚀 生成Word文档...")
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print(f"✅ 生成成功: {os.path.basename(result['file_path'])}")
            
            # 检查生成的文件内容
            return check_team_name_in_word(result['file_path'], team_data['name'])
        else:
            print(f"❌ 生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_team_name_in_word(file_path, expected_team_name):
    """检查Word中的团队名称"""
    print(f"\n🔍 检查Word中的团队名称")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    # 检查团队名称
                    has_team_name = expected_team_name in full_text
                    
                    print(f"📄 团队名称检查:")
                    print(f"   期望团队名称: '{expected_team_name}'")
                    print(f"   在Word中找到: {'✅ 是' if has_team_name else '❌ 否'}")
                    
                    # 显示单位名称区域的内容
                    print(f"\n📄 单位名称区域内容:")
                    words = full_text.split()
                    for i, word in enumerate(words):
                        if "单位名称" in word:
                            start = max(0, i-2)
                            end = min(len(words), i+10)
                            context = ' '.join(words[start:end])
                            print(f"   {context}")
                            break
                    
                    # 显示组织名称相关内容
                    print(f"\n📄 查找组织名称相关内容:")
                    organization_found = False
                    for i, word in enumerate(words):
                        if any(keyword in word for keyword in ['组织', '单位', '队名', expected_team_name]):
                            start = max(0, i-3)
                            end = min(len(words), i+8)
                            context = ' '.join(words[start:end])
                            print(f"   找到: {context}")
                            organization_found = True
                    
                    if not organization_found:
                        print(f"   ❌ 未找到组织名称相关内容")
                    
                    # 检查是否有organizationName占位符残留
                    has_org_placeholder = "{{organizationName}}" in content
                    print(f"\n📄 占位符检查:")
                    print(f"   {{{{organizationName}}}}占位符: {'⚠️ 仍存在' if has_org_placeholder else '✅ 已替换'}")
                    
                    return has_team_name
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return False
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def test_color_fields():
    """测试颜色字段"""
    print(f"\n🔍 测试颜色字段")
    print("=" * 60)
    
    try:
        import streamlit as st
        
        # 设置session state
        if 'user_id' not in st.session_state:
            st.session_state.user_id = 'user_44ecbeed9db2'
        
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 模拟包含颜色信息的团队数据
        team_data = {
            'name': '颜色测试队',
            'leader': '测试领队',
            'coach': '测试教练',
            'doctor': '测试队医',
            'contact_person': '测试联系人',
            'contact_phone': '13800138000',
            # 添加颜色字段
            'jersey_color': '红色',
            'shorts_color': '蓝色',
            'socks_color': '白色',
            'goalkeeper_kit_color': '黄色'
        }
        
        players_data = [
            {
                'name': '测试球员',
                'jersey_number': '1',
                'photo': 'test.jpg'
            }
        ]
        
        print(f"📄 测试颜色数据:")
        print(f"   球衣颜色: '{team_data.get('jersey_color', 'MISSING')}'")
        print(f"   球裤颜色: '{team_data.get('shorts_color', 'MISSING')}'")
        print(f"   球袜颜色: '{team_data.get('socks_color', 'MISSING')}'")
        print(f"   守门员服装颜色: '{team_data.get('goalkeeper_kit_color', 'MISSING')}'")
        
        # 获取配置
        paths = app_settings.word_generator.get_absolute_paths("color_test", app_settings.paths)
        
        # 创建服务
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        # 检查数据准备
        json_data = word_service._prepare_json_data(team_data, players_data)
        team_info = json_data['teamInfo']
        
        print(f"\n📄 WordGeneratorService准备的颜色数据:")
        print(f"   jerseyColor: '{team_info.get('jerseyColor', 'MISSING')}'")
        print(f"   shortsColor: '{team_info.get('shortsColor', 'MISSING')}'")
        print(f"   socksColor: '{team_info.get('socksColor', 'MISSING')}'")
        print(f"   goalkeeperKitColor: '{team_info.get('goalkeeperKitColor', 'MISSING')}'")
        
        # 生成Word
        print(f"\n🚀 生成Word文档...")
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print(f"✅ 生成成功: {os.path.basename(result['file_path'])}")
            
            # 检查生成的文件内容
            return check_colors_in_word(result['file_path'], team_data)
        else:
            print(f"❌ 生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def check_colors_in_word(file_path, team_data):
    """检查Word中的颜色信息"""
    print(f"\n🔍 检查Word中的颜色信息")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    # 检查各种颜色
                    colors = {
                        'jersey_color': team_data.get('jersey_color', ''),
                        'shorts_color': team_data.get('shorts_color', ''),
                        'socks_color': team_data.get('socks_color', ''),
                        'goalkeeper_kit_color': team_data.get('goalkeeper_kit_color', '')
                    }
                    
                    print(f"📄 颜色信息检查:")
                    color_results = {}
                    
                    for color_type, color_value in colors.items():
                        if color_value:
                            has_color = color_value in full_text
                            print(f"   {color_type} '{color_value}': {'✅ 找到' if has_color else '❌ 未找到'}")
                            color_results[color_type] = has_color
                        else:
                            print(f"   {color_type}: ❌ 数据中无此字段")
                            color_results[color_type] = False
                    
                    # 显示服装颜色区域的内容
                    print(f"\n📄 服装颜色区域内容:")
                    words = full_text.split()
                    for i, word in enumerate(words):
                        if any(keyword in word for keyword in ['服装', '颜色', '球衣', '球裤', '球袜']):
                            start = max(0, i-3)
                            end = min(len(words), i+15)
                            context = ' '.join(words[start:end])
                            print(f"   {context}")
                            break
                    
                    return color_results
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return {}
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return {}

def main():
    """主函数"""
    print("🎯 测试具体发现的问题")
    print("=" * 70)
    
    # 1. 测试团队名称问题
    team_name_result = test_team_name_issue()
    
    # 2. 测试颜色字段
    color_results = test_color_fields()
    
    # 综合分析
    print(f"\n📊 具体问题测试结果")
    print("=" * 70)
    
    print(f"🔍 团队名称问题:")
    if team_name_result:
        print(f"   ✅ 团队名称能够正确填入Word文档")
        print(f"   💡 之前的问题可能是数据传递问题")
    else:
        print(f"   ❌ 团队名称仍然无法正确填入")
        print(f"   💡 需要检查organizationName字段的映射")
    
    print(f"\n🔍 颜色字段问题:")
    if color_results:
        successful_colors = sum(1 for result in color_results.values() if result)
        total_colors = len(color_results)
        print(f"   颜色字段成功率: {successful_colors}/{total_colors}")
        
        for color_type, result in color_results.items():
            status = "✅ 正常" if result else "❌ 问题"
            print(f"   {color_type}: {status}")
        
        if successful_colors == 0:
            print(f"   💡 颜色字段可能需要在WordGeneratorService中添加映射")
    else:
        print(f"   ❌ 颜色字段测试失败")
    
    print(f"\n🎯 总结:")
    print(f"   基于联系人信息修复的成功经验")
    print(f"   发现的主要问题:")
    print(f"   1. 团队名称映射可能有问题")
    print(f"   2. 颜色字段可能缺少映射逻辑")
    print(f"   3. 需要检查WordGeneratorService的_prepare_json_data方法")

if __name__ == "__main__":
    main()
