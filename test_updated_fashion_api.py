#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的换装API服务
Test Updated Fashion API Service
"""

import os
import sys
import streamlit as st

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 模拟streamlit环境
class MockStreamlit:
    def info(self, msg):
        print(f"ℹ️ {msg}")
    
    def success(self, msg):
        print(f"✅ {msg}")
    
    def error(self, msg):
        print(f"❌ {msg}")
    
    def warning(self, msg):
        print(f"⚠️ {msg}")

# 替换streamlit模块
sys.modules['streamlit'] = MockStreamlit()
st = MockStreamlit()

def test_updated_background_removal():
    """测试更新后的背景移除功能"""
    print("🧪 测试更新后的背景移除API")
    print("=" * 60)
    
    try:
        # 添加正确的路径
        sys.path.insert(0, os.path.join(current_dir, 'streamlit_team_management_modular'))

        # 导入更新后的服务
        from services.fashion_api_service import FashionAPIService
        
        # 创建服务实例
        service = FashionAPIService()
        
        print(f"🔧 API配置:")
        print(f"   - 基础URL: {service.base_url}")
        print(f"   - API密钥: {service.api_key[:20]}...")
        print(f"   - 临时目录: {service.temp_dir}")
        
        # 查找测试图片
        test_images = [
            "streamlit_team_management_modular/photos/player1_cropped.png",
            "streamlit_team_management_modular/photos/player2_cropped.jpg", 
            "streamlit_team_management_modular/photos/player3_cropped.jpg",
            "streamlit_team_management_modular/data/user_a13da2c47ed7/uploads/测试AI感知/8b4bfbb0be374765a2884b8771dc1232.jpg"
        ]
        
        test_image = None
        for img_path in test_images:
            if os.path.exists(img_path):
                test_image = img_path
                break
        
        if not test_image:
            print("❌ 未找到测试图片")
            print("📁 请确保以下路径之一存在测试图片:")
            for path in test_images:
                print(f"   - {path}")
            return
        
        print(f"\n📸 使用测试图片: {test_image}")
        
        # 测试背景移除
        print(f"\n🎯 测试302.ai V1背景移除...")
        result = service.step2_remove_background(test_image)
        
        if result:
            print(f"✅ 背景移除成功！")
            print(f"📁 输出文件: {result}")
            
            # 检查文件是否存在
            if os.path.exists(result):
                file_size = os.path.getsize(result)
                print(f"📊 文件大小: {file_size/1024:.1f}KB")
                
                # 测试白底合成
                print(f"\n🎯 测试白底合成...")
                white_bg_result = service.step3_add_white_background(result)
                
                if white_bg_result:
                    print(f"✅ 白底合成成功！")
                    print(f"📁 输出文件: {white_bg_result}")
                    
                    if os.path.exists(white_bg_result):
                        white_file_size = os.path.getsize(white_bg_result)
                        print(f"📊 文件大小: {white_file_size/1024:.1f}KB")
                else:
                    print(f"❌ 白底合成失败")
            else:
                print(f"❌ 输出文件不存在: {result}")
        else:
            print(f"❌ 背景移除失败")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def test_complete_workflow():
    """测试完整的换装工作流"""
    print(f"\n🎯 测试完整换装工作流...")
    print("=" * 60)
    
    try:
        from services.fashion_api_service import FashionAPIService
        
        service = FashionAPIService()
        
        # 查找测试图片
        test_images = [
            "streamlit_team_management_modular/photos/player1_cropped.png",
            "streamlit_team_management_modular/photos/player2_cropped.jpg"
        ]
        
        clothes_images = [
            "streamlit_team_management_modular/assets/default_clothes/jersey1.png",
            "streamlit_team_management_modular/assets/default_clothes/jersey2.jpg"
        ]
        
        test_image = None
        for img_path in test_images:
            if os.path.exists(img_path):
                test_image = img_path
                break
        
        clothes_image = None
        for img_path in clothes_images:
            if os.path.exists(img_path):
                clothes_image = img_path
                break
        
        if not test_image:
            print("❌ 未找到测试人物图片")
            return
        
        if not clothes_image:
            print("❌ 未找到测试服装图片")
            print("💡 跳过完整工作流测试，仅测试背景移除")
            return
        
        print(f"📸 人物图片: {test_image}")
        print(f"👕 服装图片: {clothes_image}")
        
        # 测试完整工作流
        result = service.process_single_complete_workflow(test_image, clothes_image)
        
        if result.get("success"):
            print(f"✅ 完整工作流成功！")
            print(f"📁 最终结果: {result.get('final_result')}")
            print(f"⏱️ 总耗时: {result.get('total_time', 0):.1f}秒")
        else:
            print(f"❌ 完整工作流失败: {result.get('error')}")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def main():
    """主函数"""
    print("🔧 测试更新后的换装API服务")
    print("=" * 60)
    
    # 测试背景移除
    test_updated_background_removal()
    
    # 测试完整工作流
    test_complete_workflow()
    
    print(f"\n🎯 测试完成！")
    print(f"\n💡 总结:")
    print(f"1. 已将Clipdrop API替换为302.ai V1 API")
    print(f"2. 使用Authorization: Bearer认证方式")
    print(f"3. 支持异步任务处理和状态查询")
    print(f"4. 成本明确：0.01 PTC/次")

if __name__ == "__main__":
    main()
