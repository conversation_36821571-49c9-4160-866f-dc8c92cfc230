#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分析主项目的自动Word生成功能
Test and Analyze Main Project's Automatic Word Generation Feature
"""

import os
import re
from pathlib import Path

def search_automatic_word_generation():
    """搜索主项目中的自动Word生成功能"""
    print("🔍 搜索主项目的自动Word生成功能")
    print("=" * 80)
    
    # 搜索自动Word生成相关关键词
    auto_word_keywords = [
        "自动生成word",
        "自动生成Word",
        "auto.*word",
        "automatic.*word",
        "workflow.*word",
        "完成.*word",
        "生成.*word.*报名表",
        "word.*自动",
        "批量.*word",
        "一键.*word"
    ]
    
    search_results = {}
    
    # 搜索整个项目
    for root, dirs, files in os.walk("streamlit_team_management_modular"):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    for keyword in auto_word_keywords:
                        matches = re.finditer(keyword, content, re.IGNORECASE)
                        for match in matches:
                            if file_path not in search_results:
                                search_results[file_path] = []
                            
                            # 找到包含关键词的行
                            lines = content.split('\n')
                            for i, line in enumerate(lines, 1):
                                if keyword.lower() in line.lower() or re.search(keyword, line, re.IGNORECASE):
                                    search_results[file_path].append({
                                        'line': i,
                                        'keyword': keyword,
                                        'content': line.strip()
                                    })
                                    break
                                    
                except Exception as e:
                    continue
    
    print("📁 找到自动Word生成功能的文件:")
    for file_path, findings in search_results.items():
        print(f"\n📄 {file_path}")
        unique_findings = {}
        for finding in findings:
            key = f"{finding['line']}_{finding['content']}"
            if key not in unique_findings:
                unique_findings[key] = finding
        
        for finding in list(unique_findings.values())[:3]:  # 只显示前3个
            print(f"   第{finding['line']}行: {finding['content'][:80]}...")
    
    return search_results

def analyze_fashion_workflow_word_generation():
    """分析时尚工作流中的Word生成"""
    print(f"\n🔍 分析时尚工作流中的Word生成")
    print("=" * 80)
    
    fashion_files = [
        "streamlit_team_management_modular/services/fashion_workflow_service.py",
        "streamlit_team_management_modular/components/fashion_workflow.py",
        "streamlit_team_management_modular/backup_before_fix/fashion_workflow_service.py"
    ]
    
    workflow_word_analysis = {}
    
    for file_path in fashion_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 搜索Word生成相关代码
                word_patterns = [
                    r'word.*生成',
                    r'generate.*word',
                    r'word.*report',
                    r'报名表.*生成',
                    r'自动.*word',
                    r'完成.*word'
                ]
                
                word_functions = []
                lines = content.split('\n')
                
                for i, line in enumerate(lines, 1):
                    for pattern in word_patterns:
                        if re.search(pattern, line, re.IGNORECASE):
                            word_functions.append({
                                'line': i,
                                'content': line.strip(),
                                'context': lines[max(0, i-2):min(len(lines), i+3)]  # 上下文
                            })
                
                workflow_word_analysis[file_path] = {
                    'word_functions': word_functions,
                    'file_size': len(lines)
                }
                
                print(f"\n📄 {file_path}")
                print(f"   文件行数: {len(lines)}")
                print(f"   Word生成相关代码: {len(word_functions)} 处")
                
                for func in word_functions[:3]:  # 只显示前3个
                    print(f"   第{func['line']}行: {func['content'][:80]}...")
                
            except Exception as e:
                print(f"   ❌ 读取失败: {file_path} - {e}")
    
    return workflow_word_analysis

def compare_standalone_vs_automatic_word():
    """对比独立Word生成vs自动Word生成"""
    print(f"\n🔄 对比独立Word生成 vs 自动Word生成")
    print("=" * 80)
    
    comparison = {
        "独立Word生成界面": {
            "文件": "streamlit_team_management_modular/components/word_generator.py",
            "触发方式": "用户手动点击'生成Word报名表'按钮",
            "使用场景": "用户主动需要生成报名表时",
            "集成程度": "独立功能模块",
            "用户体验": "需要用户主动操作"
        },
        
        "自动Word生成": {
            "文件": "可能在fashion_workflow_service.py等工作流文件中",
            "触发方式": "工作流完成后自动触发",
            "使用场景": "AI处理完成后自动生成",
            "集成程度": "深度集成到工作流中",
            "用户体验": "无需用户干预，自动完成"
        }
    }
    
    for generation_type, details in comparison.items():
        print(f"\n🔄 {generation_type}")
        for key, value in details.items():
            print(f"   {key}: {value}")
    
    # 检查独立界面是否在主导航中
    app_file = "streamlit_team_management_modular/app.py"
    if os.path.exists(app_file):
        try:
            with open(app_file, 'r', encoding='utf-8') as f:
                app_content = f.read()
            
            # 检查是否有Word生成页面的导航
            word_nav_patterns = [
                r'Word.*生成',
                r'word.*generator',
                r'报名表.*生成'
            ]
            
            has_word_nav = False
            for pattern in word_nav_patterns:
                if re.search(pattern, app_content, re.IGNORECASE):
                    has_word_nav = True
                    break
            
            print(f"\n📱 主应用导航检查:")
            print(f"   独立Word生成页面在主导航中: {'是' if has_word_nav else '否'}")
            
        except Exception as e:
            print(f"   ❌ 检查主应用失败: {e}")
    
    return comparison

def analyze_word_generation_redundancy():
    """分析Word生成功能的冗余性"""
    print(f"\n⚠️ 分析Word生成功能的冗余性")
    print("=" * 80)
    
    # 搜索所有Word生成相关的函数和类
    word_generation_components = {}
    
    for root, dirs, files in os.walk("streamlit_team_management_modular"):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 搜索Word生成相关的函数和类
                    word_functions = re.findall(r'def\s+(\w*word\w*|\w*generate\w*|\w*report\w*)\s*\(', content, re.IGNORECASE)
                    word_classes = re.findall(r'class\s+(\w*Word\w*|\w*Generate\w*|\w*Report\w*)', content, re.IGNORECASE)
                    
                    if word_functions or word_classes:
                        word_generation_components[file_path] = {
                            'functions': word_functions,
                            'classes': word_classes
                        }
                        
                except Exception as e:
                    continue
    
    print("📊 Word生成相关组件统计:")
    total_functions = 0
    total_classes = 0
    
    for file_path, components in word_generation_components.items():
        functions = components['functions']
        classes = components['classes']
        total_functions += len(functions)
        total_classes += len(classes)
        
        if functions or classes:
            print(f"\n📄 {file_path}")
            if functions:
                print(f"   函数: {functions}")
            if classes:
                print(f"   类: {classes}")
    
    print(f"\n📊 总计:")
    print(f"   Word生成相关文件: {len(word_generation_components)}")
    print(f"   Word生成相关函数: {total_functions}")
    print(f"   Word生成相关类: {total_classes}")
    
    # 分析冗余性
    redundancy_analysis = {
        "可能的冗余": [],
        "功能重复": [],
        "建议": []
    }
    
    if total_functions > 5:
        redundancy_analysis["可能的冗余"].append(f"发现{total_functions}个Word生成相关函数，可能存在功能重复")
    
    if len(word_generation_components) > 3:
        redundancy_analysis["可能的冗余"].append(f"Word生成功能分散在{len(word_generation_components)}个文件中，可能需要整合")
    
    # 检查是否有独立组件和集成组件同时存在
    has_standalone = any('word_generator.py' in path for path in word_generation_components.keys())
    has_workflow = any('workflow' in path for path in word_generation_components.keys())
    
    if has_standalone and has_workflow:
        redundancy_analysis["功能重复"].append("同时存在独立Word生成组件和工作流集成的Word生成")
        redundancy_analysis["建议"].append("考虑移除独立Word生成界面，统一使用自动生成")
    
    print(f"\n⚠️ 冗余性分析:")
    for category, items in redundancy_analysis.items():
        if items:
            print(f"   {category}:")
            for item in items:
                print(f"      • {item}")
    
    return redundancy_analysis

def find_main_word_generation_workflow():
    """找出主要的Word生成工作流"""
    print(f"\n🎯 找出主要的Word生成工作流")
    print("=" * 80)
    
    # 重点分析可能包含主要工作流的文件
    main_workflow_files = [
        "streamlit_team_management_modular/services/fashion_workflow_service.py",
        "streamlit_team_management_modular/components/ai_chat.py",
        "streamlit_team_management_modular/components/fashion_workflow.py"
    ]
    
    main_workflow_analysis = {}
    
    for file_path in main_workflow_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 搜索自动Word生成的触发点
                auto_triggers = []
                lines = content.split('\n')
                
                for i, line in enumerate(lines, 1):
                    # 搜索可能的自动触发模式
                    trigger_patterns = [
                        r'完成.*word',
                        r'成功.*word',
                        r'自动.*word',
                        r'批量.*完成.*word',
                        r'处理.*完成.*word',
                        r'workflow.*complete.*word'
                    ]
                    
                    for pattern in trigger_patterns:
                        if re.search(pattern, line, re.IGNORECASE):
                            auto_triggers.append({
                                'line': i,
                                'content': line.strip(),
                                'pattern': pattern
                            })
                
                main_workflow_analysis[file_path] = {
                    'auto_triggers': auto_triggers,
                    'file_size': len(lines)
                }
                
                print(f"\n📄 {file_path}")
                print(f"   自动Word生成触发点: {len(auto_triggers)} 个")
                
                for trigger in auto_triggers[:3]:  # 只显示前3个
                    print(f"   第{trigger['line']}行: {trigger['content'][:80]}...")
                
            except Exception as e:
                print(f"   ❌ 分析失败: {file_path} - {e}")
    
    return main_workflow_analysis

def main():
    """主函数"""
    print("🔍 主项目自动Word生成功能分析")
    print("=" * 80)
    
    print("🎯 分析目标:")
    print("   找出主项目的自动Word生成功能")
    print("   分析独立Word生成界面与自动生成的关系")
    print("   确定哪个功能是真正使用的")
    print("   理解为什么独立界面'没有用'")
    
    # 1. 搜索自动Word生成功能
    auto_word_results = search_automatic_word_generation()
    
    # 2. 分析时尚工作流中的Word生成
    workflow_analysis = analyze_fashion_workflow_word_generation()
    
    # 3. 对比独立vs自动Word生成
    comparison = compare_standalone_vs_automatic_word()
    
    # 4. 分析Word生成功能的冗余性
    redundancy = analyze_word_generation_redundancy()
    
    # 5. 找出主要的Word生成工作流
    main_workflow = find_main_word_generation_workflow()
    
    # 总结分析
    print(f"\n🎯 分析总结")
    print("=" * 80)
    
    print("✅ 主要发现:")
    print(f"   🔍 发现多个Word生成相关文件")
    print(f"   🔄 存在独立界面和自动生成两套逻辑")
    print(f"   ⚠️ 可能存在功能冗余")
    
    print(f"\n🎯 用户反馈验证:")
    print("   用户说'独立界面没有用'的可能原因:")
    print("   1. 主项目已实现自动Word生成")
    print("   2. 用户使用的是AI工作流，完成后自动生成Word")
    print("   3. 独立界面成为冗余功能")
    print("   4. 自动生成更符合用户的使用习惯")
    
    print(f"\n💡 建议:")
    print("   1. 确认主要使用的是自动Word生成功能")
    print("   2. 考虑移除或隐藏独立Word生成界面")
    print("   3. 统一Word生成逻辑，避免功能重复")
    print("   4. 优化自动生成的用户体验")

if __name__ == "__main__":
    main()
